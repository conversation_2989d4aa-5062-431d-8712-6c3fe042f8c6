"use client";

import { Metric<PERSON><PERSON> } from "@/components/dashboard/metric-card";
import { RecentTenants } from "@/components/dashboard/recent-tenants";
import { EnhancedChart } from "@/components/dashboard/enhanced-chart";
import { formatCurrency, formatNumber } from "@/lib/utils";
import {
  Building,
  CreditCard,
  HardDrive,
  Users,
  RefreshCw,
} from "lucide-react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";

export default function DashboardPage() {
  // State for tenant metrics
  const [tenantMetrics, setTenantMetrics] = useState({
    totalActiveTenants: 0,
    totalTenants: 0,
    newTenants: 0,
    growthRate: 0,
  });

  // State for user metrics
  const [userMetrics, setUserMetrics] = useState({
    activeUserCount: 0,
    totalUsers: 0,
    activeUserPercentage: 0,
  });

  // State for revenue metrics
  const [revenueMetrics, setRevenueMetrics] = useState({
    mrr: 0,
    arr: 0,
    activeSubscriptions: 0,
  });

  // State for storage metrics
  const [storageMetrics, setStorageMetrics] = useState({
    totalStorageGB: 0,
    storageGrowthRate: 0,
  });

  // State for token usage data
  const [tokenUsageData, setTokenUsageData] = useState<
    Array<{ name: string; value: number }>
  >([]);

  // State for storage usage data
  const [storageUsageData, setStorageUsageData] = useState<
    Array<{ name: string; value: number }>
  >([]);

  // State for recent tenants
  const [recentTenants, setRecentTenants] = useState<
    Array<{
      id: string;
      name: string;
      plan: string;
      createdAt: string;
      hasActiveSubscription: boolean;
      status: "active" | "inactive" | "trial";
    }>
  >([]);

  // State for loading
  const [loading, setLoading] = useState(true);

  // State for period selection
  const [period, setPeriod] = useState("30days");

  // Fetch data when component mounts or when period changes
  useEffect(() => {
    async function fetchData() {
      await fetchDashboardData();
    }
    fetchData();
  }, [period]);

  async function fetchDashboardData() {
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();

      // Add period parameter
      params.append("period", period);

      console.log("Fetching dashboard data with params:", params.toString());

      // Fetch overview data (tenants, users, revenue, storage)
      const overviewResponse = await fetch(
        `/api/dashboard/overview?${params.toString()}`
      );

      console.log("Overview response status:", overviewResponse.status);

      if (!overviewResponse.ok) {
        const errorText = await overviewResponse.text();
        console.error("Overview API error:", errorText);
        throw new Error(
          `Overview API error: ${overviewResponse.status} - ${errorText}`
        );
      }

      const overviewData = await overviewResponse.json();
      console.log("Overview data:", overviewData);

      // Update tenant metrics with fallback values
      setTenantMetrics({
        totalActiveTenants: overviewData.tenantMetrics?.activeTenants || 0,
        totalTenants: overviewData.tenantMetrics?.totalTenants || 0,
        newTenants: overviewData.tenantMetrics?.newTenants || 0,
        growthRate: overviewData.tenantMetrics?.growthRate || 0,
      });

      // Update user metrics with fallback values
      setUserMetrics({
        activeUserCount: overviewData.userMetrics?.activeUsers || 0,
        totalUsers: overviewData.userMetrics?.totalUsers || 0,
        activeUserPercentage:
          overviewData.userMetrics?.activeUserPercentage || 0,
      });

      // Update revenue metrics with fallback values
      setRevenueMetrics({
        mrr: overviewData.revenueMetrics?.mrr || 0,
        arr: overviewData.revenueMetrics?.arr || 0,
        activeSubscriptions:
          overviewData.revenueMetrics?.activeSubscriptions || 0,
      });

      // Update storage metrics with fallback values
      setStorageMetrics({
        totalStorageGB: overviewData.storageMetrics?.totalStorageGB || 0,
        storageGrowthRate: overviewData.storageMetrics?.growthRate || 0,
      });

      console.log("Fetching usage data...");
      // Fetch usage data (tokens, storage, API)
      try {
        const usageResponse = await fetch(
          `/api/dashboard/usage?${params.toString()}`
        );

        console.log("Usage response status:", usageResponse.status);

        if (!usageResponse.ok) {
          const errorText = await usageResponse.text();
          console.error("Usage API error:", errorText);
          throw new Error(
            `Usage API error: ${usageResponse.status} - ${errorText}`
          );
        }

        const usageData = await usageResponse.json();
        console.log("Usage data:", usageData);

        // Update token usage data
        setTokenUsageData(usageData.tokenUsage || []);

        // Update storage usage data
        setStorageUsageData(usageData.storageUsage || []);
      } catch (usageError) {
        console.error("Error fetching usage data:", usageError);
      }

      console.log("Fetching tenant data...");
      // Fetch tenant data
      try {
        const activityResponse = await fetch(
          `/api/dashboard/activity?${params.toString()}`
        );

        console.log("Activity response status:", activityResponse.status);

        if (!activityResponse.ok) {
          const errorText = await activityResponse.text();
          console.error("Activity API error:", errorText);
          throw new Error(
            `Activity API error: ${activityResponse.status} - ${errorText}`
          );
        }

        const activityData = await activityResponse.json();
        console.log("Activity data:", activityData);

        // Update recent tenants with proper type casting and error handling
        try {
          setRecentTenants(
            (activityData.recentTenants || []).map((tenant: any) => ({
              ...tenant,
              id:
                tenant.id ||
                `tenant-${Math.random().toString(36).substring(2, 9)}`,
              name: tenant.name || "Unknown Tenant",
              plan: tenant.plan || "No Plan",
              createdAt: tenant.createdAt || new Date().toISOString(),
              hasActiveSubscription: tenant.hasActiveSubscription || false,
              status:
                (tenant.status as "active" | "inactive" | "trial") ||
                "inactive",
            }))
          );
        } catch (tenantError) {
          console.error("Error processing tenant data:", tenantError);
          setRecentTenants([]);
        }
      } catch (activityError) {
        console.error("Error fetching tenant data:", activityError);
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setLoading(false);
    }
  }

  // This useEffect is replaced by the one above
  // Fallback data for charts if API returns empty
  const fallbackTokenData = [
    { name: "Jan", value: 0 },
    { name: "Feb", value: 0 },
    { name: "Mar", value: 0 },
    { name: "Apr", value: 0 },
    { name: "May", value: 0 },
    { name: "Jun", value: 0 },
    { name: "Jul", value: 0 },
  ];

  const fallbackStorageData = [
    { name: "Jan", value: 0 },
    { name: "Feb", value: 0 },
    { name: "Mar", value: 0 },
    { name: "Apr", value: 0 },
    { name: "May", value: 0 },
    { name: "Jun", value: 0 },
    { name: "Jul", value: 0 },
  ];
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of all tenants and system usage
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchDashboardData()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Tabs
            defaultValue={period}
            onValueChange={setPeriod}
            className="w-full"
          >
            <TabsList>
              <TabsTrigger value="7days">7 Days</TabsTrigger>
              <TabsTrigger value="30days">30 Days</TabsTrigger>
              <TabsTrigger value="90days">90 Days</TabsTrigger>
              <TabsTrigger value="6months">6 Months</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Link href="/metrics/tenants">
          <MetricCard
            title="Total Tenants"
            value={
              loading
                ? "Loading..."
                : tenantMetrics.totalActiveTenants.toString()
            }
            icon={Building}
            trend={
              loading
                ? undefined
                : {
                    value: tenantMetrics.growthRate,
                    isPositive: tenantMetrics.growthRate > 0,
                  }
            }
            color="indigo"
          />
        </Link>
        <Link href="/metrics/users">
          <MetricCard
            title="Total Users"
            value={
              loading ? "Loading..." : formatNumber(userMetrics.totalUsers)
            }
            icon={Users}
            trend={
              loading
                ? undefined
                : {
                    value: userMetrics.activeUserPercentage,
                    isPositive: userMetrics.activeUserPercentage > 50,
                  }
            }
            color="blue"
          />
        </Link>
        <Link href="/metrics/finance">
          <MetricCard
            title="Monthly Revenue"
            value={loading ? "Loading..." : formatCurrency(revenueMetrics.mrr)}
            icon={CreditCard}
            trend={
              loading
                ? undefined
                : {
                    value: 5, // This would be calculated from historical data
                    isPositive: true,
                  }
            }
            color="green"
          />
        </Link>
        <Link href="/metrics/system">
          <MetricCard
            title="Storage Used"
            value={
              loading ? "Loading..." : `${storageMetrics.totalStorageGB} GB`
            }
            icon={HardDrive}
            trend={
              loading
                ? undefined
                : {
                    value: storageMetrics.storageGrowthRate,
                    isPositive: storageMetrics.storageGrowthRate > 0,
                  }
            }
            color="amber"
          />
        </Link>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <EnhancedChart
          title="Token Usage"
          data={tokenUsageData.length > 0 ? tokenUsageData : fallbackTokenData}
          dataKey="value"
          valueFormatter={(value) => formatNumber(value)}
          description="Total tokens used across all tenants"
          color="indigo"
          chartType="area"
          yAxisLabel="Tokens"
          showLegend={false}
        />
        <RecentTenants tenants={recentTenants} />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <EnhancedChart
          title="Storage Usage"
          data={
            storageUsageData.length > 0 ? storageUsageData : fallbackStorageData
          }
          dataKey="value"
          valueFormatter={(value) => `${value} GB`}
          description="Total storage used across all tenants"
          color="blue"
          chartType="bar"
          yAxisLabel="GB"
          showLegend={false}
        />
      </div>
    </div>
  );
}
