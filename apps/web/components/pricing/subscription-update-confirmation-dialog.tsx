"use client";

import { useState } from "react";
import { Loader2, Users, Database } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useLanguage } from "@/lib/language-context";

interface SubscriptionUpdateConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
  updateType: "users" | "storage";
  currentValue: number;
  newValue: number;
  loading?: boolean;
  priceChange?: number;
  isYearlyBilling?: boolean;
}

export function SubscriptionUpdateConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  updateType,
  currentValue,
  newValue,
  loading = false,
  priceChange = 0,
  isYearlyBilling = false,
}: SubscriptionUpdateConfirmationDialogProps) {
  const { t } = useLanguage();
  const isIncreasing = newValue > currentValue;
  const difference = Math.abs(newValue - currentValue);

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {updateType === "users"
              ? t("billing.confirmUpdateUsers")
              : t("billing.confirmUpdateStorage")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {updateType === "users" ? (
              isIncreasing ? (
                t("billing.confirmAddUsers", {
                  count: difference,
                  price: priceChange.toFixed(2),
                  interval: isYearlyBilling ? t("billing.yearly") : t("billing.monthly"),
                })
              ) : (
                t("billing.confirmRemoveUsers", {
                  count: difference,
                  price: priceChange.toFixed(2),
                  interval: isYearlyBilling ? t("billing.yearly") : t("billing.monthly"),
                })
              )
            ) : (
              isIncreasing ? (
                t("billing.confirmAddStorage", {
                  count: difference,
                  price: priceChange.toFixed(2),
                  interval: isYearlyBilling ? t("billing.yearly") : t("billing.monthly"),
                })
              ) : (
                t("billing.confirmRemoveStorage", {
                  count: difference,
                  price: priceChange.toFixed(2),
                  interval: isYearlyBilling ? t("billing.yearly") : t("billing.monthly"),
                })
              )
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onCancel}>
            {t("common.cancel")}
          </AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("billing.processing")}
              </>
            ) : (
              <>
                {updateType === "users" ? (
                  <Users className="mr-2 h-4 w-4" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                {t("billing.confirmUpdate")}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
