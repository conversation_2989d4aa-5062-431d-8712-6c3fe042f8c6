"use client";

import { useState } from "react";
import { Loader2, Users, Plus, Minus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createCustomerPortalSession } from "@/services";
import { SubscriptionWithPlan } from "@/services/src/subscriptions";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

interface ManageAdditionalUsersProps {
  subscription: SubscriptionWithPlan;
  tenantId: string;
}

export function ManageAdditionalUsers({
  subscription,
  tenantId,
}: ManageAdditionalUsersProps) {
  const [additionalUsers, setAdditionalUsers] = useState(
    subscription.additionalUsers
  );
  const [loading, setLoading] = useState(false);
  const plan = subscription.plan;
  const { t } = useLanguage();

  const incrementUsers = () => {
    setAdditionalUsers((prev) => prev + 1);
  };

  const decrementUsers = () => {
    setAdditionalUsers((prev) => (prev > 0 ? prev - 1 : 0));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setAdditionalUsers(value);
    } else if (e.target.value === "") {
      setAdditionalUsers(0);
    }
  };

  const handleUpdateUsers = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);

      // Check if anything has changed
      const hasChanges = additionalUsers !== subscription.additionalUsers;

      if (!hasChanges) {
        toast.error(t("billing.noChangesToUpdate"));
        setLoading(false);
        return;
      }

      // Open Stripe customer portal to update subscription
      const portalUrl = await createCustomerPortalSession(
        tenantId,
        `${window.location.origin}/billing`
      );

      // Redirect to Stripe customer portal
      window.location.href = portalUrl;
    } catch (error) {
      console.error("Error opening customer portal:", error);
      toast.error(t("billing.portalOpenFailed"));
    } finally {
      setLoading(false);
    }
  };

  // Calculate costs including base plan price and additional users
  const basePlanPrice = plan.price || 0; // Base plan price
  const additionalCost = additionalUsers * plan.additionalUserFee; // Cost for additional users
  const totalCost = basePlanPrice + additionalCost; // Total cost includes base plan + additional users

  // Calculate difference from current subscription
  const userDifference = additionalUsers - subscription.additionalUsers;
  const costDifference = userDifference * plan.additionalUserFee;

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("billing.manageSubscription")}</CardTitle>
        <CardDescription>{t("billing.adjustSubscription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="additionalUsers">
            {t("billing.additionalUsers")}
          </Label>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={decrementUsers}
              disabled={additionalUsers === 0}
            >
              <Minus className="h-4 w-4" />
            </Button>
            <Input
              id="additionalUsers"
              type="number"
              min="0"
              value={additionalUsers}
              onChange={handleInputChange}
              className="w-20 text-center"
            />
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={incrementUsers}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="rounded-md border p-4 space-y-2">
          <div className="flex justify-between">
            <span>{t("billing.basePlan", { users: plan.includedUsers })}</span>
            <span>
              {t("billing.priceCHF", { price: basePlanPrice.toFixed(2) })}
            </span>
          </div>
          <div className="flex justify-between">
            <span>
              {t("billing.additionalUsersCount", { count: additionalUsers })}
            </span>
            <span>
              {t("billing.priceCHF", { price: additionalCost.toFixed(2) })}
            </span>
          </div>

          <div className="border-t pt-2 mt-2 flex justify-between font-medium">
            <span>{t("billing.total")}</span>
            <span>
              {t("billing.pricePerMonth", { price: totalCost.toFixed(2) })}
            </span>
          </div>

          {userDifference !== 0 && (
            <div
              className={`text-sm mt-2 ${
                userDifference > 0 ? "text-orange-500" : "text-green-500"
              }`}
            >
              {userDifference > 0
                ? t("billing.addingUsers", {
                    count: userDifference,
                    price: costDifference.toFixed(2),
                  })
                : t("billing.removingUsers", {
                    count: Math.abs(userDifference),
                    price: Math.abs(costDifference).toFixed(2),
                  })}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleUpdateUsers}
          disabled={loading || additionalUsers === subscription.additionalUsers}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("billing.processing")}
            </>
          ) : (
            <>
              <Users className="mr-2 h-4 w-4" />
              {t("billing.updateSubscription")}
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
