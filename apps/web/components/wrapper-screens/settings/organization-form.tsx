"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/lib/language-context";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import ImageUpload from "@/components/image";

// Define form schema with translations
const createFormSchema = (t: any) =>
  z.object({
    name: z.string().min(2, {
      message: t("organization.nameMinLength"),
    }),
    slug: z
      .string()
      .min(2, {
        message: t("organization.slugMinLength"),
      })
      .regex(/^[a-z0-9-]+$/, {
        message: t("organization.slugFormat"),
      }),
    url: z
      .string()
      .url({
        message: t("organization.validUrl"),
      })
      .optional()
      .or(z.literal("")),
    description: z.string().optional(),
  });

type OrganizationFormProps = {
  tenant: {
    id: string;
    name: string;
    slug: string;
    image?: string;
    description?: string;
    url?: string;
  };
};

export function OrganizationForm({ tenant }: OrganizationFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { t } = useLanguage();

  // Create schema with translations
  const formSchema = createFormSchema(t);

  // Initialize form with existing tenant data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: tenant.name || "",
      slug: tenant.slug || "",
      url: tenant.url || "",
      description: tenant.description || "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    toast.loading(t("organization.updating"));

    try {
      // This would be replaced with your actual API call
      // const response = await updateTenant(tenant.id, values);

      // Simulate API call for now
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.remove();
      toast.success(t("organization.updateSuccess"));

      // Refresh the data
      router.refresh();
    } catch (error) {
      console.error("Update company error:", error);
      toast.error(t("organization.updateFailed"));
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div>
      <CardContent className="pt-6">
        <ImageUpload id={tenant?.image} updatedAt={new Date()} />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("organization.nameLabel")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("organization.namePlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("organization.nameDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("organization.urlLabel")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("organization.urlPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("organization.urlDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("organization.descriptionLabel")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("organization.descriptionPlaceholder")}
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("organization.descriptionHelp")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                {t("organization.saveChanges")}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </div>
  );
}
