"""
Intelligent Query Interpreter for Web Search Context

This module handles the interpretation of follow-up questions and conversation context
to generate appropriate web search queries instead of searching for literal phrases
like "give me more" or "tell me more".
"""

import json
import logging
import re
from typing import Optional, List, Dict, Any
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
import os

logger = logging.getLogger(__name__)

class IntelligentQueryInterpreter:
    """
    Interprets user queries in the context of conversation history to generate
    appropriate web search queries for follow-up questions.
    """

    # Common follow-up patterns that indicate the user wants more information
    FOLLOW_UP_PATTERNS = [
        r'\b(?:give|tell|show)\s+me\s+more\b',
        r'\b(?:more|additional)\s+(?:info|information|details)\b',
        r'\b(?:expand|elaborate)\s+(?:on\s+)?(?:this|that|it)?\b',
        r'\b(?:can\s+you\s+)?(?:tell|give)\s+me\s+more\s+(?:about|on)\b',
        r'\b(?:what\s+)?(?:else|more)\s+(?:can\s+you\s+tell\s+me)?\b',
        r'\b(?:continue|go\s+on|keep\s+going)\b',
        r'\b(?:any\s+)?(?:other|additional)\s+(?:examples|details|information)\b',
        r'\b(?:what\s+about|how\s+about)\b',
        r'\b(?:and\s+)?(?:what\s+)?(?:else|more)\b',
        r'\b(?:further|deeper)\s+(?:details|information|explanation)\b',
        r'^\s*(?:more|continue|go\s+on|elaborate|expand)\s*[.?!]*\s*$',
    ]

    # Patterns that indicate specific aspects or clarifications
    CLARIFICATION_PATTERNS = [
        r'\b(?:what\s+about|how\s+about|what\s+if)\b',
        r'\b(?:can\s+you\s+)?(?:explain|clarify|describe)\b',
        r'\b(?:how\s+)?(?:does|do|is|are|can|will|would)\b',
        r'\b(?:why|when|where|who|which)\b',
    ]

    def __init__(self):
        """Initialize the query interpreter with Azure OpenAI."""
        self.llm = None
        try:
            # Check if Azure OpenAI environment variables are available
            azure_endpoint = os.getenv("AZURE_OPENAI_API_ENDPOINT")
            azure_key = os.getenv("AZURE_OPENAI_API_KEY")
            azure_version = os.getenv("AZURE_OPENAI_API_VERSION")
            azure_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")

            if not all([azure_endpoint, azure_key, azure_version, azure_deployment]):
                logger.warning("Azure OpenAI configuration incomplete, will use fallback query generation")
                logger.debug(f"Missing config - Endpoint: {bool(azure_endpoint)}, Key: {bool(azure_key)}, Version: {bool(azure_version)}, Deployment: {bool(azure_deployment)}")
                return

            # Initialize Azure OpenAI for query interpretation
            self.llm = AzureChatOpenAI(
                azure_endpoint=azure_endpoint,
                api_key=azure_key,
                api_version=azure_version,
                azure_deployment=azure_deployment,
                temperature=0.1,  # Low temperature for consistent interpretation
                max_tokens=200,   # Short responses for query generation
            )
            logger.info("Successfully initialized Azure OpenAI for intelligent query interpretation")
        except Exception as e:
            logger.error(f"Failed to initialize Azure OpenAI for query interpretation: {e}")
            logger.info("Will use fallback query generation method")

    def is_follow_up_question(self, question: str) -> bool:
        """
        Determine if a question is a follow-up that needs context interpretation.

        Args:
            question: The user's question

        Returns:
            True if this appears to be a follow-up question
        """
        if not question:
            logger.debug("Empty question provided to follow-up detection")
            return False

        question_lower = question.lower().strip()
        logger.debug(f"Checking if '{question}' is a follow-up question")

        # Check for follow-up patterns
        for pattern in self.FOLLOW_UP_PATTERNS:
            if re.search(pattern, question_lower, re.IGNORECASE):
                logger.info(f"Detected follow-up pattern: {pattern} in question: {question}")
                return True

        # Check for very short questions that likely need context
        if len(question_lower.split()) <= 3 and any(word in question_lower for word in
                                                   ['more', 'else', 'other', 'continue', 'expand', 'elaborate']):
            logger.info(f"Detected short follow-up question: {question}")
            return True

        logger.debug(f"'{question}' is not a follow-up question")
        return False

    def extract_topic_from_conversation(self, conversation_history: str) -> Optional[str]:
        """
        Extract the main topic from conversation history.

        Args:
            conversation_history: JSON string or plain text of conversation history

        Returns:
            The main topic being discussed, or None if not found
        """
        if not conversation_history:
            logger.debug("No conversation history provided")
            return None

        logger.debug(f"Extracting topic from conversation history: {conversation_history[:200]}...")

        try:
            # Try to parse as JSON first (structured conversation history)
            if conversation_history.strip().startswith('{'):
                logger.debug("Parsing conversation history as JSON")
                history_data = json.loads(conversation_history)
                if 'conversationHistory' in history_data:
                    # Extract the last few user questions and assistant responses
                    recent_messages = history_data['conversationHistory'][-4:]  # Last 4 messages
                    logger.debug(f"Found {len(recent_messages)} recent messages")

                    # Look for user questions that aren't follow-ups
                    for message in reversed(recent_messages):
                        if message.get('role') == 'user':
                            content = message.get('content', '').strip()
                            if content and not self.is_follow_up_question(content):
                                logger.info(f"Found main topic from conversation: {content[:100]}...")
                                return content

                    # If no clear user question, look at assistant responses for context
                    for message in reversed(recent_messages):
                        if message.get('role') == 'assistant':
                            content = message.get('content', '').strip()
                            if content and len(content) > 50:  # Substantial response
                                # Extract first sentence or key topic
                                first_sentence = content.split('.')[0].strip()
                                if len(first_sentence) > 20:
                                    logger.info(f"Extracted topic from assistant response: {first_sentence[:100]}...")
                                    return first_sentence

        except json.JSONDecodeError:
            # Treat as plain text conversation history
            logger.info("Treating conversation history as plain text")

        # Fallback: extract key topics from plain text
        lines = conversation_history.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line and len(line) > 20 and not self.is_follow_up_question(line):
                logger.info(f"Found topic from plain text: {line[:100]}...")
                return line

        logger.warning("Could not extract topic from conversation history")
        return None

    async def interpret_query_for_web_search(
        self,
        question: str,
        conversation_history: Optional[str] = None
    ) -> str:
        """
        Interpret a user query in context to generate an appropriate web search query.

        Args:
            question: The user's current question
            conversation_history: Previous conversation context

        Returns:
            An appropriate search query for web search
        """
        logger.info(f"Starting query interpretation for: '{question}'")
        logger.info(f"Conversation history available: {bool(conversation_history)}")

        if not question:
            logger.warning("Empty question provided to query interpreter")
            return ""

        # If it's not a follow-up question, use the question as-is
        if not self.is_follow_up_question(question):
            logger.info(f"Not a follow-up question, using original: {question}")
            return question

        logger.info(f"Detected follow-up question: '{question}'")

        # Extract topic from conversation history
        topic = self.extract_topic_from_conversation(conversation_history)

        if not topic:
            logger.warning("No conversation context found for follow-up question, using original")
            return question

        logger.info(f"Extracted topic from conversation: '{topic[:100]}...'")

        # Use LLM to generate an intelligent search query
        if self.llm:
            try:
                search_query = await self._generate_contextual_search_query(question, topic)
                if search_query:
                    logger.info(f"Generated contextual search query: {search_query}")
                    return search_query
            except Exception as e:
                logger.error(f"Error generating contextual search query: {e}")

        # Fallback: combine the follow-up with the topic
        fallback_query = self._create_fallback_search_query(question, topic)
        logger.info(f"Using fallback search query: {fallback_query}")
        return fallback_query

    async def _generate_contextual_search_query(self, follow_up: str, topic: str) -> Optional[str]:
        """
        Use LLM to generate a contextual search query.

        Args:
            follow_up: The follow-up question
            topic: The main topic from conversation

        Returns:
            Generated search query or None if failed
        """
        try:
            system_message = SystemMessage(
                content="""You are a search query generator. Given a follow-up question and the conversation topic,
                generate a specific web search query that will find relevant information.

                Rules:
                1. Focus on the main topic, not the follow-up phrase
                2. Make the query specific and searchable
                3. Include key terms that would appear in relevant articles
                4. Keep it concise (3-8 words typically)
                5. Return ONLY the search query, nothing else

                Examples:
                - Follow-up: "tell me more" + Topic: "benefits of renewable energy" → "renewable energy advantages benefits"
                - Follow-up: "give me more details" + Topic: "machine learning algorithms" → "machine learning algorithms types examples"
                - Follow-up: "expand on this" + Topic: "climate change effects" → "climate change environmental impacts"
                """
            )

            user_message = HumanMessage(
                content=f"Follow-up question: \"{follow_up}\"\nConversation topic: \"{topic[:200]}\"\n\nGenerate search query:"
            )

            response = await self.llm.ainvoke([system_message, user_message])
            search_query = response.content.strip()

            # Validate the generated query
            if search_query and len(search_query) > 5 and not self.is_follow_up_question(search_query):
                return search_query

        except Exception as e:
            logger.error(f"Error in LLM query generation: {e}")

        return None

    def _create_fallback_search_query(self, follow_up: str, topic: str) -> str:
        """
        Create a fallback search query by combining follow-up intent with topic.

        Args:
            follow_up: The follow-up question
            topic: The main topic

        Returns:
            A fallback search query
        """
        logger.info(f"Creating fallback search query for follow-up: '{follow_up}' with topic: '{topic[:100]}...'")

        # Extract key terms from the topic
        topic_words = topic.lower().split()

        # Remove common stop words but keep important ones
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'what', 'how', 'when', 'where', 'why', 'who', 'which'}
        key_words = [word for word in topic_words if word not in stop_words and len(word) > 2]

        # Take the most important words (first 5-6)
        key_words = key_words[:6]

        # Determine the intent from the follow-up
        follow_up_lower = follow_up.lower()
        intent_added = False

        if any(word in follow_up_lower for word in ['example', 'examples']):
            key_words.append('examples')
            intent_added = True
        elif any(word in follow_up_lower for word in ['detail', 'details', 'more details']):
            key_words.append('details')
            intent_added = True
        elif any(word in follow_up_lower for word in ['benefit', 'advantage', 'benefits']):
            key_words.append('benefits')
            intent_added = True
        elif any(word in follow_up_lower for word in ['how', 'method', 'way']):
            key_words.append('how')
            intent_added = True
        elif any(word in follow_up_lower for word in ['why', 'reason']):
            key_words.append('why')
            intent_added = True
        elif any(word in follow_up_lower for word in ['application', 'use', 'uses']):
            key_words.append('applications')
            intent_added = True
        elif any(word in follow_up_lower for word in ['type', 'types', 'kind', 'kinds']):
            key_words.append('types')
            intent_added = True

        # If no specific intent was detected, add general terms
        if not intent_added:
            if any(word in follow_up_lower for word in ['more', 'additional', 'further']):
                key_words.append('information')
            elif any(word in follow_up_lower for word in ['expand', 'elaborate']):
                key_words.append('explanation')
            else:
                key_words.append('more')

        fallback_query = ' '.join(key_words)
        logger.info(f"Generated fallback query: '{fallback_query}'")
        return fallback_query

# Global instance for use across the application
query_interpreter = IntelligentQueryInterpreter()
