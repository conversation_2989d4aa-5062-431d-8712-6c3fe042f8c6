import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { BlobServiceClient } from "@azure/storage-blob";
import db from "@/lib/shared-db";

const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const tenantId = formData.get("tenantId") as string;
    const workspaceSlug = formData.get("workspaceSlug") as string;

    if (!file || !tenantId || !workspaceSlug) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });
    console.log({ tenant });

    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);
    console.log({ containerClient });

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "container",
    });

    // Generate blob path with tenant and workspace structure
    const blobName = `${tenant?.slug}/${workspaceSlug}/${Date.now()}-${
      file.name
    }`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    console.log({ arrayBuffer });

    // Upload file
    await blockBlobClient.uploadData(arrayBuffer, {
      blobHTTPHeaders: { blobContentType: file.type },
    });

    return NextResponse.json({
      url: blockBlobClient.url,
      message: "File uploaded successfully",
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}
