"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, UserPlus } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { useEffect, useState } from "react";
import { getCookie } from "@/utils/cookies";

import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Create schema with translations
const createFormSchema = (t: any) =>
  z.object({
    email: z.string().email({
      message: t("inviteMember.validEmail"),
    }),
    role: z.enum(["ADMIN", "MEMBER", "OWNER", "CUSTOM"], {
      required_error: t("inviteMember.selectRole"),
    }),
    customRoleId: z.string().optional(),
  });

export function InviteDialog({
  open,
  onSubmit,
  setOpen,
  isLoading = false,
  trigger,
  title,
  subTitle,
  userRole,
}) {
  const { t } = useLanguage();
  const [customRoles, setCustomRoles] = useState([]);
  const [loadingRoles, setLoadingRoles] = useState(false);

  // Create schema with translations
  const formSchema = createFormSchema(t);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      role: "MEMBER",
      customRoleId: "",
    },
  });

  // Fetch custom roles
  useEffect(() => {
    const fetchCustomRoles = async () => {
      if (open && userRole === "OWNER") {
        setLoadingRoles(true);
        try {
          const tenantId = getCookie("currentOrganizationId");
          const response = await fetch(`/api/roles?tenantId=${tenantId}`);
          if (response.ok) {
            const data = await response.json();
            setCustomRoles(data.customRoles || []);
          }
        } catch (error) {
          console.error("Error fetching custom roles:", error);
        } finally {
          setLoadingRoles(false);
        }
      }
    };

    fetchCustomRoles();
  }, [open, userRole]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <UserPlus className="h-4 w-4 mr-2" />
          {trigger}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <UserPlus className="h-6 w-6" />
            <DialogTitle className="text-xl">{title}</DialogTitle>
          </div>
          <DialogDescription>{subTitle}</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("inviteMember.emailAddress")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("inviteMember.emailPlaceholder")}
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>{t("roles.role")}</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Reset customRoleId when not selecting CUSTOM
                        if (value !== "CUSTOM") {
                          form.setValue("customRoleId", "");
                        }
                      }}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      {userRole === "OWNER" && (
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="OWNER" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {t("roles.owner")}
                          </FormLabel>
                        </FormItem>
                      )}
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="ADMIN" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {t("roles.admin")}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="MEMBER" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {t("roles.member")}
                        </FormLabel>
                      </FormItem>
                      {userRole === "OWNER" && customRoles.length > 0 && (
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="CUSTOM" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {t("roles.customRole") || "Custom Role"}
                          </FormLabel>
                        </FormItem>
                      )}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("role") === "CUSTOM" && (
              <FormField
                control={form.control}
                name="customRoleId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("roles.selectCustomRole") || "Select Custom Role"}
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={loadingRoles}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              t("roles.selectRole") || "Select a role"
                            }
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {customRoles.map((role: any) => (
                          <SelectItem key={role.id} value={role.id}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <div className="flex justify-end pt-2">
              <Button disabled={isLoading} type="submit">
                {isLoading ? <Loader2 className="animate-spin mr-2" /> : null}
                {t("inviteMember.sendInvitation")}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
