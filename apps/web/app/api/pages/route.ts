import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import db from "@/lib/shared-db";
import { withPermission } from "@/lib/permission-middleware";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";

// Create a new page
const createPage = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, tenantId, workspaceSlug: rawWorkspaceSlug } = body;
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;

    if (!name || !tenantId) {
      return NextResponse.json(
        { error: "Title, content, and tenantId are required" },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    const workspaceId = await db.workspace.findFirst({
      where: {
        slug: workspaceSlug,
        tenantId: tenantId,
      },
    });

    if (!workspaceId?.id) {
      return NextResponse.json(
        { error: "Workspace Not Found" },
        { status: 403 }
      );
    }
    const page = await db.page.create({
      data: {
        name,
        workspaceId: workspaceId?.id,
      },
    });

    return NextResponse.json({
      data: page,
      message: "Page created successfully",
    });
  } catch (error) {
    console.error("Create page error:", error);
    return NextResponse.json(
      { error: "Failed to create page" },
      { status: 500 }
    );
  }
};

// Get all pages or a specific page
const getPages = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get("pageId");
    const tenantIdFromParams = searchParams.get("tenantId");

    // Use tenantId from header or fallback to query parameter
    const tenantId = tenantIdFromHeader || tenantIdFromParams;

    const rawWorkspaceSlug = searchParams.get("workspaceSlug");
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;

    if (!tenantId) {
      return NextResponse.json(
        {
          error:
            "TenantId is required in x-tenant-id header or query parameters",
        },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    if (pageId) {
      const page = await db.page.findFirst({
        where: {
          id: pageId,
          workspace: {
            tenantId: tenantId,
            slug: workspaceSlug,
          },
        },
        include: {
          folders: {
            include: {
              childRelations: true,
              parentRelations: true,
            },
            where: {
              childRelations: {
                none: {},
              },
            },
          },
          files: {
            where: {
              parentId: pageId,
            },
          },
        },
      });

      if (!page) {
        return NextResponse.json({ error: "Page not found" }, { status: 404 });
      }

      return NextResponse.json({ data: page });
    }

    const pages = await db.page.findMany({
      where: {
        workspace: {
          slug: workspaceSlug,
          tenantId: tenantId,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ data: pages });
  } catch (error) {
    console.error("Get pages error:", error);
    return NextResponse.json(
      { error: "Failed to fetch pages" },
      { status: 500 }
    );
  }
};

// Update a page
const updatePage = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      id,
      tenantId: tenantIdFromBody,
      workspaceSlug: rawWorkspaceSlug,
      ...data
    } = body;
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;

    // Use tenantId from header or fallback to body parameter
    const tenantId = tenantIdFromHeader || tenantIdFromBody;

    if (!id || !tenantId) {
      return NextResponse.json(
        {
          error:
            "Page ID and tenantId are required in x-tenant-id header or request body",
        },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId: tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Check if page exists and belongs to the tenant
    const existingPage = await db.page.findFirst({
      where: {
        id,
        workspace: {
          tenantId: tenantId,
          slug: workspaceSlug,
        },
      },
    });

    if (!existingPage) {
      return NextResponse.json({ error: "Page not found" }, { status: 404 });
    }

    const updatedPage = await db.page.update({
      where: { id },
      data,
    });

    return NextResponse.json({
      data: updatedPage,
      message: "Page updated successfully",
    });
  } catch (error) {
    console.error("Update page error:", error);
    return NextResponse.json(
      { error: "Failed to update page" },
      { status: 500 }
    );
  }
};

// Delete a page
const deletePage = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get("id");
    const tenantIdFromParams = searchParams.get("tenantId");
    const force = searchParams.get("force") ?? "false";

    // Use tenantId from header or fallback to query parameter
    const tenantId = tenantIdFromHeader || tenantIdFromParams;

    if (!pageId || !tenantId) {
      return NextResponse.json(
        {
          error:
            "Page ID and tenantId are required in x-tenant-id header or query parameters",
        },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Check if page exists and belongs to the tenant
    const existingPage = await db.page.findFirst({
      where: {
        id: pageId,
      },
      include: {
        folders: true,
        files: true,
      },
    });

    if (!existingPage) {
      return NextResponse.json({ error: "Page not found" }, { status: 404 });
    }

    if (
      (existingPage?.folders?.length || existingPage?.files?.length) &&
      force !== "true"
    ) {
      return NextResponse.json({ error: "Page is not empty" }, { status: 400 });
    }

    // Delete the page
    await db.page.delete({
      where: { id: pageId },
    });

    return NextResponse.json({
      message: "Page deleted successfully",
    });
  } catch (error) {
    console.error("Delete page error:", error);
    return NextResponse.json(
      { error: "Failed to delete page" },
      { status: 500 }
    );
  }
};

// Export the handlers with permission checks
export const GET = withPermission(getPages, "READ", "PAGE");
export const POST = withPermission(createPage, "CREATE", "PAGE");
export const PUT = withPermission(updatePage, "UPDATE", "PAGE");
export const DELETE = withPermission(deletePage, "DELETE", "PAGE");
