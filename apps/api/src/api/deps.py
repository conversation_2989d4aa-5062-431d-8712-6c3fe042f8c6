from motor.motor_asyncio import AsyncIOMotorClient
from fastapi import Depends

from src.config import settings  # Ensure DATABASE_URL is stored in settings

MONGO_URI = settings.DATABASE_URL  # Get MongoDB connection string from .env
client = AsyncIOMotorClient(MONGO_URI)
db = client.get_database()  # Adjust with your actual DB name


async def get_db():
    """Dependency to get the MongoDB database instance."""
    yield db


SessionDep = Depends(get_db)
