import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { compare, hash } from "bcrypt";
import crypto from "crypto";
export async function POST(request: Request) {
  const body = await request.json();
  const { email, password, newPassword } = body;

  try {
    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    // Find the user by email
    const user = await db.user.findUnique({
      where: { emailHash },
    });

    if (!user || !user.password) {
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    // Verify password
    const passwordMatch = await compare(password, user.password);
    if (!passwordMatch) {
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    // If password reset is required, ensure new password is provided
    if (user.passwordResetRequired) {
      if (!newPassword) {
        return NextResponse.json(
          {
            error: "Password reset required",
            passwordResetRequired: true,
          },
          { status: 400 }
        );
      }

      // Update password and reset the flag
      const hashedPassword = await hash(newPassword, 12);
      await db.user.update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          passwordResetRequired: false,
        },
      });

      return NextResponse.json({
        message: "Password updated successfully",
        success: true,
      });
    }

    return NextResponse.json({
      message: "Authentication successful",
      success: true,
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "An error occurred during login" },
      { status: 500 }
    );
  }
}
