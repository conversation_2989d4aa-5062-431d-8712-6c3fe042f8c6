"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  ChevronLeft,
  Download,
  Edit2,
  ExternalLink,
  Trash2,
  RefreshCw,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { workspaceChatService } from "@/services/workspace-chat";
import { getCookie } from "@/utils/cookies";
import toast from "react-hot-toast";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useFileOperations } from "@/hooks/use-file-operations";
import { CreateFolderDialog } from "./page-wrapper";
import Image from "next/image";
import ReactMarkdown from "react-markdown";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function FilePage({
  workspaceSlug,
  tenantId,
  file,
  fileId,
  usageSummary,
}) {
  console.log({ file });
  const { t } = useLanguage();
  const router = useRouter();
  const searchParams: any = useSearchParams();

  const [isLoading, setIsLoading] = useState(false);
  const pageId = searchParams.get("page") || "";

  const [newFolderName, setNewFolderName] = useState(file?.name);
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const { handleRenameFile, handleDeleteFile } = useFileOperations({
    workspaceSlug,
    tenantId,
    pageId: pageId as string,
    setIsLoading,
    usageSummary,
  });
  const isSyncedWithCloud = file?.gDriveFileId || file?.oneDriveFileId;
  const userId = getCookie("userId") || "";

  // Get vectorization status badge
  const getVectorizationStatusBadge = (status?: string) => {
    switch (status) {
      case "PENDING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Pending
          </span>
        );
      case "PROCESSING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Processing
          </span>
        );
      case "COMPLETED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Indexed
          </span>
        );
      case "FAILED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Failed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
            Unknown
          </span>
        );
    }
  };

  // Handle manual vectorization
  const handleManualVectorization = async () => {
    try {
      toast.loading("Starting vectorization process...");

      await workspaceChatService.uploadForIndexing({
        userId: userId,
        document_path: file.url,
        workspaceSlug: workspaceSlug,
        tenantId: tenantId,
        file_id: fileId,
      });

      toast.remove();
      toast.success("Vectorization process started successfully!");

      // Reload the page to show updated status
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      toast.remove();
      toast.error("Failed to start vectorization process. Please try again.");
      console.error("Error starting vectorization:", error);
    }
  };

  // Get file preview based on extension
  const getFilePreview = () => {
    if (!file) return null;

    const extension = file.extension?.toLowerCase();

    switch (extension) {
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return (
          <div className="flex justify-center py-8">
            <div className="rounded-lg overflow-hidden border max-w-2xl">
              <div className="aspect-video relative">
                <Image
                  src={file.url}
                  alt={file.name}
                  className="object-contain"
                  height={500}
                  width={500}
                  loading="lazy"
                  onError={(e) => {
                    console.error("Error loading image:", e);
                    e.currentTarget.src = "/placeholder-image.png";
                  }}
                />
              </div>
            </div>
          </div>
        );
      case "pdf":
        return (
          <div className="flex justify-center py-8">
            <div className="rounded-lg overflow-hidden border w-full">
              <div className="relative">
                <iframe
                  style={{
                    width: "100%",
                    height: "80vh",
                    border: "none",
                    paddingBottom: 16,
                  }}
                  src={`${file.url}`}
                  title={file.name}
                  onLoad={(e) => {
                    // Hide loading message when iframe is loaded
                    const loadingEl =
                      e.currentTarget.parentElement?.querySelector(
                        ".loading-message"
                      ) as HTMLElement | null;
                    if (loadingEl) loadingEl.style.display = "none";
                  }}
                />
                <div className="loading-message absolute inset-0 flex items-center justify-center bg-background/80">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-4">
                      {t("workspace.loadingDocumentContent")}
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <ExternalLink className="h-4 w-4" />
                        {t("workspace.openInNewTab")}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => downloadURI(file.url, file.name)}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.downloadDocument")}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case "doc":
      case "docx":
      case "ppt":
      case "pptx":
      case "xls":
      case "xlsx":
      case "ods":
      case "odt":
      case "odp":
      case "pptm":
      case "xlsm":
      case "docm":
      case "txt":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              {file.url ? (
                <div className="prose dark:prose-invert max-w-none">
                  <iframe
                    src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
                      file.url
                    )}`}
                    style={{
                      width: "100%",
                      height: "80vh",
                      border: "none",
                      paddingBottom: 16,
                    }}
                    title={file.name}
                    onLoad={(e) => {
                      // Hide loading message when iframe is loaded
                      const loadingEl =
                        e.currentTarget.parentElement?.querySelector(
                          ".loading-message"
                        ) as HTMLElement | null;
                      if (loadingEl) loadingEl.style.display = "none";
                    }}
                  ></iframe>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  <p>{t("workspace.documentNotAvailable")}</p>
                  <p className="text-sm mt-2">
                    {t("workspace.documentCouldNotBeLoaded")}
                  </p>
                </div>
              )}
            </Card>
          </div>
        );
      case "md":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              <div className="prose dark:prose-invert max-w-none">
                {file.content ? (
                  <ReactMarkdown>{file.content}</ReactMarkdown>
                ) : (
                  <div className="flex items-center justify-center min-h-[200px]">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">
                        {t("workspace.loadingMarkdownContent")}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.viewRawMarkdown")}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        );

      case "csv":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              {file.url ? (
                file.content ? (
                  <div className="overflow-auto max-h-[70vh]">
                    <div dangerouslySetInnerHTML={{ __html: file.content }} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center min-h-[200px]">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">
                        {t("workspace.loadingCsvContent")}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.downloadCsvFile")}
                      </Button>
                    </div>
                  </div>
                )
              ) : (
                <div className="text-center text-muted-foreground">
                  <p>{t("workspace.csvFileNotAvailable")}</p>
                  <p className="text-sm mt-2">
                    {t("workspace.fileCouldNotBeLoaded")}
                  </p>
                </div>
              )}
            </Card>
          </div>
        );
      case "json":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              {file.url ? (
                file.content ? (
                  <pre className="whitespace-pre-wrap text-sm p-4 bg-muted rounded-md overflow-auto max-h-[70vh]">
                    {file.content}
                  </pre>
                ) : (
                  <div className="flex items-center justify-center min-h-[200px]">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">
                        {t("workspace.loadingJsonContent")}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.downloadJsonFile")}
                      </Button>
                    </div>
                  </div>
                )
              ) : (
                <div className="text-center text-muted-foreground">
                  <p>{t("workspace.jsonFileNotAvailable")}</p>
                  <p className="text-sm mt-2">
                    {t("workspace.fileCouldNotBeLoaded")}
                  </p>
                </div>
              )}
            </Card>
          </div>
        );
      case "html":
      case "htm":
        return (
          <div className="flex justify-center py-8">
            <div className="rounded-lg overflow-hidden border w-full">
              {file.url ? (
                <div>
                  <div className="relative">
                    <iframe
                      style={{
                        width: "100%",
                        height: "80vh",
                        border: "none",
                        paddingBottom: 16,
                      }}
                      src={`${file.url}`}
                      title={file.name}
                      sandbox="allow-same-origin allow-scripts"
                      onLoad={(e) => {
                        // Hide loading message when iframe is loaded
                        const loadingEl =
                          e.currentTarget.parentElement?.querySelector(
                            ".loading-message"
                          ) as HTMLElement | null;
                        if (loadingEl) loadingEl.style.display = "none";
                      }}
                    />
                    <div className="loading-message absolute inset-0 flex items-center justify-center bg-background/80">
                      <div className="text-center">
                        <p className="text-muted-foreground mb-4">
                          {t("workspace.loadingHtmlContent")}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="text-center p-2">
                    <div className="flex gap-2 justify-center">
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <ExternalLink className="h-4 w-4" />
                        {t("workspace.openInNewTab")}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => downloadURI(file.url, file.name)}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.downloadHtmlFile")}
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center min-h-[200px]">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-4">
                      {t("workspace.htmlFileNotAvailable")}
                    </p>
                    <p className="text-sm text-muted-foreground mt-2 mb-4">
                      {t("workspace.fileCouldNotBeLoaded")}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      default:
        return (
          <div className="flex justify-center py-8">
            <div className="rounded-lg overflow-hidden border max-w-lg p-8 text-center">
              <p className="text-xl mb-4">
                {t("workspace.fileTypeCannotBePreviewedTitle")}
              </p>
              <p className="text-muted-foreground mb-4">
                {t("workspace.fileTypeCannotBePreviewedDesc", {
                  extension: extension ? `.${extension}` : "",
                })}
              </p>
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={() => window.open(file.url, "_blank")}
                  className="gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  {t("workspace.openInNewTab")}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => downloadURI(file.url, file.name)}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  {t("workspace.downloadFile")}
                </Button>
              </div>
            </div>
          </div>
        );
    }
  };

  const handleRename = async () => {
    if (await handleRenameFile(fileId, newFolderName)) {
      setNewFolderName("");
    }
  };
  function downloadURI(uri: string, name: string) {
    var link = document.createElement("a");
    link.download = name;
    link.target = "_blank";
    link.href = uri;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  if (!file) {
    return <div>{t("workspace.loadingFile")}</div>;
  }

  return (
    <div className="h-full">
      <div className="flex justify-between items-center p-4 border-b">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            className="gap-1"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4" />
            {t("workspace.back")}
          </Button>
          <div>
            <h1 className="text-xl font-bold">{file?.name}</h1>
            <p className="text-sm text-muted-foreground">
              {file?.extension ? `.${file.extension}` : ""} · {file?.size} ·
              {t("workspace.lastModified", {
                date: file ? new Date(file.createdAt).toLocaleDateString() : "",
              })}
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <div className="ml-2">
            <Tooltip>
              <TooltipTrigger asChild>
                {getVectorizationStatusBadge(file?.vectorizationStatus)}
              </TooltipTrigger>
              <TooltipContent>
                {t("workspace.vectorizationStatusTooltip")}
              </TooltipContent>
            </Tooltip>
          </div>
          {(file?.vectorizationStatus === "PENDING" ||
            file?.vectorizationStatus === "FAILED") && (
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              onClick={handleManualVectorization}
            >
              <RefreshCw className="h-3 w-3" />
              {file?.vectorizationStatus === "PENDING"
                ? "Start Indexing"
                : "Retry Indexing"}
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => {
              if (
                [
                  "docx",
                  "doc",
                  "pptx",
                  "ppt",
                  "pptm",
                  "xlsx",
                  "xls",
                  "odt",
                  "ods",
                  "odp",
                  "xlsm",
                  "txt",
                  "docm",
                ]?.includes(file?.extension)
              ) {
                window.open(
                  `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
                    file.url
                  )}`,
                  "_blank"
                );
              } else {
                window.open(file.url, "_blank");
              }
            }}
          >
            <ExternalLink className="h-4 w-4" />
            {t("workspace.openInNewTab")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => downloadURI(file.url, file.name)}
          >
            <Download className="h-4 w-4" />
            {t("workspace.downloadFile")}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={isSyncedWithCloud} variant="ghost" size="sm">
                {t("workspace.more")}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("workspace.actions")}</DropdownMenuLabel>
              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={() => setIsCreateFolderOpen(true)}>
                <Edit2 className="h-4 w-4 mr-2" />
                {t("workspace.rename")}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-500"
                onClick={async () => {
                  // Delete the file and navigate back
                  await handleDeleteFile(fileId);

                  if (file?.parentId) {
                    // If file is in a folder, go back to that folder
                    router.push(
                      `/workspace/${workspaceSlug}/folder/${file.parentId}?page=${pageId}`
                    );
                  } else {
                    // If file is in a page, go back to the workspace
                    router.push(`/workspace/${workspaceSlug}`);
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {t("workspace.delete")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="overflow-auto">{getFilePreview()}</div>
      <CreateFolderDialog
        isLoading={isLoading}
        title={t("workspace.renameFile")}
        subTitle={t("workspace.enterFileName")}
        isOpen={isCreateFolderOpen}
        onClose={() => {
          setIsCreateFolderOpen(false);
          setNewFolderName("");
        }}
        folderName={newFolderName}
        onFolderNameChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setNewFolderName(e.target.value)
        }
        onCreateFolder={handleRename}
        label={t("workspace.newFileName")}
        labelPlaceholder={t("workspace.enterFileNamePlaceholder")}
        buttonText={t("workspace.renameFile")}
      />
    </div>
  );
}
