"use client";

import React, { useState } from "react";
import { ShareButton } from "./ShareButton";
import { useLanguage } from "@/lib/language-context";
import {
  MessageSquare,
  Clock,
  Users,
  MoreHorizontal,
  Edit3,
  Download,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

interface ChatHeaderProps {
  chatId: string | null;
  chatTitle?: string;
  hasMessages: boolean;
  messageCount?: number;
  lastActivity?: string;
  onTitleUpdate?: (newTitle: string) => void;
  onChatDelete?: () => void;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  chatId,
  chatTitle,
  hasMessages,
  messageCount = 0,
  lastActivity,
  onTitleUpdate,
  onChatDelete,
}) => {
  const { t } = useLanguage();
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(chatTitle || "");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Only show header if we have a chat with messages
  if (!chatId || !hasMessages) {
    return null;
  }

  const formatLastActivity = (dateString?: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return "Active now";
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Handle title update
  const handleTitleUpdate = async () => {
    if (!editTitle.trim() || editTitle === chatTitle) {
      setIsEditing(false);
      return;
    }

    try {
      setIsUpdating(true);
      const response = await fetch(`/api/chat/${chatId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: editTitle.trim(),
        }),
      });

      if (response.ok) {
        onTitleUpdate?.(editTitle.trim());
        toast.success("Chat title updated");
        setIsEditing(false);
      } else {
        toast.error("Failed to update title");
      }
    } catch (error) {
      console.error("Error updating title:", error);
      toast.error("Failed to update title");
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle chat export
  const handleExportChat = async () => {
    try {
      const response = await fetch(`/api/chat/${chatId}/export`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${chatTitle || "chat"}-export.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success("Chat exported successfully");
      } else {
        toast.error("Failed to export chat");
      }
    } catch (error) {
      console.error("Error exporting chat:", error);
      toast.error("Failed to export chat");
    }
  };

  // Handle chat deletion
  const handleDeleteChat = async () => {
    try {
      setIsDeleting(true);
      const response = await fetch(`/api/chat/${chatId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Chat deleted successfully");
        onChatDelete?.();
        router.push("/ask-ai");
      } else {
        toast.error("Failed to delete chat");
      }
    } catch (error) {
      console.error("Error deleting chat:", error);
      toast.error("Failed to delete chat");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  return (
    <div className="sticky w-full top-14 z-10 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 px-4 py-4">
      <div className="">
        {/* Main Header Row */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
              <MessageSquare className="h-5 w-5 text-white" />
            </div>

            <div className="flex-1 min-w-0">
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleTitleUpdate();
                      } else if (e.key === "Escape") {
                        setIsEditing(false);
                        setEditTitle(chatTitle || "");
                      }
                    }}
                    className="text-xl font-bold"
                    disabled={isUpdating}
                    autoFocus
                  />
                  <Button
                    size="sm"
                    onClick={handleTitleUpdate}
                    disabled={isUpdating}
                    className="h-8"
                  >
                    {isUpdating ? "..." : "Save"}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setIsEditing(false);
                      setEditTitle(chatTitle || "");
                    }}
                    className="h-8"
                  >
                    Cancel
                  </Button>
                </div>
              ) : (
                <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100 truncate">
                  {chatTitle || t("chat.untitledChat") || "Untitled Chat"}
                </h1>
              )}
              <div className="flex items-center gap-3 mt-1">
                {messageCount > 0 && (
                  <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                    <MessageSquare className="h-3 w-3" />
                    <span>
                      {messageCount}{" "}
                      {messageCount === 1 ? "message" : "messages"}
                    </span>
                  </div>
                )}
                {lastActivity && (
                  <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                    <Clock className="h-3 w-3" />
                    <span>{formatLastActivity(lastActivity)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <ShareButton chatId={chatId} chatTitle={chatTitle} />

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setEditTitle(chatTitle || "");
                    setIsEditing(true);
                  }}
                  disabled={isEditing}
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit title
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExportChat}>
                  <Download className="h-4 w-4 mr-2" />
                  Export chat
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-red-600 focus:text-red-600"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete chat
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Chat</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this chat? This action cannot be
              undone. All messages and data will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteChat}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? "Deleting..." : "Delete Chat"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
