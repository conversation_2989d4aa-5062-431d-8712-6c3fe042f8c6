"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  CheckCircle2,
  Eye,
  EyeOff,
  Loader2,
  Lock,
  ShieldAlert,
  XCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { verifyResetToken, resetPassword } from "@/services/src/auth";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

// Form schema with translations
const createFormSchema = (t: any) =>
  z
    .object({
      password: z.string().min(8, {
        message: t("password.requirements"),
      }),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("auth.passwordsDoNotMatch"),
      path: ["confirmPassword"],
    });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

export default function ResetPasswordPage({
  params,
}: {
  params: { token: string };
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isView, setIsView] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [resetCompleted, setResetCompleted] = useState(false);
  const [tokenError, setTokenError] = useState<string | null>(null);
  const router = useRouter();
  const { t } = useLanguage();

  // Create schema with translations
  const formSchema = createFormSchema(t);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Verify token when component mounts
  useEffect(() => {
    const validateToken = async () => {
      try {
        const response = await verifyResetToken(params.token);

        if (response?.error) {
          setTokenError(response.error);
          setIsTokenValid(false);
        } else {
          setIsTokenValid(true);
        }
      } catch (error) {
        console.error("Token verification error:", error);
        setTokenError(t("password.tokenVerificationFailed"));
        setIsTokenValid(false);
      } finally {
        setIsLoading(false);
      }
    };

    validateToken();
  }, [params.token]);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    toast.loading(t("password.resetting"));

    try {
      const response = await resetPassword({
        token: params.token,
        password: data.password,
      });

      toast.dismiss();
      setIsSubmitting(false);

      if (response?.error) {
        toast.error(response.error);
        return;
      }

      toast.success(t("password.resetSuccess"));
      setResetCompleted(true);

      // Redirect to sign in after 3 seconds
      setTimeout(() => {
        router.push("/sign-in");
      }, 3000);
    } catch (error) {
      toast.dismiss();
      setIsSubmitting(false);
      console.error("Password reset error:", error);
      toast.error(t("common.tryAgain"));
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-sm">
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-center">{t("password.verifyingLink")}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Invalid token
  if (!isTokenValid) {
    return (
      <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-sm">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <ShieldAlert className="h-10 w-10 text-destructive" />
              </div>
              <CardTitle className="text-2xl text-center">
                {t("password.invalidLink")}
              </CardTitle>
              <CardDescription className="text-center">
                {tokenError || t("password.linkExpired")}
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button asChild className="w-full">
                <Link href="/forgot-password">
                  {t("password.requestNewLink")}
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  // Reset completed successfully
  if (resetCompleted) {
    return (
      <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-sm">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <CheckCircle2 className="h-10 w-10 text-green-500" />
              </div>
              <CardTitle className="text-2xl text-center">
                {t("password.passwordReset")}
              </CardTitle>
              <CardDescription className="text-center">
                {t("password.resetSuccessMessage")}
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center text-sm text-muted-foreground">
              {t("password.redirectMessage")}
            </CardContent>
            <CardFooter>
              <Button asChild className="w-full">
                <Link href="/sign-in">{t("common.signInNow")}</Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  // Reset password form
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <div className="flex justify-center mb-4">
              <Lock className="h-10 w-10 text-primary" />
            </div>
            <CardTitle className="text-2xl">
              {t("password.resetPassword")}
            </CardTitle>
            <CardDescription>{t("password.createNewPassword")}</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("password.newPassword")}</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={isView ? "text" : "password"}
                            placeholder={t(
                              "password.createPasswordPlaceholder"
                            )}
                            {...field}
                          />
                          {isView ? (
                            <Eye
                              className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                              onClick={() => setIsView(!isView)}
                            />
                          ) : (
                            <EyeOff
                              className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                              onClick={() => setIsView(!isView)}
                            />
                          )}
                        </div>
                      </FormControl>
                      <FormDescription>
                        {t("password.requirements")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("password.confirmNewPassword")}</FormLabel>
                      <FormControl>
                        <Input
                          type={isView ? "text" : "password"}
                          placeholder={t("password.confirmPasswordPlaceholder")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? (
                    <Loader2 className="animate-spin mr-2" />
                  ) : null}
                  {t("password.resetPassword")}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
