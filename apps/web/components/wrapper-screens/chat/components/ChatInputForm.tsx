import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowUpIcon, Globe } from "lucide-react";
import { AutoResizeTextarea } from "@/components/ui/autoresize-textarea";
import { useLanguage } from "@/lib/language-context";
import { Message, ImageAttachment } from "../types";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ImageUpload } from "./ImageUpload";
import { cn } from "@/lib/utils";

interface ChatInputFormProps {
  input: string;
  setInput: (input: string) => void;
  handleKeyDown: (e: any) => void;
  handleChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleAISubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  status: string;
  state: string;
  isMobile: boolean;
  includeWebResults: boolean;
  setIncludeWebResults: (include: boolean) => void;
  webSearchLimitExceeded?: boolean;
  selectedImages: ImageAttachment[];
  setSelectedImages: (images: ImageAttachment[]) => void;
}

export const ChatInputForm: React.FC<ChatInputFormProps> = ({
  input,
  setInput,
  handleKeyDown,
  handleChange,
  handleAISubmit,
  setMessages,
  status,
  includeWebResults,
  setIncludeWebResults,
  webSearchLimitExceeded,
  selectedImages,
  setSelectedImages,
}) => {
  const { t } = useLanguage();

  const handleImagesSelected = (images: ImageAttachment[]) => {
    setSelectedImages(images);
  };

  const handleRemoveImage = (imageId: string) => {
    setSelectedImages(selectedImages.filter((img) => img.id !== imageId));
  };

  const canSubmit = input.trim() !== "" || selectedImages.length > 0;

  return (
    <div className="flex flex-col items-center w-full">
      {/* Web search toggle */}
      <div className="flex items-center w-full mb-2">
        <div className="px-3 py-1 bg-background/80 backdrop-blur-sm rounded-full border border-input shadow-sm transition-all duration-200 ease-linear">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-2">
                  <Switch
                    id="web-search"
                    checked={includeWebResults === true}
                    onCheckedChange={(checked) => {
                      console.log("Web search toggle changed to:", checked);
                      setIncludeWebResults(checked);
                    }}
                    disabled={
                      webSearchLimitExceeded ||
                      status === "streaming" ||
                      status === "submitted"
                    }
                    className={
                      webSearchLimitExceeded
                        ? "cursor-not-allowed opacity-50"
                        : ""
                    }
                  />
                  <Label
                    htmlFor="web-search"
                    className="flex items-center gap-1 text-xs cursor-pointer"
                  >
                    <Globe className="h-3 w-3" />
                    {t("chat.includeWebResults")}
                  </Label>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                {webSearchLimitExceeded
                  ? t("chat.webSearchLimitExceeded")
                  : t("chat.webSearchTooltip")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Enhanced Image Upload Section */}
      {/* {selectedImages.length > 0 && (
        <div className="w-full max-w-[45rem] mb-4">
          <div className="bg-muted/30 rounded-xl p-4 border border-border/50">
            <div className="flex items-center gap-2 mb-3">
              <div className="bg-primary/10 rounded-full p-1">
                <ImageIcon className="h-4 w-4 text-primary" />
              </div>
              <span className="text-sm font-medium text-foreground">
                {t("chat.attachedImages") || "Attached Images"}
              </span>
              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">
                {selectedImages.length}
              </span>
            </div>
            <ImageUpload
              onImagesSelected={handleImagesSelected}
              selectedImages={selectedImages}
              onRemoveImage={handleRemoveImage}
              disabled={status === "streaming" || status === "submitted"}
            />
          </div>
        </div>
      )} */}

      {/* Chat input form */}
      <div className="w-full flex items-center">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (!canSubmit) return;
            setMessages((prevMessages) => [
              ...prevMessages,
              {
                id: "user_tempid",
                role: "user",
                content: input,
                images: selectedImages.length > 0 ? selectedImages : undefined,
                metadata: {
                  includeWebResults: includeWebResults === true,
                  hasImages: selectedImages.length > 0,
                },
              },
            ]);
            handleAISubmit(e);
            // Clear images after submission
          }}
          className={cn(
            "w-[calc(100%-2rem)] md:w-full max-w-[45rem] bg-background border border-input rounded-[16px] px-3 sm:px-4 py-2 sm:py-3 flex items-center shadow-lg transition-all duration-200 ease-linear mb-6",
            selectedImages.length > 0 && "border-primary/30 shadow-primary/10"
          )}
        >
          {/* Image upload button */}
          <div className="mr-2">
            <ImageUpload
              onImagesSelected={handleImagesSelected}
              selectedImages={selectedImages}
              onRemoveImage={handleRemoveImage}
              disabled={status === "streaming" || status === "submitted"}
            />
          </div>

          <AutoResizeTextarea
            onKeyDown={handleKeyDown}
            onChange={(e: any) => handleChange(e)}
            value={input}
            placeholder={
              selectedImages.length > 0
                ? t("chat.enterMessageWithImages") ||
                  "Ask about your images or add a message..."
                : t("chat.enterMessage")
            }
            className="flex-1 bg-transparent text-sm sm:text-base focus:outline-none px-1 py-1 sm:px-2"
            disabled={status === "streaming" || status === "submitted"}
          />
          <Button
            size="icon"
            className="rounded-full h-8 w-8 sm:h-10 sm:w-10"
            disabled={
              !canSubmit || status === "streaming" || status === "submitted"
            }
          >
            <ArrowUpIcon className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
};
