"use client";

import { useState } from "react";
import { Loader2, Database, Plus, Minus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createCustomerPortalSession } from "@/services";
import { SubscriptionWithPlan } from "@/services/src/subscriptions";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

// Define storage add-on options based on the pricing model
const STORAGE_ADDONS = [
  { id: "10gb", size: 10, price: 29 },
  { id: "50gb", size: 50, price: 79 },
  { id: "100gb", size: 100, price: 149 },
];

interface ManageAdditionalStorageProps {
  subscription: SubscriptionWithPlan;
  tenantId: string;
  usageSummary?: {
    currentUsage: number;
    totalUsage: number;
    limit: number;
  };
}

export function ManageAdditionalStorage({
  subscription,
  tenantId,
  usageSummary,
}: ManageAdditionalStorageProps) {
  // Default to 0 if additionalStorageGB is not defined in the subscription
  const [additionalStorageGB, setAdditionalStorageGB] = useState(
    subscription.additionalStorageGB || 0
  );
  const [loading, setLoading] = useState(false);
  const plan = subscription.plan;
  const { t } = useLanguage();

  // Find the closest storage add-on option
  const getStorageAddon = (size: number) => {
    if (size <= 0) return null;
    if (size <= 10) return STORAGE_ADDONS[0];
    if (size <= 50) return STORAGE_ADDONS[1];
    return STORAGE_ADDONS[2];
  };

  const incrementStorage = () => {
    const currentAddon = getStorageAddon(additionalStorageGB);
    const currentIndex = currentAddon
      ? STORAGE_ADDONS.findIndex((addon) => addon.id === currentAddon.id)
      : -1;

    if (currentIndex === -1) {
      // If no current addon, add the smallest one
      setAdditionalStorageGB(STORAGE_ADDONS[0].size);
    } else if (currentIndex < STORAGE_ADDONS.length - 1) {
      // Move to the next larger addon
      setAdditionalStorageGB(STORAGE_ADDONS[currentIndex + 1].size);
    }
  };

  const decrementStorage = () => {
    const currentAddon = getStorageAddon(additionalStorageGB);
    const currentIndex = currentAddon
      ? STORAGE_ADDONS.findIndex((addon) => addon.id === currentAddon.id)
      : -1;

    if (currentIndex > 0) {
      // Move to the next smaller addon
      setAdditionalStorageGB(STORAGE_ADDONS[currentIndex - 1].size);
    } else {
      // If at the smallest addon or no addon, set to 0
      setAdditionalStorageGB(0);
    }
  };

  const handleUpdateStorage = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);

      // Check if anything has changed
      const hasChanges =
        additionalStorageGB !== (subscription.additionalStorageGB || 0);

      if (!hasChanges) {
        toast.error(t("billing.noChangesToUpdate"));
        setLoading(false);
        return;
      }

      // Open Stripe customer portal to update subscription
      const portalUrl = await createCustomerPortalSession(
        tenantId,
        `${window.location.origin}/billing`
      );

      // Redirect to Stripe customer portal
      window.location.href = portalUrl;
    } catch (error) {
      console.error("Error opening customer portal:", error);
      toast.error(t("billing.portalOpenFailed"));
    } finally {
      setLoading(false);
    }
  };

  // Calculate costs for additional storage
  const basePlanStorage = plan.vectorStoreGB || 0; // Base plan storage
  const currentAddon = getStorageAddon(additionalStorageGB);
  const additionalCost = currentAddon ? currentAddon.price : 0;

  // Calculate difference from current subscription
  const storageDifference =
    additionalStorageGB - (subscription.additionalStorageGB || 0);
  const currentStorageAddon = getStorageAddon(
    subscription.additionalStorageGB || 0
  );
  const newStorageAddon = getStorageAddon(additionalStorageGB);
  const costDifference =
    (newStorageAddon?.price || 0) - (currentStorageAddon?.price || 0);

  // Calculate total storage
  const totalStorage = basePlanStorage + additionalStorageGB;

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("billing.manageStorage")}</CardTitle>
        <CardDescription>{t("billing.adjustStorage")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {usageSummary && (
          <div className="text-sm mb-4">
            <p>
              {t("billing.currentUsage")}: {usageSummary.totalUsage.toFixed(2)}{" "}
              GB
              {usageSummary.totalUsage > basePlanStorage && (
                <span className="text-orange-500 ml-1">
                  ({t("billing.exceededBaseStorage")})
                </span>
              )}
            </p>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="additionalStorage">
            {t("billing.additionalStorage")}
          </Label>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={decrementStorage}
              disabled={additionalStorageGB === 0}
            >
              <Minus className="h-4 w-4" />
            </Button>
            <div className="w-20 text-center border rounded-md py-2">
              {additionalStorageGB > 0 ? `${additionalStorageGB} GB` : "0 GB"}
            </div>
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={incrementStorage}
              disabled={additionalStorageGB >= 100}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="rounded-md border p-4 space-y-2">
          <div className="flex justify-between">
            <span>
              {t("billing.basePlanStorage", { storage: basePlanStorage })}
            </span>
            <span>{t("billing.included")}</span>
          </div>
          <div className="flex justify-between">
            <span>
              {additionalStorageGB > 0
                ? t("billing.additionalStorageGB", {
                    count: additionalStorageGB,
                  })
                : t("billing.noAdditionalStorage")}
            </span>
            <span>
              {additionalStorageGB > 0
                ? t("billing.priceCHF", { price: additionalCost.toFixed(2) })
                : t("billing.free")}
            </span>
          </div>

          <div className="border-t pt-2 mt-2 flex justify-between font-medium">
            <span>{t("billing.totalStorage")}</span>
            <span>{totalStorage} GB</span>
          </div>

          {storageDifference !== 0 && (
            <div
              className={`text-sm mt-2 ${
                costDifference > 0 ? "text-orange-500" : "text-green-500"
              }`}
            >
              {costDifference > 0
                ? t("billing.addingStorage", {
                    count: storageDifference,
                    price: costDifference.toFixed(2),
                  })
                : t("billing.reducingStorage", {
                    count: Math.abs(storageDifference),
                    price: Math.abs(costDifference).toFixed(2),
                  })}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleUpdateStorage}
          disabled={
            loading ||
            additionalStorageGB === (subscription.additionalStorageGB || 0)
          }
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("billing.processing")}
            </>
          ) : (
            <>
              <Database className="mr-2 h-4 w-4" />
              {t("billing.updateStorageAction")}
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
