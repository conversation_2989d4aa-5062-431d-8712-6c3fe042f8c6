import db from "@/lib/shared-db";
import { NextRequest, NextResponse } from "next/server";

// Update user profile
export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");
    const data = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    const user = await db.user.update({
      where: { id: userId },
      data: {
        name: `${data.forename} ${data.surname ?? ""}`?.trim(),
      },
    });
    if (!user.id) {
      return NextResponse.json(
        { error: "Error updating user profile" },
        { status: 500 }
      );
    }

    return NextResponse.json({ ...user }, { status: 200 });
  } catch (error) {
    console.error("Update user API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Delete user account
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    const deleteAllRelation = await db.$transaction(async (tx) => {
      await tx.membership.deleteMany({
        where: { userId },
      });
      const user = await tx.user.delete({
        where: { id: userId },
      });
      return { user };
    });
    if (deleteAllRelation?.user?.id) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: "Error deleting user account" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Delete user API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
// Delete user account
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    const user = await db.user.findUnique({
      where: { id: userId },
    });

    if (!user.id) {
      return NextResponse.json(
        { error: "Error finding user account" },
        { status: 500 }
      );
    }

    return NextResponse.json({ ...user }, { status: 200 });
  } catch (error) {
    console.error("finding user API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
