import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";

// Get all groups for a tenant
async function getGroups(request: Request) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Get all groups for the tenant
    const groups = await db.group.findMany({
      where: {
        tenantId,
      },
      include: {
        groupMembers: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        groupWorkspaces: {
          include: {
            workspace: true,
          },
        },
      },
    });

    return NextResponse.json({
      data: groups,
      message: "Groups retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching groups:", error);
    return NextResponse.json(
      { error: "Failed to fetch groups" },
      { status: 500 }
    );
  }
}

// Create a new group
async function createGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, description, tenantId } = await request.json();

    if (!name || !tenantId) {
      return NextResponse.json(
        { error: "Name and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Check if a group with the same name already exists in this tenant
    const existingGroup = await db.group.findFirst({
      where: {
        name,
        tenantId,
      },
    });

    if (existingGroup) {
      return NextResponse.json(
        { error: "A group with this name already exists" },
        { status: 400 }
      );
    }

    // Create the group
    const group = await db.group.create({
      data: {
        name,
        description,
        tenantId,
      },
    });

    return NextResponse.json({
      message: "Group created successfully",
      data: group,
    });
  } catch (error) {
    console.error("Error creating group:", error);
    return NextResponse.json(
      { error: "Failed to create group" },
      { status: 500 }
    );
  }
}

// Update a group
async function updateGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, name, description, tenantId } = await request.json();

    if (!id || !name || !tenantId) {
      return NextResponse.json(
        { error: "ID, Name, and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const existingGroup = await db.group.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!existingGroup) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Check if another group with the same name already exists in this tenant
    const duplicateGroup = await db.group.findFirst({
      where: {
        name,
        tenantId,
        id: {
          not: id,
        },
      },
    });

    if (duplicateGroup) {
      return NextResponse.json(
        { error: "Another group with this name already exists" },
        { status: 400 }
      );
    }

    // Update the group
    const group = await db.group.update({
      where: {
        id,
      },
      data: {
        name,
        description,
      },
    });

    return NextResponse.json({
      message: "Group updated successfully",
      data: group,
    });
  } catch (error) {
    console.error("Error updating group:", error);
    return NextResponse.json(
      { error: "Failed to update group" },
      { status: 500 }
    );
  }
}

// Delete a group
async function deleteGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get("id");
    const tenantId = url.searchParams.get("tenantId");

    if (!id || !tenantId) {
      return NextResponse.json(
        { error: "ID and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const existingGroup = await db.group.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!existingGroup) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Delete the group (this will cascade delete all group members and group workspaces)
    await db.group.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({
      message: "Group deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting group:", error);
    return NextResponse.json(
      { error: "Failed to delete group" },
      { status: 500 }
    );
  }
}

// Export the handlers with permission checks
export const GET = withPermission(getGroups, "READ", "WORKSPACE");
export const POST = withPermission(createGroup, "CREATE", "WORKSPACE");
export const PUT = withPermission(updateGroup, "UPDATE", "WORKSPACE");
export const DELETE = withPermission(deleteGroup, "DELETE", "WORKSPACE");
