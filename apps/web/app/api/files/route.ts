import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { BlobServiceClient } from "@azure/storage-blob";
import { withPermission } from "@/lib/permission-middleware";
import { getTranslations } from "@/lib/server-i18n";

const getFiles = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Parse query parameters
    const url = new URL(request.url);
    // const page = parseInt(url.searchParams.get("page") ?? "1");
    // const limit = parseInt(url.searchParams.get("limit") ?? "10");
    const tenantIdFromParams = url.searchParams.get("tenantId");
    const fileId = url.searchParams.get("id");

    // Use tenantId from header or fallback to query parameter
    const tenantId = tenantIdFromHeader || tenantIdFromParams;

    if (!tenantId) {
      return NextResponse.json(
        {
          error:
            "Tenant ID is required in x-tenant-id header or query parameters",
        },
        { status: 400 }
      );
    }
    if (fileId) {
      const file = await db.file.findUnique({
        where: { id: fileId },
        include: {
          workspace: true,
        },
      });

      if (!file) {
        return NextResponse.json({ error: "File not found" }, { status: 404 });
      }

      return NextResponse.json({ file });
    }
    // Calculate pagination
    // const skip = (page - 1) * limit;

    // Fetch files with pagination
    const [files] = await Promise.all([
      db.file.findMany({
        where: { tenantId },
        // skip,
        // take: limit,
        orderBy: { createdAt: "desc" },
      }),
    ]);

    return NextResponse.json({
      files,
      // pagination: {
      //   total,
      //   page,
      //   pageSize: limit,
      //   totalPages: Math.ceil(total / limit),
      // },
    });
  } catch (error) {
    console.error("Error fetching files:", error);
    return NextResponse.json(
      { error: "Failed to fetch files" },
      { status: 500 }
    );
  }
};

const createFile = async (request: Request) => {
  try {
    // Get userId from headers
    // Note: tenantId is retrieved but not used in this function as we're using workspaceSlug instead
    const userIdFromHeader = request.headers.get("x-user-id");
    // We're retrieving but not using tenantId in this function
    request.headers.get("x-tenant-id"); // Intentionally not stored

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }
    const body = await request.json();
    const slug = decodeURI(body?.workspaceSlug);

    const workspace = await db.workspace.findFirst({
      where: {
        slug,
      },
    });

    if (!workspace?.id) {
      return NextResponse.json(
        { error: "Workspace Not Found" },
        { status: 403 }
      );
    }

    // Handle both single file and array of files
    let filesToProcess: any[] = [];

    if (body?.files && Array.isArray(body.files) && body.files.length > 0) {
      // Multiple files case
      filesToProcess = body.files;
    } else if (body && typeof body === "object" && !body.files) {
      // Single file case - the body itself is the file
      filesToProcess = [body];
    } else {
      const { t } = getTranslations();
      return NextResponse.json(
        {
          error: t("api.errors.failedToUploadFile"),
          details: "No files provided or invalid file data",
        },
        { status: 400 }
      );
    }

    // Create file records for all files in parallel
    const fileRecords = await Promise.all(
      filesToProcess
        .map((file: any) => {
          if (!file) return null;
          // Remove workspaceSlug from the file data if present
          // We're using the workspace ID from the database lookup instead
          const fileData = { ...file };
          delete fileData.workspaceSlug;
          const rest = fileData;
          return db.file.create({
            data: {
              ...rest,
              workspaceId: workspace.id,
              vectorizationStatus: "PENDING", // Set initial status to PENDING
            },
          });
        })
        .filter(Boolean) // Filter out any null values
    );

    return NextResponse.json(
      {
        message: "Files uploaded successfully",
        data: fileRecords,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error uploading file:", error);
    const { t } = getTranslations();
    return NextResponse.json(
      { error: t("api.errors.failedToUploadFile") },
      { status: 500 }
    );
  }
};

// ✅ Update a file
const updateFile = async (request: Request) => {
  try {
    // Get userId from headers
    // Note: tenantId is retrieved but not used in this function
    const userIdFromHeader = request.headers.get("x-user-id");
    // We're retrieving but not using tenantId in this function
    request.headers.get("x-tenant-id"); // Intentionally not stored

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.unauthorized") },
        { status: 401 }
      );
    }

    const { id, ...data } = await request.json();
    if (!id) {
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.fileIdAndNameRequired") },
        { status: 400 }
      );
    }

    const file = await db.file.update({ where: { id }, data });
    return NextResponse.json({
      message: "File updated successfully",
      file,
    });
  } catch (error) {
    console.error("Error updating file:", error);
    const { t } = getTranslations();
    return NextResponse.json(
      { error: t("api.errors.failedToUpdateFile") },
      { status: 500 }
    );
  }
};

// ✅ Delete a file (with cascading delete for relationships)
const deleteFile = async (request: Request) => {
  try {
    // Get userId from headers
    // Note: tenantId is retrieved but not used in this function
    const userIdFromHeader = request.headers.get("x-user-id");
    // We're retrieving but not using tenantId in this function
    request.headers.get("x-tenant-id"); // Intentionally not stored

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.unauthorized") },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const id = url.searchParams.get("id");

    if (!id) {
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.fileIdRequired") },
        { status: 400 }
      );
    }

    const file = await db.file.findUnique({
      where: { id },
      include: {
        workspace: true,
      },
    });

    if (!file) {
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.fileNotFound") },
        { status: 404 }
      );
    }

    await db.file.delete({ where: { id } });

    // Delete from multiple storage platforms
    try {
      // Delete from Azure Blob Storage
      const blobServiceClient = BlobServiceClient.fromConnectionString(
        process.env.AZURE_STORAGE_CONNECTION_STRING || ""
      );
      const containerClient = blobServiceClient.getContainerClient(
        process.env.AZURE_STORAGE_CONTAINER_NAME || ""
      );
      const parts = decodeURIComponent(
        file?.url?.split("/").slice(-3)?.join("/")
      );
      const blobClient = containerClient.getBlobClient(parts || "");
      await blobClient.delete();
    } catch (storageError) {
      console.error("Error deleting file from storage:", storageError);
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.failedToDeleteFile") },
        { status: 500 }
      );
    }

    // Delete from database

    return NextResponse.json({ message: "File deleted successfully" });
  } catch (error) {
    console.error("Error deleting file:", error);
    const { t } = getTranslations();
    return NextResponse.json(
      { error: t("api.errors.failedToDeleteFile") },
      { status: 500 }
    );
  }
};

// Export the handlers with permission checks
export const GET = withPermission(getFiles, "READ", "FILE");
export const POST = withPermission(createFile, "CREATE", "FILE");
export const PUT = withPermission(updateFile, "UPDATE", "FILE");
export const DELETE = withPermission(deleteFile, "DELETE", "FILE");
