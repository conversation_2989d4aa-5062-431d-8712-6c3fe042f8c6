import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get("from")
      ? new Date(searchParams.get("from") as string)
      : new Date(new Date().setDate(new Date().getDate() - 30));
    const toDate = searchParams.get("to")
      ? new Date(searchParams.get("to") as string)
      : new Date();

    // Get active subscriptions grouped by plan
    const subscriptionsByPlan = await db.subscription.groupBy({
      by: ["planId"],
      where: {
        isActive: true,
        createdAt: {
          lte: toDate,
        },
      },
      _count: {
        _all: true,
      },
    });

    // Get plan details
    const planIds = subscriptionsByPlan.map((item) => item.planId);
    const plans = await db.plan.findMany({
      where: {
        id: {
          in: planIds,
        },
      },
      select: {
        id: true,
        name: true,
        type: true,
      },
    });

    // Map plan names to the results
    const planDistribution = subscriptionsByPlan.map((item) => {
      const plan = plans.find((p) => p.id === item.planId);
      return {
        name: plan?.name || "Unknown",
        value: item._count._all,
      };
    });

    // Get tenants without subscriptions
    const totalTenants = await db.tenant.count({
      where: {
        createdAt: {
          lte: toDate,
        },
      },
    });
    const tenantsWithSubscriptions = await db.tenant.count({
      where: {
        Subscription: {
          some: {
            isActive: true,
          },
        },
        createdAt: {
          lte: toDate,
        },
      },
    });

    const tenantsWithoutSubscriptions = totalTenants - tenantsWithSubscriptions;

    // Add tenants without subscriptions to the distribution
    if (tenantsWithoutSubscriptions > 0) {
      planDistribution.push({
        name: "No Plan",
        value: tenantsWithoutSubscriptions,
      });
    }

    // Get subscription counts by billing interval
    const subscriptionsByInterval = await db.subscription.groupBy({
      by: ["billingInterval"],
      where: {
        isActive: true,
        createdAt: {
          lte: toDate,
        },
      },
      _count: {
        _all: true,
      },
    });

    // Format the billing interval data
    const billingIntervalDistribution = subscriptionsByInterval.map((item) => ({
      name: item.billingInterval === "month" ? "Monthly" : "Yearly",
      value: item._count._all,
    }));

    // Get subscription tier breakdown (free, basic, premium, etc.)
    // This uses the plan type from the Plan model
    const subscriptionsByTier = await db.subscription.findMany({
      where: {
        isActive: true,
        createdAt: {
          lte: toDate,
        },
      },
      include: {
        plan: true,
      },
    });

    // Group by plan type
    const tierCounts = {};
    subscriptionsByTier.forEach((sub) => {
      const tierType = sub.plan.type;
      if (!tierCounts[tierType]) {
        tierCounts[tierType] = 0;
      }
      tierCounts[tierType]++;
    });

    // Format the tier distribution data
    const tierDistribution = Object.entries(tierCounts).map(
      ([tier, count]) => ({
        name: tier,
        value: count as number,
      })
    );

    // Add free tier (tenants without subscriptions) if any
    if (tenantsWithoutSubscriptions > 0) {
      tierDistribution.push({
        name: "FREE",
        value: tenantsWithoutSubscriptions,
      });
    }

    // Get trial subscriptions
    const trialSubscriptions = await db.subscription.count({
      where: {
        isActive: true,
        isOnTrial: true,
        createdAt: {
          lte: toDate,
        },
      },
    });

    return NextResponse.json({
      planDistribution,
      billingIntervalDistribution,
      tierDistribution,
      trialSubscriptions,
    });
  } catch (error) {
    console.error("Error fetching tenant plan metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch tenant plan metrics" },
      { status: 500 }
    );
  }
}
