const Stripe = require("stripe");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16",
});

// Define plan data
const plans = [
  {
    name: "Starter",
    description: "Perfect for small teams or individuals getting started.",
    includedUsers: 4,
    additionalUserFee: 50,
    vectorStoreGB: 20,
    price: 249, // CHF 249 per month
  },
  {
    name: "Business",
    description: "Ideal for growing businesses with moderate usage needs.",
    includedUsers: 15,
    additionalUserFee: 40,
    vectorStoreGB: 60,
    price: 649, // CHF 649 per month
  },
  {
    name: "Enterprise",
    description: "For enterprise teams with higher usage requirements.",
    includedUsers: 30,
    additionalUserFee: 30,
    vectorStoreGB: 120,
    price: 1049, // CHF 1049 per month
  },
];

// Define storage tiers
const storageTiers = [
  {
    name: "10GB",
    sizeGB: 10,
    price: 29, // CHF 29 per month
  },
  {
    name: "50GB",
    sizeGB: 50,
    price: 79, // CHF 79 per month
  },
  {
    name: "100GB",
    sizeGB: 100,
    price: 149, // CHF 149 per month
  },
];

// Function to create a product and its prices
async function createProductWithPrices(
  name,
  description,
  monthlyPrice,
  yearlyPrice,
  metadata = {}
) {
  console.log(`Creating product: ${name}`);
  
  // Create the product
  const product = await stripe.products.create({
    name,
    description,
    active: true,
    metadata,
  });
  
  console.log(`Created product: ${product.id}`);
  
  // Create monthly price
  const monthlyPriceObj = await stripe.prices.create({
    product: product.id,
    currency: "chf",
    unit_amount: Math.round(monthlyPrice * 100), // Convert to cents
    recurring: {
      interval: "month",
    },
    active: true,
  });
  
  console.log(`Created monthly price: ${monthlyPriceObj.id}`);
  
  // Create yearly price (10x monthly with 16.7% discount)
  const yearlyPriceObj = await stripe.prices.create({
    product: product.id,
    currency: "chf",
    unit_amount: Math.round(yearlyPrice * 100), // Convert to cents
    recurring: {
      interval: "year",
    },
    active: true,
  });
  
  console.log(`Created yearly price: ${yearlyPriceObj.id}`);
  
  return {
    product,
    monthlyPrice: monthlyPriceObj,
    yearlyPrice: yearlyPriceObj,
  };
}

// Main function to create all products and prices
async function createStripeProducts() {
  console.log("Starting Stripe product creation...");
  
  const results = {
    plans: [],
    additionalUsers: [],
    storageTiers: [],
  };
  
  // Create base plan products
  for (const plan of plans) {
    const yearlyPrice = plan.price * 10; // 10x monthly price (16.7% discount)
    
    const result = await createProductWithPrices(
      `${plan.name} Plan`,
      `${plan.description} Includes ${plan.includedUsers} users and ${plan.vectorStoreGB} GB vector storage.`,
      plan.price,
      yearlyPrice,
      {
        type: "base_plan",
        plan_name: plan.name,
        included_users: plan.includedUsers.toString(),
        vector_store_gb: plan.vectorStoreGB.toString(),
      }
    );
    
    // Create additional user product for this plan
    const userYearlyPrice = plan.additionalUserFee * 10; // 10x monthly price
    
    const userResult = await createProductWithPrices(
      `${plan.name} Additional User`,
      `Additional users beyond the ${plan.includedUsers} included in the ${plan.name} plan.`,
      plan.additionalUserFee,
      userYearlyPrice,
      {
        type: "additional_user",
        plan_name: plan.name,
      }
    );
    
    results.plans.push({
      plan: plan.name,
      product: result.product.id,
      monthlyPrice: result.monthlyPrice.id,
      yearlyPrice: result.yearlyPrice.id,
      userProduct: userResult.product.id,
      userMonthlyPrice: userResult.monthlyPrice.id,
      userYearlyPrice: userResult.yearlyPrice.id,
    });
    
    results.additionalUsers.push({
      plan: plan.name,
      product: userResult.product.id,
      monthlyPrice: userResult.monthlyPrice.id,
      yearlyPrice: userResult.yearlyPrice.id,
    });
  }
  
  // Create storage tier products
  for (const tier of storageTiers) {
    const yearlyPrice = tier.price * 10; // 10x monthly price (16.7% discount)
    
    const result = await createProductWithPrices(
      `Additional Vector Storage (${tier.name})`,
      `Additional vector storage of ${tier.sizeGB} GB.`,
      tier.price,
      yearlyPrice,
      {
        type: "storage_tier",
        size_gb: tier.sizeGB.toString(),
      }
    );
    
    results.storageTiers.push({
      tier: tier.name,
      product: result.product.id,
      monthlyPrice: result.monthlyPrice.id,
      yearlyPrice: result.yearlyPrice.id,
    });
  }
  
  console.log("\nProduct and price creation completed!");
  console.log("\nSummary of created products and prices:");
  console.log(JSON.stringify(results, null, 2));
  
  console.log("\nUpdate your seed files with these IDs:");
  console.log("\n1. For apps/web/scripts/seed-plans.ts:");
  
  for (const planResult of results.plans) {
    console.log(`
For ${planResult.plan} plan:
  stripePriceId: "${planResult.monthlyPrice}", // Monthly price ID
  stripeYearlyPriceId: "${planResult.yearlyPrice}", // Yearly price ID
  stripeUserPriceId: "${planResult.userMonthlyPrice}", // Monthly user price ID
  stripeUserYearlyPriceId: "${planResult.userYearlyPrice}", // Yearly user price ID`);
  }
  
  console.log("\n2. For apps/web/scripts/seed-storage-tiers.ts:");
  
  for (const tierResult of results.storageTiers) {
    console.log(`
For ${tierResult.tier} tier:
  stripePriceId: "${tierResult.monthlyPrice}", // Monthly price ID
  stripeYearlyPriceId: "${tierResult.yearlyPrice}", // Yearly price ID`);
  }
}

// Run the script
createStripeProducts()
  .then(() => {
    console.log("Script completed successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Error running script:", error);
    process.exit(1);
  });
