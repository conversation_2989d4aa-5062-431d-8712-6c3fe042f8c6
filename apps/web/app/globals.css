@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }


  :root {
  --background: 234.1 54% 100%;
  --foreground: 234.1 2.7% 10%;
  --card: 234.1 27% 100%;
  --card-foreground: 234.1 2.7% 15%;
  --popover: 234.1 54% 100%;
  --popover-foreground: 234.1 97.7% 10%;
  --primary: 234.1 52.9% 37.5%;
  --primary-foreground: 0 0% 100%;
  --secondary: 234.1 20.8% 90%;
  --secondary-foreground: 0 0% 0%;
  --muted: 272.1 20.8% 95%;
  --muted-foreground: 234.1 2.7% 40%;
  --accent: 272.1 20.8% 90%;
  --accent-foreground: 234.1 2.7% 15%;
  --destructive: 0 77% 50%;
  --destructive-foreground: 234.1 2.7% 100%;
  --border: 234.1 25.4% 82%;
  --input: 234.1 25.4% 50%;
  --ring: 234.1 88.7% 45.3%;
  --radius: 0.5rem;
  --chart-1: 195.9 72% 53%;
  --chart-2: 267.9 72% 53%;
  --chart-3: 339.9 72% 53%;
  --chart-4: 51.9 72% 53%;
  --chart-5: 123.9 72% 53%;
  --sidebar-background: 234, 54%, 95%;
  --sidebar-foreground: 234, 3%, 9%;
  --sidebar-primary: 234, 53%, 34%;
  --sidebar-primary-foreground: 0, 0%, 100%;
  --sidebar-accent: 272, 21%, 86%;
  --sidebar-accent-foreground: 0 0% 0%;
  --sidebar-border: 234, 25%, 78%;
  --sidebar-ring: 224, 98%, 45%;
  }

  .dark {
  --background: 234.1 31.6% 10%;
  --foreground: 234.1 2.7% 100%;
  --card: 234.1 27% 10%;
  --card-foreground: 234.1 2.7% 100%;
  --popover: 234.1 31.6% 5%;
  --popover-foreground: 234.1 2.7% 100%;
  --primary: 234.1 52.9% 37.5%;
  --primary-foreground: 0 0% 100%;
  --secondary: 234.1 20.8% 20%;
  --secondary-foreground: 0 0% 100%;
  --muted: 272.1 20.8% 25%;
  --muted-foreground: 234.1 2.7% 65%;
  --accent: 272.1 20.8% 25%;
  --accent-foreground: 234.1 2.7% 95%;
  --destructive: 0 77% 50%;
  --destructive-foreground: 234.1 2.7% 100%;
  --border: 234.1 25.4% 50%;
  --input: 234.1 25.4% 50%;
  --ring: 234.1 88.7% 45.3%;
  --radius: 0.5rem;
  --chart-1: 195.9 72% 53%;
  --chart-2: 267.9 72% 53%;
  --chart-3: 339.9 72% 53%;
  --chart-4: 51.9 72% 53%;
  --chart-5: 123.9 72% 53%;
  --sidebar-background: 234, 32%, 13%;
  --sidebar-foreground: 234, 3%, 100%;
  --sidebar-primary: 234, 53%, 30%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 272, 21%, 21%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 234, 25%, 48%;
  --sidebar-ring: 224, 98%, 45%;
  }

}

.markDown ol li {
  margin-top: 8px;
}

.markDown pre {
  overflow-x: scroll;
  margin-top: 8px;
  margin-bottom: 8px;
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}


/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.markDown ol li {
  margin-top: 8px;
}

.markDown pre {
  overflow-x: scroll;
  margin-top: 8px;
  margin-bottom: 8px;
}
.markdown > * {
  all: revert;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}