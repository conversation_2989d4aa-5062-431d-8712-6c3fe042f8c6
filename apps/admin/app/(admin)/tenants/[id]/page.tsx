"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { UsageChart } from "@/components/dashboard/usage-chart";
import { formatCurrency, formatDate, formatNumber } from "@/lib/utils";
import {
  ArrowLeft,
  Building,
  CreditCard,
  HardDrive,
  Users,
} from "lucide-react";
import Link from "next/link";
import { MetricCard } from "@/components/dashboard/metric-card";
import { useEffect, useState } from "react";

// Mock data for charts and users - in a real app, these would come from API calls
const mockTokenUsage = [
  { name: "Jan", value: 1200 },
  { name: "Feb", value: 1500 },
  { name: "<PERSON>", value: 1800 },
  { name: "Apr", value: 1400 },
  { name: "May", value: 2200 },
  { name: "Jun", value: 2500 },
  { name: "Jul", value: 2800 },
];

const mockStorageUsage = [
  { name: "<PERSON>", value: 40 },
  { name: "Feb", value: 55 },
  { name: "<PERSON>", value: 65 },
  { name: "Apr", value: 78 },
  { name: "May", value: 90 },
  { name: "<PERSON>", value: 102 },
  { name: "Jul", value: 120 },
];

const mockUsers = [
  {
    id: "1",
    name: "<PERSON> Doe",
    email: "<EMAIL>",
    role: "OWNER",
    lastActive: "2023-07-15T14:30:00Z",
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "ADMIN",
    lastActive: "2023-07-14T10:15:00Z",
  },
  {
    id: "3",
    name: "Bob Johnson",
    email: "<EMAIL>",
    role: "MEMBER",
    lastActive: "2023-07-13T16:45:00Z",
  },
];

export default function TenantDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  const [tenant, setTenant] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTenant() {
      try {
        const response = await fetch(`/api/tenants/${id}`);
        if (!response.ok) {
          throw new Error("Failed to fetch tenant data");
        }
        const data = await response.json();
        setTenant(data);
      } catch (err) {
        setError("Error loading tenant data");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchTenant();
  }, [id]);

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-lg">Loading tenant data...</p>
      </div>
    );
  }

  if (error || !tenant) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-lg text-red-500">{error || "Tenant not found"}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/tenants">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">{tenant.name}</h1>
          <p className="text-muted-foreground">{tenant.description}</p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Users"
          value={tenant?.users?.toString()}
          icon={Users}
        />
        <MetricCard
          title="Storage Used"
          value={tenant.storage}
          icon={HardDrive}
        />
        <MetricCard
          title="Monthly Revenue"
          value={formatCurrency(tenant.monthlyRevenue)}
          icon={CreditCard}
        />
        <MetricCard
          title="Plan"
          value={tenant.plan}
          icon={Building}
          description={`Status: ${tenant.status}`}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <UsageChart
          title="Token Usage (Last 7 Months)"
          data={mockTokenUsage}
          dataKey="value"
          valueFormatter={(value) => formatNumber(value)}
        />
        <UsageChart
          title="Storage Usage (GB)"
          data={mockStorageUsage}
          dataKey="value"
          valueFormatter={(value) => `${value} GB`}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left font-medium">Name</th>
                  <th className="px-4 py-3 text-left font-medium">Email</th>
                  <th className="px-4 py-3 text-left font-medium">Role</th>
                  <th className="px-4 py-3 text-left font-medium">
                    Last Active
                  </th>
                </tr>
              </thead>
              <tbody>
                {mockUsers.map((user) => (
                  <tr key={user.id} className="border-b">
                    <td className="px-4 py-3">{user.name}</td>
                    <td className="px-4 py-3">{user.email}</td>
                    <td className="px-4 py-3">{user.role}</td>
                    <td className="px-4 py-3">{formatDate(user.lastActive)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
