import { apiUrl } from "../index";

export interface Notification {
  id: string;
  type: "MENTION" | "COMMENT_REPLY" | "THREAD_SHARED";
  title: string;
  content: string;
  status: "UNREAD" | "READ" | "DISMISSED";
  triggerUser?: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  chat?: {
    id: string;
    title?: string;
    ThreadShare?: {
      shareToken: string;
    }[];
  };
  relatedMessage?: {
    id: string;
    content: string;
  };
  comment?: {
    id: string;
    content: string;
  };
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationsResponse {
  notifications: Notification[];
  unreadCount: number;
  hasMore: boolean;
}

// Get user notifications
export const getNotifications = async (params?: {
  status?: "unread" | "read" | "all";
  limit?: number;
  offset?: number;
}): Promise<NotificationsResponse> => {
  const searchParams = new URLSearchParams();

  if (params?.status) searchParams.append("status", params.status);
  if (params?.limit) searchParams.append("limit", params.limit.toString());
  if (params?.offset) searchParams.append("offset", params.offset.toString());

  const response = await fetch(
    `${apiUrl}/notifications?${searchParams.toString()}`
  );

  if (!response.ok) {
    throw new Error("Failed to fetch notifications");
  }

  return response.json();
};

// Mark notifications as read
export const markNotificationsAsRead = async (params: {
  notificationIds?: string[];
  markAllAsRead?: boolean;
}): Promise<{ success: boolean }> => {
  const response = await fetch(`${apiUrl}/notifications`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error("Failed to mark notifications as read");
  }

  return response.json();
};

// Get unread count only
export const getUnreadCount = async (): Promise<{ unreadCount: number }> => {
  const response = await getNotifications({ status: "unread", limit: 1 });
  return { unreadCount: response.unreadCount };
};
