import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get("days") || "7", 10);
    
    // Calculate the start date
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get total API requests in the period
    const totalRequests = await db.aPIRequest.count({
      where: {
        timestamp: {
          gte: startDate
        }
      }
    });

    // Get failed API requests in the period
    const failedRequests = await db.aPIRequest.count({
      where: {
        timestamp: {
          gte: startDate
        },
        success: false
      }
    });

    // Calculate error rate
    const errorRate = totalRequests > 0 
      ? (failedRequests / totalRequests) * 100 
      : 0;

    // Get errors by endpoint
    const errorsByEndpoint = await db.aPIRequest.groupBy({
      by: ['endpoint'],
      where: {
        timestamp: {
          gte: startDate
        },
        success: false
      },
      _count: {
        _all: true
      }
    });

    // Sort by error count (descending)
    errorsByEndpoint.sort((a, b) => b._count._all - a._count._all);

    // Get errors by tenant
    const errorsByTenant = await db.aPIRequest.groupBy({
      by: ['tenantId'],
      where: {
        timestamp: {
          gte: startDate
        },
        success: false
      },
      _count: {
        _all: true
      }
    });

    // Get tenant names
    const tenantIds = errorsByTenant.map(item => item.tenantId);
    const tenants = await db.tenant.findMany({
      where: {
        id: {
          in: tenantIds
        }
      },
      select: {
        id: true,
        name: true
      }
    });

    // Map tenant names to the results
    const errorsByTenantWithNames = errorsByTenant.map(item => {
      const tenant = tenants.find(t => t.id === item.tenantId);
      return {
        tenantId: item.tenantId,
        tenantName: tenant?.name || 'Unknown',
        errorCount: item._count._all
      };
    });

    // Sort by error count (descending)
    errorsByTenantWithNames.sort((a, b) => b.errorCount - a.errorCount);

    return NextResponse.json({
      period: `${days} days`,
      totalRequests,
      failedRequests,
      errorRate: Math.round(errorRate * 100) / 100,
      errorsByEndpoint: errorsByEndpoint.map(item => ({
        endpoint: item.endpoint,
        errorCount: item._count._all
      })),
      errorsByTenant: errorsByTenantWithNames
    });
  } catch (error) {
    console.error("Error fetching error metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch error metrics" },
      { status: 500 }
    );
  }
}
