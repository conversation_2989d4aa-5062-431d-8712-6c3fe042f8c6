import { Message } from '../types';

/**
 * Processes messages to group regenerated messages with their original messages
 * @param messages Array of messages to process
 * @returns Processed messages with regenerated messages nested under their originals
 */
export const processMessages = (messages: Message[]): Message[] => {
  // Create a map to store message chains (original → regenerated → re-regenerated, etc.)
  const messageChains: Record<string, Message[]> = {};
  // Map to track which messages are regenerated versions of others
  const regenerationMap: Record<string, string> = {};
  // Set to track which messages are at the root of regeneration chains
  const rootMessages = new Set<string>();
  // Create a list to store the final processed messages
  const processedMessages: Message[] = [];

  // First pass: identify regeneration chains
  messages.forEach((message) => {
    if (message.id) {
      if (message.originalMessageId) {
        // This is a regenerated message, track its relationship
        regenerationMap[message.id] = message.originalMessageId;

        // Find the root message of this regeneration chain
        let currentId = message.originalMessageId;
        while (regenerationMap[currentId]) {
          currentId = regenerationMap[currentId];
        }

        // Mark the root message
        rootMessages.add(currentId);

        // Add this message to its chain
        if (!messageChains[currentId]) {
          messageChains[currentId] = [];
        }
        messageChains[currentId].push(message);
      }
    }
  });

  // Second pass: add all non-regenerated messages to processed list
  messages.forEach((message) => {
    if (message.id && !message.originalMessageId) {
      processedMessages.push(message);
    }
  });

  // Third pass: process each regeneration chain
  processedMessages.forEach((rootMessage) => {
    if (rootMessage.id && rootMessages.has(rootMessage.id)) {
      // This is a root message with regenerations
      const chain = messageChains[rootMessage.id] || [];

      // Sort all regenerated messages in the chain by creation time
      const sortedChain = [...chain].sort((a, b) => {
        if (!a.createdAt || !b.createdAt) return 0;
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      });

      // Organize the chain by regeneration level
      const regenerationLevels: Record<number, Message[]> = {};

      // Initialize level 0 with the root message
      regenerationLevels[0] = [rootMessage];

      // Process each regenerated message to determine its level
      sortedChain.forEach((message) => {
        if (message.originalMessageId) {
          // Find the level of the parent message
          let parentLevel = -1;

          // Check each level to find the parent
          Object.entries(regenerationLevels).forEach(([levelStr, messages]) => {
            const level = parseInt(levelStr);
            const foundParent = messages.find(
              (msg) => msg.id === message.originalMessageId
            );
            if (foundParent) {
              parentLevel = level;
            }
          });

          // If parent found, add this message to the next level
          if (parentLevel >= 0) {
            const nextLevel = parentLevel + 1;
            if (!regenerationLevels[nextLevel]) {
              regenerationLevels[nextLevel] = [];
            }
            regenerationLevels[nextLevel].push(message);
          }
        }
      });

      // Flatten the levels into a single array for the carousel
      const allRegeneratedMessages: Message[] = [];
      const maxLevel = Math.max(
        ...Object.keys(regenerationLevels).map((k) => parseInt(k))
      );

      // Start from level 1 (skip the root message which is at level 0)
      for (let level = 1; level <= maxLevel; level++) {
        if (regenerationLevels[level]) {
          // Add generation level to each message's metadata
          regenerationLevels[level].forEach((msg) => {
            // Add generation level to the message
            msg.metadata = {
              ...msg.metadata,
              generationLevel: level,
            };
            allRegeneratedMessages.push(msg);
          });
        }
      }

      // Update the root message with all regenerated messages
      const rootIndex = processedMessages.findIndex(
        (msg) => msg.id === rootMessage.id
      );
      if (rootIndex !== -1) {
        // Mark the root message as having regenerated versions
        processedMessages[rootIndex] = {
          ...processedMessages[rootIndex],
          metadata: {
            ...processedMessages[rootIndex].metadata,
            originalResponse: true,
          },
          regeneratedMessages: allRegeneratedMessages,
        };
      }
    }
  });

  return processedMessages;
};

/**
 * Creates a temporary message for streaming
 * @param assistantMessage The original assistant message
 * @returns A temporary message object
 */
export const createTemporaryMessage = (assistantMessage: Message): Message => {
  return {
    role: "assistant",
    content: "",
    metadata: {
      regeneratedResponse: true,
      isSelected: true,
    },
    originalMessageId: assistantMessage.id,
    sources: [],
  };
};

/**
 * Adds a temporary message to the messages array
 * @param messages Current messages array
 * @param assistantMessage The original assistant message
 * @param tempMessage The temporary message to add
 * @returns Updated messages array
 */
export const addTemporaryMessage = (
  messages: Message[],
  assistantMessage: Message,
  tempMessage: Message
): Message[] => {
  return messages.map((msg) => {
    if (msg.id === assistantMessage.id) {
      // Get existing regenerated messages or initialize empty array
      const existingRegeneratedMessages = msg.regeneratedMessages || [];
      
      // Create a new array with the temporary message added
      const updatedRegeneratedMessages = [...existingRegeneratedMessages, tempMessage];
      
      // Return the updated original message
      return {
        ...msg,
        metadata: {
          ...msg.metadata,
          originalResponse: true,
          isStreaming: true, // Add flag to indicate streaming is in progress
        },
        regeneratedMessages: updatedRegeneratedMessages,
      };
    }
    return msg;
  });
};

/**
 * Updates a streaming message with new content
 * @param messages Current messages array
 * @param assistantMessage The original assistant message
 * @param content New content to update with
 * @param sources Updated sources
 * @returns Updated messages array
 */
export const updateStreamingMessage = (
  messages: Message[],
  assistantMessage: Message,
  content: string,
  sources: any[]
): Message[] => {
  return messages.map((msg) => {
    // Find the original message
    if (msg.id === assistantMessage.id && msg.regeneratedMessages) {
      // Find and update the temporary message in the regeneratedMessages array
      const updatedRegeneratedMessages = msg.regeneratedMessages.map((regMsg) => {
        // Find the temporary message by checking for missing ID
        if (!regMsg.id && regMsg.metadata?.regeneratedResponse) {
          // Update the content and sources of the temporary message
          return {
            ...regMsg,
            content,
            sources,
          };
        }
        return regMsg;
      });
      
      // Return the updated original message
      return {
        ...msg,
        regeneratedMessages: updatedRegeneratedMessages,
      };
    }
    return msg;
  });
};
