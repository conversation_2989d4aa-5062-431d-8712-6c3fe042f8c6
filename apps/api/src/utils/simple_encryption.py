"""
Simplified encryption utility for handling encrypted fields.
This is a fallback implementation that focuses on compatibility.
"""

import os
import base64
import json
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class SimpleDecryption:
    """
    Simple decryption utility that tries multiple approaches.
    """

    def __init__(self):
        self.master_key = os.getenv("NEXT_ENCRYPTION_CLOAK_KEY", "")
        self.keychain_data = os.getenv("NEXT_ENCRYPTION_CLOAK_KEYCHAIN", "")
        self.available = bool(self.master_key or self.keychain_data)

        if not self.available:
            logger.warning("No encryption keys found - decryption will be skipped")
        else:
            logger.info("Encryption keys found - decryption available")

    def decrypt_field(self, encrypted_value: str) -> Optional[str]:
        """
        Attempt to decrypt a field value using various methods.

        Args:
            encrypted_value: The encrypted string from the database

        Returns:
            Decrypted string or original value if decryption fails
        """
        if not encrypted_value or not self.available:
            return encrypted_value

        # If it doesn't look encrypted, return as-is
        if not self._looks_encrypted(encrypted_value):
            return encrypted_value

        # Try different decryption approaches
        decryption_methods = [
            self._try_v1_aesgcm256,
            self._try_simple_base64,
            self._try_prefixed_base64,
            self._try_json_decode,
            self._try_cloak_format,
        ]

        for method in decryption_methods:
            try:
                result = method(encrypted_value)
                if result and result != encrypted_value and self._is_valid_text(result):
                    logger.debug(f"Successfully decrypted using {method.__name__}")
                    return result
            except Exception as e:
                logger.debug(f"Decryption method {method.__name__} failed: {str(e)}")
                continue

        # If all methods fail, return original
        logger.debug("All decryption methods failed, returning original value")
        return encrypted_value

    def _looks_encrypted(self, value: str) -> bool:
        """Check if a value looks like it might be encrypted."""
        if not value or len(value) < 10:
            return False

        # Check for cloak version prefixes
        if value.startswith('v1.aesgcm256.'):
            return True

        # Check for encryption prefixes
        prefixes = ['enc:', 'encrypted:', '$encrypted$', 'cloak:', 'v1.']
        if any(value.startswith(prefix) for prefix in prefixes):
            return True

        # Check if it's base64-like (long string with base64 characters)
        try:
            if len(value) > 20 and all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in value):
                base64.b64decode(value, validate=True)
                return True
        except:
            pass

        return False

    def _is_valid_text(self, text: str) -> bool:
        """Check if decrypted text looks valid."""
        if not text:
            return False

        # Check if it's printable and reasonable length
        try:
            # Must be valid UTF-8
            text.encode('utf-8')
            # Should be mostly printable
            printable_ratio = sum(1 for c in text if c.isprintable() or c.isspace()) / len(text)
            return printable_ratio > 0.8
        except:
            return False

    def _try_v1_aesgcm256(self, value: str) -> Optional[str]:
        """Try to handle v1.aesgcm256 format (simplified approach)."""
        if not value.startswith('v1.aesgcm256.'):
            return None

        try:
            # For simple decryption, we'll just try to extract readable parts
            # This is a fallback when the full cloak decryption fails
            parts = value.split('.')
            if len(parts) >= 4:
                # Try to decode the last part as it might contain readable data
                last_part = '.'.join(parts[4:])
                try:
                    decoded = base64.urlsafe_b64decode(last_part + '==')
                    # Try to find readable text in the decoded data
                    text = decoded.decode('utf-8', errors='ignore')
                    if self._is_valid_text(text):
                        return text
                except:
                    pass
        except:
            pass

        return None

    def _try_simple_base64(self, value: str) -> Optional[str]:
        """Try simple base64 decoding."""
        try:
            decoded = base64.b64decode(value)
            return decoded.decode('utf-8')
        except:
            return None

    def _try_prefixed_base64(self, value: str) -> Optional[str]:
        """Try decoding after removing common prefixes."""
        prefixes = ['enc:', 'encrypted:', '$encrypted$', 'cloak:']

        for prefix in prefixes:
            if value.startswith(prefix):
                clean_value = value[len(prefix):]
                try:
                    decoded = base64.b64decode(clean_value)
                    return decoded.decode('utf-8')
                except:
                    continue

        return None

    def _try_json_decode(self, value: str) -> Optional[str]:
        """Try parsing as JSON (in case it's a JSON-encoded value)."""
        try:
            # Maybe it's JSON-encoded
            parsed = json.loads(value)
            if isinstance(parsed, str):
                return parsed
        except:
            pass

        # Try base64 decode then JSON
        try:
            decoded = base64.b64decode(value)
            parsed = json.loads(decoded.decode('utf-8'))
            if isinstance(parsed, str):
                return parsed
        except:
            pass

        return None

    def _try_cloak_format(self, value: str) -> Optional[str]:
        """Try basic cloak-like format decoding."""
        try:
            # Remove prefixes
            clean_value = value
            for prefix in ['enc:', 'encrypted:', '$encrypted$', 'cloak:']:
                if value.startswith(prefix):
                    clean_value = value[len(prefix):]
                    break

            # Try to decode as base64
            decoded = base64.b64decode(clean_value)

            # If it's short enough, might be simple encrypted text
            if len(decoded) < 1000:  # Reasonable limit
                # Try to decode as UTF-8
                return decoded.decode('utf-8')
        except:
            pass

        return None

    def decrypt_message_content(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decrypt the content field of a message document.

        Args:
            message: Message document from MongoDB

        Returns:
            Message document with decrypted content
        """
        if not message or 'content' not in message:
            return message

        original_content = message['content']
        if not original_content:
            return message

        decrypted_content = self.decrypt_field(original_content)

        # Only update if decryption changed the content
        if decrypted_content and decrypted_content != original_content:
            message['content'] = decrypted_content

        return message

    def decrypt_user_fields(self, user: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decrypt encrypted fields in a user document.

        Args:
            user: User document from MongoDB

        Returns:
            User document with decrypted fields
        """
        if not user:
            return user

        # Decrypt name field
        if 'name' in user and user['name']:
            original_name = user['name']
            decrypted_name = self.decrypt_field(original_name)
            if decrypted_name and decrypted_name != original_name:
                user['name'] = decrypted_name

        # Decrypt email field
        if 'email' in user and user['email']:
            original_email = user['email']
            decrypted_email = self.decrypt_field(original_email)
            if decrypted_email and decrypted_email != original_email:
                user['email'] = decrypted_email

        return user

# Global instance
simple_decryption = SimpleDecryption()

def decrypt_message_content_simple(message: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function to decrypt message content."""
    return simple_decryption.decrypt_message_content(message)

def decrypt_user_fields_simple(user: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function to decrypt user fields."""
    return simple_decryption.decrypt_user_fields(user)

def decrypt_field_simple(encrypted_value: str) -> Optional[str]:
    """Convenience function to decrypt a single field."""
    return simple_decryption.decrypt_field(encrypted_value)

def is_simple_encryption_available() -> bool:
    """Check if encryption keys are available."""
    return simple_decryption.available
