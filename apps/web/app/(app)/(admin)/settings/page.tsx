import OrganizationSettingsPage from "@/components/wrapper-screens/organization-settings/screen";
import { authOptions } from "@/lib/next-auth";
import { getServerSession } from "next-auth";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function Page({ params }) {
  const tenantId = cookies().get("currentOrganizationId")?.value;
  const session: any = await getServerSession(authOptions);

  if (!tenantId) {
    return redirect("/sign-in");
  }

  try {
    const memberships = (session as any)?.memberships || [];
    const membership = memberships.find((m) => m.tenant.id === tenantId);
    const userRole = (session as any).memberships?.find(
      (membership) => membership.tenant.id === tenantId
    )?.role;

    // Handle case where folder data is not found

    return (
      <OrganizationSettingsPage
        organization={membership ? membership.tenant : null}
        userRole={userRole}
      />
    );
  } catch (error) {
    console.error("Error loading folder:", error);
  }
}
