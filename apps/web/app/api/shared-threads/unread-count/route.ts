import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";

// Get unread counts for shared threads
export async function GET(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get current tenant ID from cookies or session
    const cookieStore = cookies();
    const tenantId = cookieStore.get("currentOrganizationId")?.value ?? session?.tenantId;

    if (!tenantId) {
      return new NextResponse("No tenant selected", { status: 400 });
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
        tenantId: tenantId,
      },
    });

    if (!membership) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // Get all accessible shared threads
    const privateThreadsWhere = {
      tenantId: tenantId,
      isPublic: false,
      OR: [
        { createdById: session.userId },
        { chat: { userId: session.userId } }
      ],
      // Exclude expired shares
      AND: [
        {
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        }
      ]
    };

    const publicThreadsWhere = {
      tenantId: tenantId,
      isPublic: true,
      // Exclude expired shares
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    };

    // Count private threads with unread activity
    // TODO: Implement actual unread logic based on notifications/comments
    const privateUnreadCount = 0; // Placeholder

    // Count public threads with unread activity
    // TODO: Implement actual unread logic based on notifications/comments
    const publicUnreadCount = 0; // Placeholder

    // Get total counts
    const totalPrivateThreads = await db.threadShare.count({
      where: privateThreadsWhere,
    });

    const totalPublicThreads = await db.threadShare.count({
      where: publicThreadsWhere,
    });

    return NextResponse.json({
      privateUnreadCount,
      publicUnreadCount,
      totalUnreadCount: privateUnreadCount + publicUnreadCount,
      totalPrivateThreads,
      totalPublicThreads,
      totalThreads: totalPrivateThreads + totalPublicThreads,
    });
  } catch (error) {
    console.error("[SHARED_THREADS_UNREAD_COUNT_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
