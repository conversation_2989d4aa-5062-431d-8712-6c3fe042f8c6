"use client";

import * as React from "react";
import {
  Folder,
  Plus,
  ChevronRight,
  FileText,
  History,
  Users,
} from "lucide-react";

// Import removed since we're implementing custom scrolling
import { GroupMenuItem, NavProjects } from "@/components/layouts/nav-projects";
import { NavSecondary } from "@/components/layouts/nav-secondary";
import { NavUser } from "@/components/layouts/nav-user";
import {
  Sidebar,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/layouts/sidebar";
import { OrganizationSwitcher } from "@/components/organizations/organization-switcher";
import { Button } from "../ui/button";
import { CreateGroupDialog } from "../model/create-group-dialog";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { updateChat } from "@/services";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

// Default workspaces - adding more to test scrolling

const data = {
  user: {
    name: "<PERSON><PERSON> ",
    role: "Admin",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [],
  navSecondary: [
    {
      title: "Help",
      url: `${process.env.NEXT_PUBLIC_API_BASE_URL}/docs/en`,
      icon: FileText,
      translationKey: "sidebar.help",
    },
  ],
  projects: [
    {
      name: "Shared Threads",
      url: "/shared-threads",
      icon: Users,
      translationKey: "sidebar.sharedThreads",
    },
  ],
};

export function MemberAppSidebar({
  session,
  group,
  chatHistory,
  tenantId,
  ...props
}: any) {
  const [openDropdown, setOpenDropdown] = React.useState<number | null>(null);

  const [editingIndex, setEditingIndex] = React.useState<number | null>(null);
  const [editingName, setEditingName] = React.useState("");

  const handleStartEditing = (index: number, name: string) => {
    setEditingIndex(index);
    setEditingName(name);
    setOpenDropdown(null);
  };

  const handleNameSubmit = async (chat: any) => {
    if (editingName.trim() === "") return;

    try {
      const response = await updateChat({
        id: chat.id,
        title: editingName,
      });
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chat.renameSuccess"));
        window.location.reload();
      }
    } catch (error) {
      toast.error(t("chat.renameFailed"));
    } finally {
      setEditingIndex(null);
    }
  };

  const pathname = usePathname() || "";
  const router = useRouter();
  const { t } = useLanguage();
  const [hoveredProject, setHoveredProject] = React.useState<number | null>(
    null
  );
  const user = { ...session?.user, role: session?.memberships?.[0]?.role };
  const [groups] = React.useState(() => {
    // Fall back to group prop if no temporary data
    return (
      group?.map((g: any) => ({
        id: g.id,
        title: g.name,
        icon: Folder,
        isActive: false,
        items: g?.chats?.map((chat: any) => ({
          id: chat.id,
          title: chat.title,
          url: `/ask-ai/${chat.id}/`,
        })),
      })) || []
    );
  });

  // State to track expanded groups
  const [expandedGroups, setExpandedGroups] = React.useState<
    Record<string, boolean>
  >({});

  // State to track group chats (using only the setter)
  const [, setGroupChats] = React.useState<Record<string, any[]>>({});

  // Toggle group expansion
  const toggleGroupExpansion = (groupName: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupName]: !prev[groupName],
    }));
  };

  React.useEffect(() => {
    const group =
      groups?.find(
        (g: any) => g?.items?.find((c: any) => c?.url === pathname)
      ) || [];
    setExpandedGroups((prev) => ({
      ...prev,
      [group?.title]: true,
    }));
  }, [groups]);

  // Check if a group or its chat is active
  const isGroupActive = (groupId: string) => {
    return pathname.includes(`/ask-ai/${groupId}`);
  };

  // Check if a specific chat is active
  const isChatActive = (chatId: string) => {
    return pathname === `/ask-ai/${chatId}/`;
  };

  // Initialize chats from the group prop
  React.useEffect(() => {
    if (groups) {
      const newGroupChats: Record<string, any[]> = {};

      groups.forEach((g: any) => {
        if (g.id && g.chats) {
          newGroupChats[g.id] = g.chats.map((chat: any) => ({
            id: chat.id,
            name: chat.name,
            icon: Folder,
            type: "chat",
            createdAt: chat.createdAt,
          }));

          // Auto-expand group if it's active
          if (isGroupActive(g.id)) {
            setExpandedGroups((prev) => ({
              ...prev,
              [g.id]: true,
            }));
          }
        }
      });

      setGroupChats(newGroupChats);
    }
  }, [groups, pathname]); // Added pathname as dependency

  return (
    <Sidebar
      className="top-[--header-height] !h-[calc(100svh-var(--header-height))]"
      {...props}
    >
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="px-2">
              <OrganizationSwitcher isAdmin={false} />
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <div className="flex min-h-0 flex-1 flex-col gap-2 p-2 overflow-hidden">
        <a href="/ask-ai" className="shrink-0 mb-2">
          <Button
            variant="secondary"
            size="sm"
            className="w-full justify-between rounded-full px-4"
          >
            {t("memberSidebar.newChat")}
            <Plus className="h-4 w-4 " />
          </Button>
        </a>{" "}
        <div className="shrink-0">
          <NavProjects projects={data.projects} />
        </div>
        <div className="flex items-center justify-between px-2 py-1 shrink-0">
          <div className="text-xs font-medium text-sidebar-foreground/70">
            {t("memberSidebar.chatHistory")}
          </div>

          <CreateGroupDialog
            trigger={
              <Button size="icon" variant="ghost">
                <Plus className="h-4 w-4" />
                <span className="sr-only">
                  {t("memberSidebar.createGroup")}
                </span>
              </Button>
            }
            tenantId={tenantId}
          />
        </div>
        <div className="overflow-y-auto flex w-full h-full min-w-0 flex-col">
          <ul className="flex w-full min-w-0 flex-col gap-1">
            {group.map((item: any) => {
              console.log("item",item)
              const groupName = item.name;
              const isActive = isGroupActive(item.id);
              const isExpanded = expandedGroups[groupName];
              const chats =
                groups.find((g: { id: string }) => g.id === item.id)?.items ||
                [];

              return (
                <li key={item.title} className="group/menu-item relative">
                  <div className="flex items-center">
                    <div className="flex w-full">
                      <div
                        className={`flex flex-1 items-center gap-2 overflow-hidden rounded-l-md p-2 text-left text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                          isActive
                            ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                            : ""
                        }`}
                      >
                        <div
                          onClick={() => toggleGroupExpansion(groupName)}
                          className="flex-1 overflow-hidden"
                        >
                          <span
                            className="overflow-hidden text-ellipsis whitespace-nowrap w-full inline-block"
                            title={item.name}
                          >
                            {item.name}
                          </span>
                        </div>
                      </div>
                      <button
                        onClick={() => toggleGroupExpansion(groupName)}
                        className={`flex items-center overflow-hidden rounded-r-md p-2 text-left text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                          isActive
                            ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                            : ""
                        }`}
                      >
                        <ChevronRight
                          className={`size-4 shrink-0 transition-transform ${
                            isExpanded ? "rotate-90" : ""
                          }`}
                        />
                      </button>
                    </div>
                  </div>

                  {isExpanded && (
                    <ul className="ml-6 mt-1 space-y-1">
                      {chats.length > 0 &&
                        chats.map((chat: any, chatIndex: number) => {
                          const chatUrl = `/ask-ai/${item.id}`;
                          const chatIsActive = isChatActive(chat?.id);

                          return (
                            <li
                              onMouseEnter={() => {
                                setHoveredProject(chatIndex);
                              }}
                              onMouseLeave={() => setHoveredProject(null)}
                              key={chat.id}
                              className="flex items-center gap-2"
                            >
                              <Link
                                href={chat.url || chatUrl}
                                className={`flex items-center gap-2 rounded-md py-1.5 px-2 text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                                  chatIsActive
                                    ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                    : ""
                                } w-full`}
                              >
                                <History className="size-3.5 shrink-0" />
                                <div className="w-full overflow-hidden">
                                  {editingIndex === chatIndex ? (
                                    <input
                                      type="text"
                                      value={editingName}
                                      onChange={(e) =>
                                        setEditingName(e.target.value)
                                      }
                                      onBlur={() => handleNameSubmit(chat)}
                                      onKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          handleNameSubmit(chat);
                                        } else if (e.key === "Escape") {
                                          setEditingIndex(null);
                                        }
                                      }}
                                      className="bg-transparent border-none outline-none w-full max-w-[185px] text-sm focus:ring-2 focus:ring-primary focus-visible:ring-2 focus-visible:ring-primary"
                                      autoFocus={true}
                                    />
                                  ) : (
                                    <span
                                      className="overflow-hidden text-ellipsis whitespace-nowrap w-full inline-block"
                                      title={chat?.title}
                                    >
                                      {chat?.title}
                                    </span>
                                  )}
                                </div>
                              </Link>

                              <GroupMenuItem
                                router={router}
                                index={chatIndex}
                                item={{
                                  ...chat,
                                  name: chat?.title,
                                }}
                                openDropdown={openDropdown}
                                setOpenDropdown={setOpenDropdown}
                                hoveredProject={hoveredProject}
                                showMore={true}
                                groups={groups}
                                handleStartEditing={handleStartEditing}
                              />
                            </li>
                          );
                        })}
                    </ul>
                  )}
                </li>
              );
            })}
          </ul>

          <div className="h-full">
            <div className="relative flex w-full min-w-0 flex-col">
              <ul className="flex w-full min-w-0 flex-col gap-1">
                <NavProjects
                  showMore={true}
                  groups={group}
                  projects={chatHistory?.map((item: any) => ({
                    id: item?.id,
                    name: item?.name,
                    url: item?.url,
                    icon: History,
                  }))}
                />
              </ul>
            </div>
          </div>
        </div>
        <div className="mx-2 h-px bg-sidebar-border shrink-0" />
        <div className="shrink-0 mt-auto">
          <NavSecondary items={data.navSecondary} />
        </div>
      </div>
      <SidebarFooter>
        <NavUser
          user={{
            name: user?.name ?? user?.email,
            role: user?.role,
            avatar: user?.image,
          }}
        />
      </SidebarFooter>
    </Sidebar>
  );
}
