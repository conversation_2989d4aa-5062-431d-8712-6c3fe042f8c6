import { importKeychain, serializeKey } from "@47ng/cloak";
import { PrismaClient } from "@prisma/client";
import { fieldEncryptionExtension } from "prisma-field-encryption";

let db: any;

const CLOAK_MASTER_KEY = process.env.NEXT_ENCRYPTION_CLOAK_KEY || "";
const CLOAK_KEYCHAIN = process.env.NEXT_ENCRYPTION_CLOAK_KEYCHAIN || "";

const keychain = await importKeychain(CLOAK_KEYCHAIN, CLOAK_MASTER_KEY);

if (process.env.NODE_ENV === "production") {
  db = new PrismaClient();
} else {
  if (!(global as any).prisma) {
    (global as any).prisma = new PrismaClient();
  }
  db = (global as any).prisma;
}

const decryptionKeys = await Promise.all(
  Object.values(keychain).map(({ key }) => serializeKey(key))
);

const extensionB = fieldEncryptionExtension({
  encryptionKey: CLOAK_MASTER_KEY,
  decryptionKeys,
});

db = db.$extends(extensionB);

export default db;
