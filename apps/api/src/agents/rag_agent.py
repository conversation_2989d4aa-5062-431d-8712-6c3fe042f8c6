import os
from typing import Dict, List, Optional, Any, Union
import numpy as np
from pymongo import MongoClient
import json
from bson import ObjectId
import requests
from src.utils.robust_citation_enhancer import RobustCitationEnhancer
from src.utils.intelligent_query_interpreter import query_interpreter
from PIL import Image
import base64
import io
from .web_search import WebSearchClient, format_web_sources, enhance_prompt_with_web_results

from langchain_openai import AzureOpenAIEmbeddings
from langchain_community.embeddings import SentenceTransformerEmbeddings
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.embeddings import OpenAIEmbeddings
from langchain_core.embeddings import Embeddings
from .azure_vision_embeddings import AzureVisionEmbeddings
import datetime

# Custom JSON encoder to handle non-serializable objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        # Add more custom type handling as needed
        return super().default(obj)

# Import the markitdown converter
from src.utils.markitdown_converter import MarkitdownConverter


from langchain_community.vectorstores import Chroma

from langchain_community.vectorstores import ElasticsearchStore

from langchain_community.vectorstores import FAISS

from langchain_community.vectorstores import Milvus

from langchain_community.vectorstores import MongoDBAtlasVectorSearch

from langchain_community.vectorstores import OpenSearchVectorSearch

from langchain_community.vectorstores import Pinecone

from langchain_community.vectorstores import Qdrant

from langchain_community.vectorstores import Weaviate


from langchain_community.document_loaders import CSVLoader

from langchain_community.document_loaders import DirectoryLoader

from langchain_community.document_loaders import PyPDFLoader

from langchain_community.document_loaders import TextLoader

from langchain_community.document_loaders import UnstructuredMarkdownLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.chains import RetrievalQA
from langchain_deepseek import ChatDeepSeek

from langchain.retrievers import EnsembleRetriever
from langchain_community.llms import AzureOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_community.llms import HuggingFacePipeline

from langchain_community.llms import LlamaCpp

from langchain_community.llms import OpenAI

from langchain_community.chat_models import ChatOpenAI

from langchain_community.chat_models import AzureChatOpenAI

from langchain_community.chat_models import ChatAnthropic
from langchain_core.prompts import PromptTemplate
from langchain_core.language_models import LLM
import logging


from langchain.schema import BaseRetriever
from langchain.schema.document import Document
import numpy as np
from typing import List
# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiWorkspaceRetriever(BaseRetriever):
    # Define instance variables in the class

    def __init__(self, retrievers, top_k):
        super().__init__()
        # Ensure retrievers is properly initialized
        self.retrievers = retrievers
        self.top_k = top_k
        self.logger = logging.getLogger(__name__)

    def get_relevant_documents(self, query):
        """
        Deprecated method. Use invoke() instead.
        """
        return self.invoke(query)

    def invoke(self, query):
        # Check if this is a simple greeting that doesn't need document retrieval
        if RobustCitationEnhancer._is_simple_greeting(query):
            self.logger.info(f"Simple greeting detected: '{query}'. Skipping document retrieval.")
            return []

        all_docs = []

        # Log the number of retrievers
        self.logger.info(f"Querying {len(self.retrievers)} workspace retrievers")

        # Get documents from each retriever
        for i, retriever in enumerate(self.retrievers):
            try:
                self.logger.info(f"Getting documents from retriever {i+1}")
                # Use invoke method instead of get_relevant_documents
                docs = retriever.invoke(query)
                self.logger.info(f"Retriever {i+1} returned {len(docs)} documents")

                # Add workspace identifier to help debug which retriever returned which docs
                for doc in docs:
                    if 'workspace_index' not in doc.metadata:
                        doc.metadata['workspace_index'] = i

                all_docs.extend(docs)
            except Exception as e:
                self.logger.error(f"Error retrieving from workspace {i+1}: {str(e)}")

        # Log total number of documents found
        self.logger.info(f"Total documents found across all retrievers: {len(all_docs)}")

        # If no documents were found, return empty list
        if not all_docs:
            self.logger.warning("No documents found across any retrievers")
            return []

        # Try to sort and filter by score if available
        try:
            # Different vector stores use different metadata keys for scores
            # Try common score keys and use the first one that works
            score_keys = ['score', 'similarity', 'relevance_score', 'distance']
            score_key_found = False

            for key in score_keys:
                if all(key in doc.metadata for doc in all_docs):
                    # Check if this is a distance metric (lower is better) or similarity metric (higher is better)
                    is_distance_metric = key == 'distance'

                    # Sort by the score (respecting whether higher or lower is better)
                    all_docs.sort(
                        key=lambda doc: doc.metadata[key],
                        reverse=not is_distance_metric  # Reverse for similarity, don't reverse for distance
                    )

                    # Apply a threshold to filter out low-quality matches
                    if len(all_docs) > 0:
                        # Get the best score
                        best_score = all_docs[0].metadata[key]

                        # For distance metrics, lower is better, so we filter out docs with scores > threshold * best
                        # For similarity metrics, higher is better, so we filter out docs with scores < threshold * best
                        threshold_factor = 3.0 if is_distance_metric else 0.5

                        if is_distance_metric:
                            # Filter out documents with distance > threshold * best_distance
                            filtered_docs = [doc for doc in all_docs if doc.metadata[key] <= threshold_factor * best_score]
                        else:
                            # Filter out documents with similarity < threshold * best_similarity
                            filtered_docs = [doc for doc in all_docs if doc.metadata[key] >= threshold_factor * best_score]

                        # Only apply filtering if we don't lose too many documents
                        if len(filtered_docs) >= min(3, len(all_docs)):
                            all_docs = filtered_docs
                            self.logger.info(f"Filtered documents by {key} threshold, keeping {len(all_docs)} docs")

                    self.logger.info(f"Sorted documents by {key}")
                    score_key_found = True
                    break

            # If no standard score key was found, apply our own relevance scoring
            if not score_key_found and len(all_docs) > 0:
                self.logger.info("No standard score key found, applying custom relevance scoring")

                # Extract query terms for basic relevance calculation
                query_terms = [term.lower() for term in query.split() if len(term) > 3]

                if query_terms:
                    # Calculate a basic relevance score for each document
                    for doc in all_docs:
                        content = doc.page_content.lower()
                        # Count term occurrences and normalize by content length
                        term_count = sum(content.count(term) for term in query_terms)
                        doc.metadata['custom_relevance'] = term_count / (len(content) * 0.01)

                    # Sort by our custom relevance score
                    all_docs.sort(key=lambda doc: doc.metadata['custom_relevance'], reverse=True)

                    # Filter out documents with very low relevance
                    if len(all_docs) > 0:
                        best_score = all_docs[0].metadata['custom_relevance']
                        filtered_docs = [doc for doc in all_docs if doc.metadata['custom_relevance'] >= 0.3 * best_score]

                        # Only apply filtering if we don't lose too many documents
                        if len(filtered_docs) >= min(3, len(all_docs)):
                            all_docs = filtered_docs
                            self.logger.info(f"Filtered documents by custom relevance, keeping {len(all_docs)} docs")
        except Exception as e:
            self.logger.warning(f"Could not sort/filter documents by score: {e}")

        # Return top documents (limited by top_k)
        result_docs = all_docs[:self.top_k]
        self.logger.info(f"Returning top {len(result_docs)} documents")

        return result_docs



class CustomEmbeddingModel(Embeddings):
    """A wrapper class for custom embedding models that don't have a LangChain integration."""

    def __init__(self, model_callable, dimension: int = 1536):
        """
        Initialize with a callable that generates embeddings.

        Args:
            model_callable: A function that takes a list of texts and returns a list of embeddings
            dimension: The dimensionality of the embeddings
        """
        self.model_callable = model_callable
        self.dimension = dimension

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents."""
        return self.model_callable(texts)

    def embed_query(self, text: str) -> List[float]:
        """Embed a query."""
        return self.model_callable([text])[0]

class AdvancedRAGAgent:
    """
    An advanced RAG agent capable of connecting with multiple vector storage solutions,
    handling different document types, and providing optimized retrieval results with
    customizable LLM and embedding models.
    """

    def __init__(
        self,
        vector_store_type: str = "chroma",
        embedding_model: Union[str, Dict, Embeddings] = "openai",
        llm_model: Union[str, Dict, LLM] = "gpt-3.5-turbo",
        temperature: float = 0.0,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        top_k: int = 4,
        api_keys: Optional[Dict[str, str]] = None,
        vector_store_config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the RAG agent with highly customizable components.

        Args:
            vector_store_type: Type of vector store to use (chroma, faiss, pinecone, etc.)
            embedding_model: Model to use for embeddings - can be:
                - string: "openai", "huggingface", "azure", etc.
                - dict: {"type": "huggingface", "model_name": "sentence-transformers/all-mpnet-base-v2", ...}
                - Embeddings: A pre-initialized LangChain embeddings object
            llm_model: Language model to use for generation - can be:
                - string: "gpt-3.5-turbo", "gpt-4", "claude-3-opus", "deepseek-chat", etc.
                - dict: {"type": "openai", "model_name": "gpt-4", "api_key": "...", ...}
                - LLM: A pre-initialized LangChain LLM object
            temperature: Temperature for generation (default when using string/dict config)
            chunk_size: Size of document chunks
            chunk_overlap: Overlap between chunks
            top_k: Number of documents to retrieve
            api_keys: Dictionary of API keys for different services
            vector_store_config: Additional configuration for the vector store
        """
        self.vector_store_type = vector_store_type.lower()
        self.embedding_model_config = embedding_model
        self.llm_model_config = llm_model
        self.temperature = temperature
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.top_k = top_k
        self.api_keys = api_keys or {}
        self.vector_store_config = vector_store_config or {}

        # Set API keys if provided
        # Set API keys in environment variables if provided
        api_key_mapping = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY",
            "azure": "AZURE_OPENAI_API_KEY",
            "deepseek": "DEEPSEEK_API_KEY"
        }

        for service, env_var in api_key_mapping.items():
            if self.api_keys.get(env_var):
                os.environ[env_var] = self.api_keys[env_var]

        # Initialize components
        self.embedding_model = self._initialize_embedding_model()
        self.llm = self._initialize_llm()
        self.vector_store = None
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        self.qa_chain = None

        logger.info(f"Initialized RAG agent with {vector_store_type}")

    def _initialize_embedding_model(self):
        """Initialize the embedding model based on the selected configuration."""

        # If already a LangChain embeddings object, return it
        if isinstance(self.embedding_model_config, Embeddings):
            logger.info("Using pre-initialized embedding model")
            return self.embedding_model_config

        # Parse configuration if it's a dictionary
        if isinstance(self.embedding_model_config, dict):
            model_type = self.embedding_model_config.get("type", "openai").lower()
            model_name = self.embedding_model_config.get("model_name")
            api_key = self.embedding_model_config.get("api_key",
                                                     self.api_keys.get(model_type))
        else:
            # Assume it's a string
            model_type = self.embedding_model_config.lower()
            model_name = None
            api_key = self.api_keys.get(model_type)
        # Initialize based on the model type
        if model_type == "openai":
            logger.info("Initializing OpenAI embeddings")
            return OpenAIEmbeddings(
                model=model_name or "embed-v-4-0",
                api_key=api_key
            )

        elif model_type == "azure":
            if not isinstance(self.embedding_model_config, dict):
                raise ValueError("Azure OpenAI embeddings require a dictionary configuration")

            # Required Azure-specific parameters
            azure_endpoint = self.embedding_model_config.get("azure_endpoint")
            if not azure_endpoint:
                raise ValueError("Azure OpenAI embeddings require azure_endpoint")

            deployment_name = self.embedding_model_config.get("deployment_name")
            if not deployment_name:
                raise ValueError("Azure OpenAI embeddings require deployment_name")

            api_version = self.embedding_model_config.get("api_version", "2024-02-15-preview")

            logger.info(f"Initializing Azure OpenAI embeddings with deployment {deployment_name}")

            return AzureOpenAIEmbeddings(
                model=model_name or "embed-v-4-0",
                api_key=api_key,
                deployment=deployment_name,
                azure_endpoint=azure_endpoint,
                openai_api_version=api_version,
            )

        elif model_type == "azure_vision":
            if not isinstance(self.embedding_model_config, dict):
                raise ValueError("Azure Vision embeddings require a dictionary configuration")

            # Required Azure Vision-specific parameters
            azure_endpoint = self.embedding_model_config.get("azure_endpoint")
            if not azure_endpoint:
                raise ValueError("Azure Vision embeddings require azure_endpoint")

            deployment_name = self.embedding_model_config.get("deployment_name")
            if not deployment_name:
                raise ValueError("Azure Vision embeddings require deployment_name")

            api_version = self.embedding_model_config.get("api_version", "2023-12-01-preview")

            logger.info(f"Initializing Azure Vision embeddings with deployment {deployment_name}")

            return AzureVisionEmbeddings(
                azure_endpoint=azure_endpoint,
                api_key=api_key,
                deployment_name=deployment_name,
                api_version=api_version,
                model=model_name or "embed-v-4-0"
            )

        elif model_type == "huggingface" or model_type == "hf":
            model_name = model_name or "sentence-transformers/all-mpnet-base-v2"
            logger.info(f"Initializing HuggingFace embeddings with {model_name}")

            if isinstance(self.embedding_model_config, dict):
                # Get additional parameters from config
                hf_params = {k: v for k, v in self.embedding_model_config.items()
                            if k not in ["type", "model_name", "api_key"]}

                return HuggingFaceEmbeddings(
                    model_name=model_name,
                    **hf_params
                )
            else:
                return HuggingFaceEmbeddings(model_name=model_name)

        elif model_type == "custom":
            if not isinstance(self.embedding_model_config, dict) or "callable" not in self.embedding_model_config:
                raise ValueError("Custom embedding model requires a dictionary with a 'callable' function")

            callable_fn = self.embedding_model_config["callable"]
            dimension = self.embedding_model_config.get("dimension", 1536)

            logger.info("Initializing custom embedding model")
            return CustomEmbeddingModel(callable_fn, dimension)

        else:
            raise ValueError(f"Unsupported embedding model type: {model_type}")

    def _initialize_llm(self):
        """Initialize the language model based on the selected configuration."""

        # If already a LangChain LLM object, return it
        if isinstance(self.llm_model_config, LLM):
            logger.info("Using pre-initialized LLM")
            return self.llm_model_config

        # Parse configuration if it's a dictionary
        if isinstance(self.llm_model_config, dict):
            model_type = self.llm_model_config.get("type", "openai").lower()
            model_name = self.llm_model_config.get("model_name")

            api_key = self.llm_model_config.get("api_key",
                                              self.api_keys.get(model_type))
            temperature = self.llm_model_config.get("temperature", self.temperature)
        else:
            # Assume it's a string
            if "gpt" in self.llm_model_config.lower() or "text-davinci" in self.llm_model_config.lower():
                model_type = "openai"
            elif "claude" in self.llm_model_config.lower():
                model_type = "anthropic"
            elif "llama" in self.llm_model_config.lower():
                model_type = "llamacpp"
            elif "deepseek" in self.llm_model_config.lower():
                model_type = "DeepSeek-R1"
            else:
                model_type = "openai"  # Default to OpenAI

            model_name = self.llm_model_config
            api_key = self.api_keys.get(model_type)
            temperature = self.temperature

        # Initialize based on the model type
        if model_type == "openai":
            logger.info(f"Initializing OpenAI model: {model_name}")

            if model_name and ("gpt-3.5" in model_name or "gpt-4" in model_name):
                return ChatOpenAI(
                    model_name=model_name,
                    temperature=temperature,
                    api_key=api_key
                )
            else:
                return OpenAI(
                    model_name=model_name or "text-davinci-003",
                    temperature=temperature,
                    api_key=api_key
                )

        elif model_type == "azure":
            if not isinstance(self.llm_model_config, dict):
                raise ValueError("Azure OpenAI requires a dictionary configuration")

            # Required Azure-specific parameters
            azure_endpoint = self.llm_model_config.get("azure_endpoint")
            if not azure_endpoint:
                raise ValueError("Azure OpenAI requires azure_endpoint")

            deployment_name = self.llm_model_config.get("deployment_name")
            if not deployment_name:
                raise ValueError("Azure OpenAI requires deployment_name")

            api_version = self.llm_model_config.get("api_version", "2023-05-15")
            model_name = self.llm_model_config.get("model_name", "")

            logger.info(f"Initializing Azure OpenAI with deployment {deployment_name}")

            # Check if it's a chat model or completion model based on deployment name pattern
            if "gpt-3.5" in deployment_name.lower() or "gpt-4" in deployment_name.lower() or "chat" in deployment_name.lower() or "deepseek" in deployment_name.lower():
                return AzureChatOpenAI(
                    deployment_name=deployment_name,
                    azure_endpoint=azure_endpoint,
                    api_version=api_version,
                    api_key=api_key,
                    temperature=temperature,
                    model_name=model_name,
                )
            else:
                return AzureOpenAI(
                    deployment_name=deployment_name,
                    azure_endpoint=azure_endpoint,
                    api_version=api_version,
                    api_key=api_key,
                    temperature=temperature
                )

        elif model_type == "anthropic":
            logger.info(f"Initializing Anthropic Claude model: {model_name}")

            return ChatAnthropic(
                model_name=model_name or "claude-3-sonnet-20240229",
                temperature=temperature,
                api_key=api_key
            )

        elif model_type == "deepseek":
            logger.info(f"Initializing DeepSeek model: {model_name} {api_key}")

            azure_endpoint = self.llm_model_config.get("azure_endpoint")
            # Check if it's a chat model based on the model name

            return ChatDeepSeek(
                api_base=azure_endpoint,
                model_name=model_name or "DeepSeek-R1",
                temperature=temperature,
                api_key=api_key or os.getenv("DEEPSEEK_API_KEY")
            )

        elif model_type == "huggingface" or model_type == "hf":
            if not isinstance(self.llm_model_config, dict) or not model_name:
                raise ValueError("HuggingFace pipeline requires a model_name")

            logger.info(f"Initializing HuggingFace pipeline with {model_name}")

            # Extract additional parameters if any
            hf_params = {k: v for k, v in self.llm_model_config.items()
                       if k not in ["type", "model_name", "api_key", "temperature"]}

            try:
                from transformers import pipeline

                pipe = pipeline(
                    "text-generation",
                    model=model_name,
                    temperature=temperature,
                    **hf_params
                )

                return HuggingFacePipeline(pipeline=pipe)
            except ImportError:
                raise ImportError("HuggingFace transformers not installed. Install with: pip install transformers")

        elif model_type == "llamacpp":
            if not isinstance(self.llm_model_config, dict) or not self.llm_model_config.get("model_path"):
                raise ValueError("LlamaCpp requires a model_path in the configuration")

            model_path = self.llm_model_config.get("model_path")
            logger.info(f"Initializing LlamaCpp with model at {model_path}")

            # Extract additional parameters if any
            llama_params = {k: v for k, v in self.llm_model_config.items()
                         if k not in ["type", "model_name", "api_key", "temperature", "model_path"]}

            try:
                return LlamaCpp(
                    model_path=model_path,
                    temperature=temperature,
                    **llama_params
                )
            except ImportError:
                raise ImportError("LlamaCpp not installed. Install with: pip install llama-cpp-python")

        elif model_type == "custom":
            if not isinstance(self.llm_model_config, dict) or "callable" not in self.llm_model_config:
                raise ValueError("Custom LLM requires a dictionary with a 'callable' function")

            from langchain_core.language_models import BaseLLM

            class CustomLLM(BaseLLM):
                """Custom LLM wrapper for arbitrary callable models."""

                def __init__(self, model_callable, **kwargs):
                    super().__init__(**kwargs)
                    self.model_callable = model_callable

                def _call(self, prompt, stop=None, **kwargs):
                    return self.model_callable(prompt, stop=stop)

                @property
                def _llm_type(self):
                    return "custom"

            logger.info("Initializing custom LLM")
            return CustomLLM(self.llm_model_config["callable"])

        else:
            raise ValueError(f"Unsupported LLM type: {model_type}")

    def _initialize_vector_store(self, documents=None, collection_name=None):
        """Initialize the vector store based on the selected type."""
        # For MongoDB, we don't need documents or persist_directory to connect to existing store
        if documents is None and "persist_directory" not in self.vector_store_config:
            if self.vector_store_type == "mongodb":
                # For MongoDB, we can proceed without documents or persist_directory
                if "connection_string" in self.vector_store_config and "db_name" in self.vector_store_config and "namespace" in self.vector_store_config:
                    logger.info("Connecting to existing MongoDB store without documents")
                else:
                    logger.warning("MongoDB connection requires connection_string, db_name, and namespace")
                    return None
            else:
                logger.warning("No documents provided and no persist directory specified")
                return None

        store_params = {"embedding": self.embedding_model}
        store_params.update(self.vector_store_config)

        # Handle MongoDB Atlas Vector Search specific configuration
        if self.vector_store_type == "mongodb":
            if "namespace" not in store_params:
                raise ValueError("MongoDB requires collection_name in vector_store_config")
            if "connection_string" not in store_params:
                raise ValueError("MongoDB requires connection_string in vector_store_config")
            if "db_name" not in store_params:
                raise ValueError("MongoDB requires db_name in vector_store_config")
            client = MongoClient(store_params["connection_string"])
            db = client[store_params["db_name"]]
            collection = db[collection_name or store_params["namespace"]]
            # Ensure index name is set for vector search
            if "index_name" not in store_params:
                store_params["index_name"] = "default_vector_index"

            param={
                "index_name": store_params["index_name"],
                "text_key": store_params.get("text_key", "content"),
                "embedding_key": store_params.get("embedding_key", "embedding"),
                "collection" :collection,
                "embedding": self.embedding_model,
            }

            # Initialize MongoDB vector store
            if documents:
                return MongoDBAtlasVectorSearch.from_documents(
                    documents=documents,
                    **param
                )
            else:
                return MongoDBAtlasVectorSearch(**param)

        if self.vector_store_type == "chroma":
            if documents:
                return Chroma.from_documents(documents=documents, **store_params)
            else:
                return Chroma(**store_params)

        elif self.vector_store_type == "faiss":
            if documents:
                return FAISS.from_documents(documents=documents, **store_params)
            else:
                raise ValueError("FAISS requires documents for initialization")

        elif self.vector_store_type == "pinecone":
            if "pinecone_api_key" not in store_params and "api_key" not in store_params:
                store_params["api_key"] = self.api_keys.get("pinecone")
            if "index_name" not in store_params:
                raise ValueError("Pinecone requires an index_name in vector_store_config")

            if documents:
                return Pinecone.from_documents(documents=documents, **store_params)
            else:
                return Pinecone(**store_params)

        elif self.vector_store_type == "weaviate":
            if documents:
                return Weaviate.from_documents(documents=documents, **store_params)
            else:
                return Weaviate(**store_params)

        elif self.vector_store_type == "milvus":
            if documents:
                return Milvus.from_documents(documents=documents, **store_params)
            else:
                return Milvus(**store_params)

        elif self.vector_store_type == "qdrant":
            if documents:
                return Qdrant.from_documents(documents=documents, **store_params)
            else:
                return Qdrant(**store_params)

        elif self.vector_store_type == "opensearch":
            if documents:
                return OpenSearchVectorSearch.from_documents(documents=documents, **store_params)
            else:
                return OpenSearchVectorSearch(**store_params)

        elif self.vector_store_type == "elasticsearch":
            if documents:
                return ElasticsearchStore.from_documents(documents=documents, **store_params)
            else:
                return ElasticsearchStore(**store_params)

        else:
            raise ValueError(f"Unsupported vector store type: {self.vector_store_type}")

    def load_documents(self, source_path: str, source_type: str = "auto"):
        """
        Load documents from a file or directory.

        Args:
            source_path: Path to the file or directory
            source_type: Type of documents to load (auto, pdf, text, csv, markdown, directory)

        Returns:
            List of loaded and processed documents
        """
        logger.info(f"Loading documents from {source_path}")

        # Detect source type if set to auto
        if source_type == "auto":
            if os.path.isdir(source_path):
                source_type = "directory"
            else:
                ext = os.path.splitext(source_path)[1].lower()
                # Check if we should use markitdown for this file type
                markitdown_converter = MarkitdownConverter()
                supported_extensions = markitdown_converter.get_supported_extensions()

                if ext in supported_extensions:
                    source_type = "markitdown"
                elif ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",]:
                    source_type = "image"
                elif ext == ".pdf":
                    source_type = "pdf"
                elif ext == ".txt":
                    source_type = "text"
                elif ext == ".csv":
                    source_type = "csv"
                elif ext == ".md" or ext == ".markdown":
                    source_type = "markdown"
                else:
                    source_type = "text"  # Default to text

        # Load documents based on type
        if source_type == "markitdown":
            # Use the markitdown converter for enhanced document processing
            markitdown_converter = MarkitdownConverter()
            documents = markitdown_converter.convert_file(source_path)
            logger.info(f"Loaded document with Markitdown: {source_path}")
        elif source_type == "image":
            try:
                # Create a prompt for image description
                # Load the image
                img = Image.open(source_path)

                # Convert image to base64 for API calls if needed
                buffered = io.BytesIO()
                img.save(buffered, format=img.format or "JPEG")
                img_str = base64.b64encode(buffered.getvalue()).decode()
                img_format = (img.format or "png").lower()

                # Configure the client based on the model type
                endpoint = f"{os.getenv('AZURE_VISION_API_ENDPOINT')}/openai/deployments/{os.getenv('AZURE_VISION_MODEL')}/chat/completions?api-version=2024-02-15-preview"
                headers = {
                    "Content-Type": "application/json",
                    "api-key": os.getenv("DEEPSEEK_API_KEY")
                }
                messages = [
                    {"role": "system", "content":[
                        {"type": "text", "text": "You are an AI assistant that provides detailed descriptions of images."}
                    ]},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "Provide a detailed description of this image. Include all visible elements, text, and context."},
                            {"type": "image_url", "image_url": {"url":f"data:image/{img_format};base64,{img_str}"}}
                        ]
                    }
                ]

                data = {
                    "messages": messages,
                    "model": os.getenv('AZURE_VISION_MODEL',"mistral-medium-2505"),
                    "temperature": 0.7,
                }

                response = requests.post(endpoint, headers=headers, json=data)

                description = response.json()["choices"][0]["message"]["content"]
                from langchain.schema.document import Document
                documents = [
                    Document(
                        page_content=description,
                        metadata={
                            "source": source_path,
                            "file_type": "image",
                            "file_name": os.path.basename(source_path),
                            "description_type": "llm_generated"
                        }
                    )
                ]
            except Exception as llm_error:
                logger.error(f"Error generating image description with LLM: {llm_error}")
                # Create a minimal document with error information
                from langchain.schema.document import Document
                documents = [
                    Document(
                        page_content=f"This is an image file named {os.path.basename(source_path)}. The system was unable to extract detailed information from this image.",
                        metadata={
                            "source": source_path,
                            "file_type": "image",
                            "file_name": os.path.basename(source_path),
                            "description_type": "fallback"
                        }
                    )
                ]

        elif source_type == "directory":
            loader = DirectoryLoader(source_path, show_progress=True)
            documents = loader.load()
        elif source_type == "pdf":
            loader = PyPDFLoader(source_path)
            documents = loader.load()
        elif source_type == "text":
            loader = TextLoader(source_path)
            documents = loader.load()
        elif source_type == "csv":
            loader = CSVLoader(source_path)
            documents = loader.load()
        elif source_type == "markdown":
            loader = UnstructuredMarkdownLoader(source_path)
            documents = loader.load()
        else:
            raise ValueError(f"Unsupported document type: {source_type}")

        logger.info(f"Loaded {len(documents)} documents")

        split_docs = self.text_splitter.split_documents(documents)
        logger.info(f"Split into {len(split_docs)} chunks")

        return split_docs

    def index_documents(self, documents):
        """
        Index the provided documents in the vector store.

        Args:
            documents: List of documents to index

        Returns:
            The initialized vector store
        """
        logger.info(f"Indexing {len(documents)} documents")

        self.vector_store = self._initialize_vector_store(documents)

        # Initialize QA chain
        retriever = self.vector_store.as_retriever(search_kwargs={"k": self.top_k})

        # Create a custom prompt template for better retrieval
        template = """
            You are an AI assistant designed to answer user questions based on the provided context.

            Context:
            {context}

            Question:
            {question}

            Instructions:
            - Prioritize information from the provided context to answer the question.
            - If the context includes web search results, use this information to provide up-to-date answers about current events, news, or general knowledge questions.
            - When using web search results, cite your sources using the format [W1], [W2], etc. corresponding to the numbering in the web search results.
            - If the question is about recent events or current information and web search results are available, rely primarily on those results.
            - Always respond in the **same language** as the user's question, even if the context is in a different language. Translate the context internally as needed.
            - Format responses using Markdown for readability:
            - `Code blocks` for technical content
            - Use lists and headings for structure and clarity.

        """

        PROMPT = PromptTemplate(
            template=template,
            input_variables=["context", "question"]
        )

        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=retriever,
            chain_type_kwargs={"prompt": PROMPT}
        )

        logger.info("Indexing complete, QA chain initialized")
        return self.vector_store
    def _create_combined_retriever(self, retrievers):

        """
        Create a combined retriever that fetches from multiple sources and merges results.

        Args:
            retrievers: List of retrievers to combine

        Returns:
            A combined retriever
        """
        # Ensure retrievers is not empty
        if not retrievers or len(retrievers) == 0:
            logger.error("No retrievers provided to _create_combined_retriever")
            raise ValueError("No retrievers provided to create combined retriever")

        logger.info(f"Creating combined retriever with {len(retrievers)} retrievers")
        # Create and return the MultiWorkspaceRetriever with the retrievers
        return MultiWorkspaceRetriever(retrievers=retrievers, top_k=self.top_k)

    def connect_to_existing_store(self, workspace_ids=None):
        """
        Connect to existing vector stores for multiple workspaces.

        Args:
            workspace_ids: List of workspace IDs the user has access to
        """
        logger.info(f"Connecting to existing {self.vector_store_type} vector stores for workspaces: {workspace_ids}")

        if not workspace_ids:
            logger.error("No workspace IDs provided")
            return False

        # Create a list to store retrievers for each workspace
        retrievers = []

        for workspace_id in workspace_ids:
            collection_name = f"workspace_{workspace_id}"
            store_params = {"connection_string": self.vector_store_config["connection_string"],
                           "db_name": self.vector_store_config["db_name"],
                           "namespace": collection_name}
            client = MongoClient(store_params["connection_string"])
            db = client[store_params["db_name"]]
            collections=db.list_collection_names()
            if(collection_name not in collections):
                continue
            vector_store = self._initialize_vector_store(collection_name=collection_name)


            if vector_store:
                # Create a retriever for this workspace
                retriever = vector_store.as_retriever(search_kwargs={"k": self.top_k})
                retrievers.append(retriever)
                logger.info(f"Connected to vector store for workspace {workspace_id}")
            else:
                logger.warning(f"Failed to connect to vector store for workspace {workspace_id}")

        if not retrievers:
            logger.error("Failed to connect to any vector stores")
            return False

        # Create an enhanced prompt template optimized for conversation memory
        template = """You are an AI assistant designed to answer user questions based on the provided context and conversation history.

Context from Documents:
{context}

Current Question:
{question}

Conversation History:
{previous_message}

User Information:
- User Name: {user_name}
- Is First Message: {is_first_message}

CRITICAL INSTRUCTIONS FOR CONVERSATION MEMORY:
1. **CONVERSATION CONTINUITY**: If conversation history is provided, you MUST reference and build upon previous exchanges. Look for:
   - Follow-up questions (e.g., "Can you elaborate?", "What about...", "Tell me more")
   - References to previous topics ("the document you mentioned", "that solution", "the second point")
   - Pronouns referring to earlier content ("it", "that", "this", "they")

2. **CONTEXT AWARENESS**:
   - Parse the conversation history carefully - it may be in JSON format with structured turns
   - Identify what was previously discussed and maintain logical flow
   - If the user asks for clarification or expansion, refer back to your previous responses

3. **GREETING BEHAVIOR**:
   - If this is the first message (is_first_message = "true"), greet the user by name: "Hello {user_name}!"
   - For follow-up messages, do NOT repeat greetings - jump straight into answering

4. **INFORMATION PRIORITIZATION**:
   - Use document context to provide accurate, sourced information
   - If web search results are included, cite them as [W1], [W2], etc.
   - For recent events or current information, prioritize web results
   - If the question is unrelated to available context, politely explain limitations

5. **RESPONSE FORMATTING**:
   - Always respond in the same language as the user's question
   - Use clear Markdown formatting: **bold**, *italics*, `code`, lists, headings
   - Maintain professional but conversational tone
   - Be concise but comprehensive

Remember: Your ability to maintain conversation context is crucial for user experience. Always acknowledge and build upon previous exchanges when relevant."""

        PROMPT = PromptTemplate(
            template=template,
            input_variables=["context", "question", "user_name", "previous_message", "is_first_message"],
            partial_variables={"previous_message": "", "is_first_message": "true"}
        )
        # If you have multiple retrievers, you need to create a combined retriever
        if len(retrievers) == 1:
            combined_retriever = retrievers[0]  # Use a single retriever directly
        else:
            combined_retriever = EnsembleRetriever(retrievers=retrievers)

        # Initialize QA chain with the combined retriever
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=combined_retriever,
            chain_type_kwargs={"prompt": PROMPT}
        )

        logger.info("QA chain initialized with retrievers from multiple workspaces")
        return True

    def save_vector_store(self, path: str):
        """
        Save the vector store to disk (only supported by some vector stores).

        Args:
            path: Path to save the vector store

        Returns:
            Boolean indicating success
        """
        if not self.vector_store:
            raise ValueError("Vector store not initialized. Please index documents first.")

        if self.vector_store_type in ["chroma", "faiss"]:
            if self.vector_store_type == "chroma":
                self.vector_store.persist(path)
                logger.info(f"Saved Chroma vector store to {path}")
            elif self.vector_store_type == "faiss":
                self.vector_store.save_local(path)
                logger.info(f"Saved FAISS vector store to {path}")
            return True
        else:
            logger.warning(f"Saving not implemented for {self.vector_store_type}")
            return False

    async def query(self,
        question,
        user_name,
        stream=False,
        usage_tracker=None,
        user_id=None,
        tenant_id=None,
        model_name=None,
        start_time=None,
        previous_message=None,
        include_web_results=False
    ):
        """
        Query using the multi-workspace retriever with optional streaming support.

        Args:
            question: The question to answer
            stream: Whether to stream the response (default: False)

        Returns:
            Query result or async generator if streaming
        """
        if not hasattr(self, 'qa_chain') or self.qa_chain is None:
            return {"error": "QA chain not initialized", "status": 500}

        # If not streaming, use the regular non-streaming approach
        if not stream:
            try:
                # Execute the query against the combined retrievers
                is_first_message = not previous_message or previous_message.strip() == ""

                # Create consistent query parameters that match the prompt template
                query_params = {
                    "query": question,
                    "question": question,  # Add both for compatibility
                    "user_name": user_name,
                    "previous_message": previous_message if previous_message else "",
                    "is_first_message": "true" if is_first_message else "false"
                }

                # Log the conversation history for debugging
                if previous_message and len(previous_message.strip()) > 0:
                    logger.info(f"Processing conversation with history. First 100 chars: {previous_message[:100]}...")
                    logger.info(f"User name: {user_name}")
                else:
                    logger.info(f"Processing first message in conversation (no history) for user: {user_name}")

                # Perform web search if enabled
                web_sources = []
                # Double-check that web search is explicitly enabled
                if include_web_results is True:
                    logger.info(f"Web search enabled for query: '{question}'")
                    try:
                        # Initialize web search client
                        web_client = WebSearchClient()

                        # Log start time for web search
                        web_search_start = datetime.datetime.now(datetime.timezone.utc)

                        # Interpret the query in conversation context for better web search
                        logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER: Starting interpretation for query: '{question}'")
                        logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER: Previous message available: {bool(previous_message)}")

                        interpreted_query = await query_interpreter.interpret_query_for_web_search(
                            question, previous_message
                        )

                        if interpreted_query != question:
                            logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER: ✅ Query interpreted: '{question}' → '{interpreted_query}'")
                        else:
                            logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER: ➡️ Using original query: '{question}'")

                        # Perform web search with the interpreted query
                        web_results = await web_client.search(interpreted_query, tenant_id, user_id)

                        # Calculate duration
                        web_search_duration = int((datetime.datetime.now(datetime.timezone.utc) - web_search_start).total_seconds() * 1000)

                        # Check if limit exceeded
                        # if isinstance(web_results, dict) and web_results.get("limitExceeded"):
                        #     # Log the failed attempt
                        #     if usage_tracker:
                        #         await usage_tracker.log_api_request(
                        #             user_id=user_id,
                        #             tenant_id=tenant_id,
                        #             endpoint="/web-search",
                        #             method="GET",
                        #             status_code=429,
                        #             duration=web_search_duration,
                        #             error_message="Web search limit exceeded"
                        #         )

                        #     return {
                        #         "answer": f"I'm sorry, but the daily web search limit has been exceeded. {web_results.get('error', '')}",
                        #         "sources": [],
                        #         "status": 429,
                        #         "title": "Web Search Limit Exceeded"
                        #     }

                        # Format web results as sources
                        if web_results and not isinstance(web_results, dict):  # Check if not an error response
                            web_sources = format_web_sources(web_results)
                            logger.info(f"Formatted {len(web_sources)} web sources")
                            logger.info(f"Formatted {web_sources} web sources")

                            # Log successful web search
                            if usage_tracker:
                                await usage_tracker.log_api_request(
                                    user_id=user_id,
                                    tenant_id=tenant_id,
                                    endpoint="/web-search",
                                    method="GET",
                                    status_code=200,
                                    duration=web_search_duration
                                )

                                logger.info(f"Web search for '{question}' returned {len(web_results)} results in {web_search_duration}ms")

                            # Get the prompt template
                            prompt_template = self.qa_chain.combine_documents_chain.llm_chain.prompt

                            # Format the prompt with the original parameters
                            original_prompt = prompt_template.format(**query_params)

                            # Enhance the prompt with web results
                            enhanced_prompt = enhance_prompt_with_web_results(original_prompt, web_results)

                            # Override the prompt in the query parameters
                            query_params["prompt"] = enhanced_prompt
                    except Exception as e:
                        logger.error(f"Error performing web search: {e}")

                # Execute the query with the potentially enhanced prompt
                result = self.qa_chain(query_params)
                logger.info(f"QA chain result: {result}")

                # Extract the answer
                answer = result.get("result", "")

                # Extract sources from the result
                sources = []
                if "source_documents" in result:
                    for doc in result["source_documents"]:
                        sources.append({
                            "content": doc.page_content,
                            "metadata": {
                                "workspace_id": doc.metadata.get("workspace_id"),
                                "_id": doc.metadata.get("_id"),
                                "fileId": doc.metadata.get("fileId"),
                                "page": doc.metadata.get("page"),
                                "fileName": doc.metadata.get("fileName") ,
                                "file_type": doc.metadata.get("file_type") ,
                                "page_label": doc.metadata.get("page_label") ,
                                "total_pages": doc.metadata.get("total_pages") ,
                                "source": doc.metadata.get("source"),
                                "slug": doc.metadata.get("slug"),
                                "workspace": {
                                    "slug": doc.metadata.get("slug"),
                                    "name": doc.metadata.get("name")
                                }
                            }
                        })

                # Enhance document sources with relevance scores and relevant text
                document_sources = RobustCitationEnhancer.enhance_sources(sources, question)

                # Format web sources without quality filtering to preserve original results
                formatted_web_sources = []
                if web_sources:
                    formatted_web_sources = RobustCitationEnhancer.format_web_sources_without_filtering(web_sources)
                    logger.info(f"📄 CITATION DISPLAY: Formatted {len(formatted_web_sources)} web sources without filtering")

                # Combine enhanced document sources and unfiltered web sources
                sources = document_sources + formatted_web_sources

                # Extract AI-generated title from the response if available
                title = question
                if len(title) > 50:
                    title = title[:47] + "..."

                # Check if the response contains a title in the expected format
                if "TITLE:" in answer:
                    title_match = answer.split("TITLE:", 1)[1].strip()
                    if "\n" in title_match:
                        title = title_match.split("\n", 1)[0].strip()
                    else:
                        title = title_match.strip()

                    # Clean up the answer by removing the title line
                    answer = answer.replace(f"TITLE: {title}", "").strip()
                    # Also clean up any "Answer:" prefix that might be present
                    if answer.startswith("Answer:"):
                        answer = answer[7:].strip()

                # Convert ObjectId to string using CustomJSONEncoder
                serializable_sources = json.loads(json.dumps(sources, cls=CustomJSONEncoder))

                # Return structured JSON response
                return {
                    "answer": answer,
                    "title": title,
                    "sources": serializable_sources,  # Include the serialized document sources in the response
                    "status": 200
                }
            except Exception as e:
                logger.error(f"Error executing query: {e}")
                return {"error": f"Query execution failed: {str(e)}", "status": 500}

        # Streaming approach
        else:
            # We need to create a streaming generator
            async def response_generator():
                try:
                    # First, get the relevant documents using the invoke method instead of get_relevant_documents
                    documents = self.qa_chain.retriever.invoke(question)

                    # Prepare sources data
                    sources = []
                    context = ""
                    for doc in documents:
                        sources.append({
                            "content": doc.page_content,
                            "metadata": {
                                "workspace_id": doc.metadata.get("workspace_id"),
                                "_id": doc.metadata.get("_id"),
                                "fileId": doc.metadata.get("fileId"),
                                "page": doc.metadata.get("page"),
                                "fileName": doc.metadata.get("fileName") ,
                                "file_type": doc.metadata.get("file_type") ,
                                "page_label": doc.metadata.get("page_label") ,
                                "total_pages": doc.metadata.get("total_pages") ,
                                "source": doc.metadata.get("source"),
                                "slug": doc.metadata.get("slug"),
                                "workspace": {
                                    "slug": doc.metadata.get("slug"),
                                    "name": doc.metadata.get("name")
                                }
                            }
                        })
                        context += doc.page_content + "\n\n"

                    # Perform web search if enabled
                    web_sources = []
                    # Double-check that web search is explicitly enabled
                    if include_web_results is True:
                        logger.info(f"Web search enabled for streaming query: '{question}'")
                        try:
                            # Initialize web search client
                            web_client = WebSearchClient()

                            # Log start time for web search
                            web_search_start = datetime.datetime.now(datetime.timezone.utc)

                            # Interpret the query in conversation context for better web search
                            logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER (STREAMING): Starting interpretation for query: '{question}'")
                            logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER (STREAMING): Previous message available: {bool(previous_message)}")

                            interpreted_query = await query_interpreter.interpret_query_for_web_search(
                                question, previous_message
                            )

                            if interpreted_query != question:
                                logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER (STREAMING): ✅ Query interpreted: '{question}' → '{interpreted_query}'")
                            else:
                                logger.info(f"🔍 INTELLIGENT QUERY INTERPRETER (STREAMING): ➡️ Using original query: '{question}'")

                            # Perform web search with the interpreted query
                            web_results = await web_client.search(interpreted_query, tenant_id, user_id)
                            logger.info(f"Web search results: {web_results}")

                            # Calculate duration
                            web_search_duration = int((datetime.datetime.now(datetime.timezone.utc) - web_search_start).total_seconds() * 1000)

                            # Check if limit exceeded
                            # if isinstance(web_results, dict) and web_results.get("limitExceeded"):
                            #     # Log the failed attempt
                            #     if usage_tracker:
                            #         await usage_tracker.log_api_request(
                            #             user_id=user_id,
                            #             tenant_id=tenant_id,
                            #             endpoint="/web-search",
                            #             method="GET",
                            #             status_code=429,
                            #             duration=web_search_duration,
                            #             error_message="Web search limit exceeded"
                            #         )

                            #     yield {
                            #         "error": f"I'm sorry, but the daily web search limit has been exceeded. {web_results.get('error', '')}",
                            #         "sources": [],
                            #         "status": 429,
                            #         "title": "Web Search Limit Exceeded",
                            #         "done": True
                            #     }
                            #     return

                            # Format web results as sources
                            if web_results and not isinstance(web_results, dict):  # Check if not an error response
                                web_sources = format_web_sources(web_results)

                                # Log successful web search
                                if usage_tracker:
                                    await usage_tracker.log_api_request(
                                        user_id=user_id,
                                        tenant_id=tenant_id,
                                        endpoint="/web-search",
                                        method="GET",
                                        status_code=200,
                                        duration=web_search_duration
                                    )

                                    logger.info(f"Web search for '{question}' returned {len(web_results)} results in {web_search_duration}ms")

                                # Add web results to context
                                web_context = "\n\nWeb Search Results:\n"
                                for i, result in enumerate(web_results):
                                    title = result.get("title", "Untitled")
                                    snippet = result.get("snippet", "No content available")
                                    link = result.get("link", "")

                                    web_context += f"[{i+1}] {title}\n"
                                    web_context += f"Content: {snippet}\n"
                                    web_context += f"URL: {link}\n\n"

                                context += web_context
                        except Exception as e:
                            logger.error(f"Error performing web search in streaming mode: {e}")

                    # Enhance document sources with relevance scores and relevant text
                    enhanced_document_sources = RobustCitationEnhancer.enhance_sources(sources, question)

                    # Format web sources without quality filtering to preserve original results
                    formatted_web_sources = []
                    if web_sources:
                        formatted_web_sources = RobustCitationEnhancer.format_web_sources_without_filtering(web_sources)
                        logger.info(f"📄 CITATION DISPLAY (STREAMING): Formatted {len(formatted_web_sources)} web sources without filtering")

                    # Combine enhanced document sources and unfiltered web sources
                    sources = enhanced_document_sources + formatted_web_sources

                    # Prepare the prompt with context and question
                    prompt_template = self.qa_chain.combine_documents_chain.llm_chain.prompt

                    # Create consistent prompt parameters that match the template
                    is_first_message = not previous_message or previous_message.strip() == ""
                    prompt_params = {
                        "context": context,
                        "question": question,
                        "user_name": user_name,
                        "previous_message": previous_message if previous_message else "",
                        "is_first_message": "true" if is_first_message else "false"
                    }

                    # Log the conversation history for debugging
                    if previous_message and len(previous_message.strip()) > 0:
                        logger.info(f"Streaming: Processing conversation with history. First 100 chars: {previous_message[:100]}...")
                        logger.info(f"Streaming: User name: {user_name}")
                    else:
                        logger.info(f"Streaming: Processing first message in conversation (no history) for user: {user_name}")

                    prompt = prompt_template.format(**prompt_params)
                    # Create a streaming call to the LLM
                    stream = self.llm.astream(prompt)

                    # Start with empty response
                    full_response = ""


                    # Yield initial structure with empty answer but with sources
                    # Convert ObjectId to string using CustomJSONEncoder
                    serializable_sources = json.loads(json.dumps(sources, cls=CustomJSONEncoder))
                    yield {
                        "answer": "",
                        "sources": serializable_sources,  # Include the serialized document sources in the first chunk
                        "done": False,
                        "status": 200
                    }

                    # Stream chunks as they come
                    async for chunk in stream:
                        content = chunk.content if hasattr(chunk, 'content') else str(chunk)
                        full_response += content

                        # Clean up any "Answer:" prefix that might be present
                        if full_response.startswith("Answer:"):
                            full_response = full_response[7:].strip()
                        if content.startswith("Answer:"):
                            content = content[7:].strip()


                        # Yield the updated response with JSON structure
                        yield {
                            "answer": content,
                            "sources": serializable_sources,  # Include the serialized document sources in each chunk
                            "done": False,
                            "status": 200
                        }
                    # Log token usage and API request
                    await usage_tracker.log_token_usage(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        input_text=question,
                        output_text=full_response,
                        request_type='rag_query',
                        model_used=model_name
                    )
                    await usage_tracker.log_api_request(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        endpoint="/query_workspace",
                        method="POST",
                        status_code=200,
                        duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000)
                    )

                    # Final response with JSON structure
                    yield {
                        "answer": "",
                        "sources": serializable_sources,  # Include the serialized document sources in the final chunk
                        "done": True,
                        "status": 200
                    }

                except Exception as e:
                    logger.error(f"Error in streaming query: {e}")
                    yield {
                        "error": f"Query execution failed: {str(e)}",
                        "sources": [],  # Empty sources array for consistency
                        "done": True,
                        "status": 500
                    }

            # Return the generator
            return response_generator()

    def delete_documents(self, file_id=None, document_ids=None, delete_all=False, collection_name=None):
        """Delete documents from the vector store.

        Args:
            file_id: File ID to delete documents for (for MongoDB).
            document_ids: List of document IDs to delete. If None and delete_all is True, deletes all documents.
            delete_all: If True, deletes all documents from the store.
            collection_name: Optional name of collection to delete from (for MongoDB).

        Returns:
            bool: True if deletion was successful, False otherwise.
        """
        try:
            # Initialize vector store if not already initialized
            if not self.vector_store:
                logger.info("Vector store not initialized, attempting to initialize...")
                self.vector_store = self._initialize_vector_store(collection_name=collection_name)
                if not self.vector_store:
                    logger.error("Failed to initialize vector store")
                    return False

            if self.vector_store_type == "mongodb":
                if collection_name:
                    client = MongoClient(self.vector_store_config["connection_string"])
                    db = client[self.vector_store_config["db_name"]]
                    collection = db[collection_name]
                    if delete_all:
                        collection.delete_many({})
                    elif file_id:
                        collection.delete_many({"fileId": file_id})
                    return True

            elif self.vector_store_type == "chroma":
                if delete_all:
                    self.vector_store._collection.delete()
                elif file_id:
                    self.vector_store._collection.delete(ids=file_id)
                return True

            elif self.vector_store_type == "faiss":
                if delete_all:
                    self.vector_store.reset()
                elif document_ids:
                    self.vector_store.delete(ids=document_ids)
                return True

            elif self.vector_store_type == "pinecone":
                if delete_all:
                    self.vector_store.delete_all()
                elif document_ids:
                    self.vector_store.delete(ids=document_ids)
                return True

            elif self.vector_store_type == "weaviate":
                if delete_all:
                    self.vector_store.delete_collection()
                elif document_ids:
                    for doc_id in document_ids:
                        self.vector_store.delete(doc_id)
                return True

            elif self.vector_store_type == "milvus":
                if delete_all:
                    self.vector_store.delete_collection()
                elif document_ids:
                    self.vector_store.delete(ids=document_ids)
                return True

            elif self.vector_store_type == "qdrant":
                if delete_all:
                    self.vector_store.delete_collection()
                elif document_ids:
                    self.vector_store.delete(points_selector=document_ids)
                return True

            elif self.vector_store_type == "opensearch":
                if delete_all:
                    self.vector_store.delete_index()
                elif document_ids:
                    for doc_id in document_ids:
                        self.vector_store.delete_document(doc_id)
                return True

            elif self.vector_store_type == "elasticsearch":
                if delete_all:
                    self.vector_store.delete_index()
                elif document_ids:
                    for doc_id in document_ids:
                        self.vector_store.delete_document(doc_id)
                return True

            else:
                logger.error(f"Unsupported vector store type for deletion: {self.vector_store_type}")
                return False

        except Exception as e:
            logger.error(f"Error deleting documents: {e}")
            return False
