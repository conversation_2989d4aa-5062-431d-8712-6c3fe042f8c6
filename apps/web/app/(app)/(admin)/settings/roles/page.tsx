import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { cookies } from "next/headers";
import { authOptions } from "@/lib/next-auth";
import RolesSettingsPage from "@/components/wrapper-screens/organization-settings/roles/screen";

export default async function Page() {
  const tenantId = cookies().get("currentOrganizationId")?.value;
  const session: any = await getServerSession(authOptions);

  if (!tenantId) {
    return redirect("/sign-in");
  }

  try {
    const memberships = (session as any)?.memberships || [];
    const membership = memberships.find((m) => m.tenant.id === tenantId);
    const userRole = (session as any).memberships?.find(
      (membership) => membership.tenant.id === tenantId
    )?.role;

    // Only owners can access this page
    if (userRole !== "OWNER") {
      return redirect("/dashboard");
    }

    return (
      <RolesSettingsPage
        organization={membership ? membership.tenant : null}
        userRole={userRole}
      />
    );
  } catch (error) {
    console.error("Error loading roles page:", error);
    return <div>Error loading roles page</div>;
  }
}
