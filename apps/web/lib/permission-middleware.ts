import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { PermissionAction, PermissionResource } from "@prisma/client";
import { hasPermission } from "@/lib/permissions";

/**
 * Middleware to check if a user has the required permission for an API route
 *
 * @param handler - The API route handler function
 * @param action - The action being performed (CREATE, READ, UPDATE, DELETE)
 * @param resource - The resource being accessed (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
 * @returns A new handler function that checks permissions before executing the original handler
 */
export function withPermission(
  handler: (request: Request) => Promise<NextResponse>,
  action: PermissionAction,
  resource: PermissionResource
) {
  return async (request: Request) => {
    try {
      // Parse URL for resource parameters
      const url = new URL(request.url);

      // Get userId and tenantId from headers
      const userIdFromHeader = request.headers.get("x-user-id");
      const tenantIdFromHeader = request.headers.get("x-tenant-id");

      // Get the user from the session for client-side calls
      const token = await getToken({
        req: request as any,
        cookieName: "next-auth.session-token",
      });

      // Use userId from header if available, then fallback to token.sub
      const userId = userIdFromHeader || token?.sub;

      // Use tenantId from header
      const tenantId = tenantIdFromHeader;

      if (!userId) {
        return NextResponse.json(
          {
            error:
              "Unauthorized - userId is required either in x-user-id header or in the session token",
          },
          { status: 401 }
        );
      }

      // If tenantId is not in header, try to get it from the request body
      let bodyTenantId: string | undefined;
      if (
        !tenantId &&
        request.body &&
        request.headers.get("content-type")?.includes("application/json")
      ) {
        // Clone the request to read the body without consuming it
        const clonedRequest = request.clone();
        const body = await clonedRequest.json();
        bodyTenantId = body.tenantId;
      }

      // Also check URL params as a fallback for backward compatibility
      const tenantIdFromParams = url.searchParams.get("tenantId");

      const finalTenantId = tenantId || bodyTenantId || tenantIdFromParams;

      if (!finalTenantId) {
        return NextResponse.json(
          {
            error:
              "TenantId is required in x-tenant-id header, request body, or query parameters",
          },
          { status: 400 }
        );
      }

      // Extract resourceId from query parameters if available
      const resourceId =
        url.searchParams.get("id") ||
        url.searchParams.get("workspaceId") ||
        url.searchParams.get("pageId") ||
        url.searchParams.get("folderId") ||
        url.searchParams.get("fileId") ||
        url.searchParams.get("memberId");

      // For regular users or restricted admin actions, check specific permissions
      const hasRequiredPermission = await hasPermission(
        userId, // Use the userId we determined earlier (from params or token)
        finalTenantId,
        action,
        resource,
        resourceId || undefined
      );

      if (!hasRequiredPermission) {
        return NextResponse.json(
          {
            error: `You don't have permission to ${action.toLowerCase()} this ${resource.toLowerCase()}`,
          },
          { status: 403 }
        );
      }

      // If the user has the required permission, execute the original handler
      return handler(request);
    } catch (error) {
      console.error("Error in permission middleware:", error);
      return NextResponse.json(
        { error: "An error occurred while checking permissions" },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware to check if a user has an active subscription
 *
 * @param handler - The API route handler function
 * @returns A new handler function that checks subscription before executing the original handler
 */
export function withActiveSubscription(
  handler: (request: Request) => Promise<NextResponse>
) {
  return async (request: Request) => {
    try {
      // This is a placeholder for subscription check logic
      // You should implement the actual subscription check here
      // For now, we'll just pass through to the handler
      return handler(request);
    } catch (error) {
      console.error("Error in subscription middleware:", error);
      return NextResponse.json(
        { error: "An error occurred while checking subscription" },
        { status: 500 }
      );
    }
  };
}
