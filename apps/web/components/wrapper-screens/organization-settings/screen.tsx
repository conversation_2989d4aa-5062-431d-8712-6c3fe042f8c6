"use client";

import { useEffect, useState } from "react";
import { Building, Loader2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { updateTenant } from "@/services";

// Create schema with translations
const createOrganizationFormSchema = (t) =>
  z.object({
    name: z.string().min(2, {
      message: t("organization.nameMinLength"),
    }),
    slug: z.string().min(2, {
      message: t("organization.slugMinLength"),
    }),
    description: z.string().optional(),
    url: z
      .string()
      .url({ message: t("organization.validUrl") })
      .optional()
      .or(z.literal("")),
  });

type OrganizationFormValues = z.infer<
  ReturnType<typeof createOrganizationFormSchema>
>;

export default function OrganizationSettingsPage({ organization, userRole }) {
  const router = useRouter();
  const [savingOrg, setSavingOrg] = useState(false);
  const { t } = useLanguage();

  // Create schema with translations
  const organizationFormSchema = createOrganizationFormSchema(t);

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues: {
      name: "",
      slug: "",
      description: "",
      url: "",
    },
    mode: "onChange",
  });

  useEffect(() => {
    if (organization) {
      form.reset({
        name: organization.name || "",
        slug: organization.slug || "",
        description: organization.description || "",
        url: organization.url || "",
      });
    }
  }, [organization, form]);

  async function onSubmit(data: OrganizationFormValues) {
    setSavingOrg(true);
    try {
      // In a real app, you would save the data to the server here
      const result = await updateTenant(organization.id, data);
      if (result?.message) {
        toast.success(result.message);
        router.refresh();
      } else {
        toast.error(t("organization.updateFailed"));
      }
    } catch (error) {
      toast.error(t("organization.updateFailed"));
    } finally {
      setSavingOrg(false);
    }
  }

  // This shouldn't happen - a user should always have a current organization
  // But just in case, redirect to select one
  if (!organization) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <Building className="h-12 w-12 text-primary mb-4" />
        <h2 className="text-xl font-semibold mb-2">
          {t("organization.loading")}
        </h2>
        <Loader2 className="h-6 w-6 animate-spin my-4" />
        <p className="text-muted-foreground text-center max-w-md">
          {t("organization.selectFromSidebar")}
        </p>
      </div>
    );
  }

  const isAdmin = userRole === "ADMIN" || userRole === "OWNER";

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader></CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("organization.nameLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("organization.namePlaceholder")}
                        {...field}
                        disabled={!isAdmin}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("organization.nameDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("organization.slugLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("organization.slugPlaceholder")}
                        {...field}
                        disabled={true}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("organization.slugDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("common.description")}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t("organization.descriptionPlaceholder")}
                        className="resize-none"
                        {...field}
                        disabled={!isAdmin}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("organization.descriptionHelp")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("organization.urlLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("organization.urlPlaceholder")}
                        {...field}
                        disabled={!isAdmin}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("organization.urlDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {isAdmin && (
                <Button type="submit" disabled={savingOrg}>
                  {savingOrg ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("common.saving")}
                    </>
                  ) : (
                    t("common.saveChanges")
                  )}
                </Button>
              )}
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
