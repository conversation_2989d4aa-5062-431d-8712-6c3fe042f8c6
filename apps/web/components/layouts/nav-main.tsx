import { ChevronRight, type LucideIcon } from "lucide-react";
import { useLanguage } from "@/lib/language-context";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/layouts/sidebar";
import { CreateGuard } from "@/components/permission-guard";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
}) {
  const { t } = useLanguage();
  return (
    <SidebarGroup>
      <div className="flex items-center justify-between px-2 mb-1">
        <SidebarGroupLabel>{t("common.workspaces")}</SidebarGroupLabel>
        <CreateGuard resource="WORKSPACE" fallback={null}>
          <a
            href="/workspace/create"
            className="flex items-center justify-center text-sm font-medium text-primary bg-primary/10 hover:bg-primary/20 rounded-md px-2 py-1"
          >
            <span className="mr-1">{t("sidebar.addWorkspace")}</span>
          </a>
        </CreateGuard>
      </div>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible key={item.title} asChild defaultOpen={item.isActive}>
            <SidebarMenuItem>
              <SidebarMenuButton asChild tooltip={item.title}>
                <a
                  href={`/workspace/${item.title
                    .toLowerCase()
                    .replace(/\s+/g, "-")}`}
                >
                  <item.icon />
                  <span>{item.title}</span>
                </a>
              </SidebarMenuButton>
              {item.items?.length ? (
                <>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuAction className="data-[state=open]:rotate-90">
                      <ChevronRight />
                      <span className="sr-only">Toggle</span>
                    </SidebarMenuAction>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items?.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <a href={subItem.url}>
                              <span>{subItem.title}</span>
                            </a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </>
              ) : null}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
