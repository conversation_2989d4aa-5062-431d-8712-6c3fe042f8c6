import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/utils";
import {
  ArrowRight,
  Building,
  CheckCircle2,
  Clock,
  XCircle,
} from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface Tenant {
  id: string;
  name: string;
  createdAt: string;
  plan: string;
  status: "active" | "inactive" | "trial";
}

interface RecentTenantsProps {
  tenants: Tenant[];
}

export function RecentTenants({ tenants }: RecentTenantsProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "trial":
        return <Clock className="h-4 w-4 text-blue-500" />;
      case "inactive":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950";
      case "trial":
        return "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950";
      case "inactive":
        return "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950";
      default:
        return "";
    }
  };

  return (
    <Card className="col-span-3 overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-base font-medium">Recent Tenants</CardTitle>
        <Link href="/tenants">
          <Button variant="ghost" size="sm" className="gap-1 text-xs">
            View all <ArrowRight className="h-3 w-3" />
          </Button>
        </Link>
      </CardHeader>
      <CardContent className="px-0 pb-0">
        <div className="divide-y">
          {tenants.map((tenant) => (
            <div
              key={tenant.id}
              className="flex items-center gap-4 px-6 py-4 hover:bg-muted/50"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-950">
                <Building className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div className="flex-1 space-y-1">
                <div className="flex items-center gap-2">
                  <p className="text-sm font-medium">{tenant.name}</p>
                  <span
                    className={cn(
                      "inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-xs font-medium",
                      getStatusColor(tenant.status)
                    )}
                  >
                    {getStatusIcon(tenant.status)}
                    {tenant.status.charAt(0).toUpperCase() +
                      tenant.status.slice(1)}
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <p className="text-xs text-muted-foreground">
                    Created {formatDate(tenant.createdAt)}
                  </p>
                  <span className="rounded-full bg-muted px-2 py-0.5 text-xs font-medium">
                    {tenant.plan}
                  </span>
                </div>
              </div>
              <Link href={`/tenants/${tenant.id}`}>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                >
                  <ArrowRight className="h-4 w-4" />
                  <span className="sr-only">View tenant</span>
                </Button>
              </Link>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
