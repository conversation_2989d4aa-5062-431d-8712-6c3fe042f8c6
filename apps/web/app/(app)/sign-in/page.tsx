import { LoginForm } from "@/components/wrapper-screens/auth/login-form";
import { authOptions } from "@/lib/next-auth";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function LoginPage(props) {
  const callbackUrl = props?.searchParams?.callbackUrl || "/dashboard";
  const error = props?.searchParams?.error || "";
  const session: any = await getServerSession(authOptions);

  if (error) {
    redirect(`/sign-in?callbackUrl=${callbackUrl}`);
  }
  if (session?.email) {
    if (callbackUrl) {
      return redirect(`${callbackUrl}`);
    } else {
      return redirect("/dashboard");
    }
  }

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <LoginForm isSignIn={true} />
      </div>
    </div>
  );
}
