"use client";

import { useEffect, useState } from "react";
import { Check, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLanguage } from "@/lib/language-context";

interface LanguageSelectorProps {
  showIcon?: boolean;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "ghost" | "outline";
}

export function LanguageSelector({
  showIcon = true,
  size = "md",
  variant = "ghost",
}: LanguageSelectorProps) {
  const { language, setLanguage, t } = useLanguage();

  const handleLanguageChange = (newLanguage: "en" | "de") => {
    setLanguage(newLanguage);

    // Force reload to apply language change to all components
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  const getButtonSize = () => {
    switch (size) {
      case "sm":
        return "h-8 px-2";
      case "lg":
        return "h-11 px-4";
      default:
        return "h-10 px-3";
    }
  };

  const getLanguageName = (code: string) => {
    switch (code) {
      case "en":
        return t("common.english");
      case "de":
        return t("common.german");
      default:
        return code.toUpperCase();
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size="icon"
          className={`w-full gap-2 justify-between`}
        >
          <div className="flex items-center gap-2">
            <Globe className="size-4" /> {t("common.language")}
          </div>
          <span>{getLanguageName(language)}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => handleLanguageChange("en")}
          className="flex items-center justify-between"
        >
          {t("common.english")}
          {language === "en" && <Check className="h-4 w-4 ml-2" />}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleLanguageChange("de")}
          className="flex items-center justify-between"
        >
          {t("common.german")}
          {language === "de" && <Check className="h-4 w-4 ml-2" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
