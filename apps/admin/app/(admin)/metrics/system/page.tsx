"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { formatNumber } from "@/lib/utils";
import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";

export default function SystemMetricsPage() {
  // State for error metrics
  const [errorMetrics, setErrorMetrics] = useState({
    totalRequests: 0,
    failedRequests: 0,
    errorRate: 0,
    errorsByEndpoint: [],
    errorsByTenant: [],
  });

  // State for latency metrics
  const [latencyMetrics, setLatencyMetrics] = useState({
    averageLatency: 0,
    p95Latency: 0,
    p99Latency: 0,
    latencyByEndpoint: [],
  });

  // State for loading
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchSystemMetrics() {
      try {
        // Fetch error metrics
        const errorResponse = await fetch("/api/metrics/system/errors?days=7");
        if (errorResponse.ok) {
          const errorData = await errorResponse.json();
          setErrorMetrics(errorData);
        }

        // Fetch latency metrics
        const latencyResponse = await fetch(
          "/api/metrics/system/latency?days=7"
        );
        if (latencyResponse.ok) {
          const latencyData = await latencyResponse.json();
          setLatencyMetrics(latencyData);
        }
      } catch (error) {
        console.error("Error fetching system metrics:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchSystemMetrics();
  }, []);

  // Colors for the charts
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading
                ? "Loading..."
                : formatNumber(errorMetrics.totalRequests)}
            </div>
            <p className="text-xs text-muted-foreground">Last 7 days</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : `${errorMetrics.errorRate}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {errorMetrics.failedRequests || 0} failed requests
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Average Latency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : `${latencyMetrics.averageLatency} ms`}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all endpoints
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">P95 Latency</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : `${latencyMetrics.p95Latency} ms`}
            </div>
            <p className="text-xs text-muted-foreground">95th percentile</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Errors by Endpoint</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={errorMetrics.errorsByEndpoint || []}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                  layout="vertical"
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="endpoint" type="category" width={150} />
                  <Tooltip
                    formatter={(value) => [`${value} errors`, "Count"]}
                  />
                  <Legend />
                  <Bar dataKey="errorCount" fill="#FF8042" name="Error Count" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Errors by Tenant</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={errorMetrics.errorsByTenant || []}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ tenantName, percent }) =>
                      `${tenantName}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="errorCount"
                    nameKey="tenantName"
                  >
                    {(errorMetrics.errorsByTenant || []).map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value, name, props) => [
                      `${value} errors`,
                      props.payload.tenantName,
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Latency by Endpoint</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          {loading ? (
            <div className="flex h-full items-center justify-center">
              <p>Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={latencyMetrics.latencyByEndpoint || []}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" unit=" ms" />
                <YAxis dataKey="endpoint" type="category" width={150} />
                <Tooltip formatter={(value) => [`${value} ms`, "Latency"]} />
                <Legend />
                <Bar
                  dataKey="averageLatency"
                  fill="#0088FE"
                  name="Avg Latency"
                />
                <Bar dataKey="p95Latency" fill="#00C49F" name="P95 Latency" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
