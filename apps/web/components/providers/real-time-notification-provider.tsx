"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useWebSocketNotifications } from "@/hooks/useWebSocketNotifications";
import { useSharedThreads } from "@/components/shared-threads/shared-threads-provider";

interface RealTimeNotificationContextType {
  isConnected: boolean;
  connectionError: string | null;
  lastNotificationTime: number;
}

const RealTimeNotificationContext =
  createContext<RealTimeNotificationContextType>({
    isConnected: false,
    connectionError: null,
    lastNotificationTime: 0,
  });

export const useRealTimeNotifications = () => {
  const context = useContext(RealTimeNotificationContext);
  if (!context) {
    throw new Error(
      "useRealTimeNotifications must be used within a RealTimeNotificationProvider"
    );
  }
  return context;
};

interface RealTimeNotificationProviderProps {
  children: React.ReactNode;
}

export const RealTimeNotificationProvider: React.FC<
  RealTimeNotificationProviderProps
> = ({ children }) => {
  const [lastNotificationTime, setLastNotificationTime] = useState<number>(0);

  // Get shared threads context if available (might not be available on all pages)
  let sharedThreadsContext;
  try {
    sharedThreadsContext = useSharedThreads();
  } catch (error) {
    // SharedThreadsProvider not available on this page
    sharedThreadsContext = null;
  }

  // Handle real-time notifications
  const handleNewNotification = (notificationData: any) => {
    console.log("Global notification handler:", notificationData);
    setLastNotificationTime(Date.now());

    // Refresh shared threads if context is available
    if (sharedThreadsContext?.refreshThreads) {
      sharedThreadsContext.refreshThreads();
    }
  };

  // Handle thread updates
  const handleThreadUpdate = (threadData: any) => {
    console.log("Global thread update handler:", threadData);

    // Refresh shared threads if context is available
    if (sharedThreadsContext?.refreshThreads) {
      sharedThreadsContext.refreshThreads();
    }
  };

  // WebSocket connection
  const { isConnected, connectionError } = useWebSocketNotifications({
    onNotification: handleNewNotification,
    onThreadUpdate: handleThreadUpdate,
    enableToasts: true,
  });

  const value = {
    isConnected,
    connectionError,
    lastNotificationTime,
  };

  return (
    <RealTimeNotificationContext.Provider value={value}>
      {children}
    </RealTimeNotificationContext.Provider>
  );
};
