"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft } from "lucide-react";
import { toast } from "react-hot-toast";
import { createWorkspace } from "@/services";
import { getCookie } from "@/utils/cookies";
import { useLanguage } from "@/lib/language-context";

// This is a client component that will store the workspace in sessionStorage
export default function CreateWorkspacePage() {
  const currentOrgId = getCookie("currentOrganizationId");
  const userId = getCookie("userId");
  const router = useRouter();
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    initials: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    name: "",
    initials: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;

    // For initials, enforce uppercase and max 2 characters
    if (id === "initials") {
      setFormData({
        ...formData,
        [id]: value.toUpperCase().slice(0, 2),
      });
    } else {
      setFormData({
        ...formData,
        [id]: value,
      });
    }

    // Clear error when user starts typing
    if (errors[id as keyof typeof errors]) {
      setErrors({
        ...errors,
        [id]: "",
      });
    }
  };

  const validateForm = () => {
    const newErrors = {
      name: "",
      initials: "",
    };

    if (!formData.name.trim()) {
      newErrors.name = t("workspace.nameRequired");
    }

    if (!formData.initials || formData.initials.length !== 2) {
      newErrors.initials = t("workspace.initialsRequired");
    }

    setErrors(newErrors);
    return !newErrors.name && !newErrors.initials;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    // Simulate API call with a timeout

    try {
      // Create a new workspace object
      const newWorkspace = {
        id: Date.now(), // Use timestamp as a temporary ID
        title: formData.name,
        slug: formData.name.toLowerCase().replace(/\s+/g, "-"),
        description: formData.description || "",
        initials: formData.initials,
        tenantId: currentOrgId,
      };
      const workspace = await createWorkspace(
        newWorkspace,
        currentOrgId,
        userId
      );

      if (workspace?.data?.id) {
        toast.success(workspace?.message ?? t("workspace.createSuccess"));
        router.refresh();
        router.push("/my-hub");
      } else {
        toast.error(workspace?.error ?? t("workspace.createFailed"));
      }
    } catch (error) {
      console.error("Error creating workspace:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full px-1 py-2 md:px-4">
      <div className="flex items-center mb-6">
        <Link href="/">
          <Button variant="outline" size="icon" className="mr-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h4 className="text-2xl font-bold md:text-3xl tracking-tight">
          {t("workspace.create")}
        </h4>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>{t("workspace.details")}</CardTitle>
            <CardDescription>
              {t("workspace.detailsDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("workspace.name")}</Label>
              <Input
                id="name"
                placeholder={t("workspace.namePlaceholder")}
                value={formData.name}
                onChange={handleChange}
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-xs text-red-500 mt-1">{errors.name}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">{t("workspace.description")}</Label>
              <Textarea
                id="description"
                placeholder={t("workspace.descriptionPlaceholder")}
                rows={4}
                value={formData.description}
                onChange={handleChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="initials">{t("workspace.initials")}</Label>
              <Input
                id="initials"
                placeholder={t("workspace.initialsPlaceholder")}
                maxLength={2}
                className={`uppercase ${
                  errors.initials ? "border-red-500" : ""
                }`}
                value={formData.initials}
                onChange={handleChange}
              />
              {errors.initials ? (
                <p className="text-xs text-red-500 mt-1">{errors.initials}</p>
              ) : (
                <p className="text-sm text-muted-foreground">
                  {t("workspace.initialsDescription")}
                </p>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" asChild>
              <Link href="/">{t("common.cancel")}</Link>
            </Button>
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading
                ? t("workspace.creating")
                : t("workspace.createWorkspace")}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
