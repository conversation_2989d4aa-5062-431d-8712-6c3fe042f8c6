"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Building } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useState, useEffect } from "react";

interface Tenant {
  id: string;
  name: string;
}

interface TenantSelectorProps {
  className?: string;
  selectedTenantId: string | null;
  onTenantChange: (tenantId: string | null) => void;
}

export function TenantSelector({
  className,
  selectedTenantId,
  onTenantChange,
}: TenantSelectorProps) {
  const [open, setOpen] = useState(false);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchTenants() {
      try {
        const response = await fetch("/api/tenants");
        if (response.ok) {
          const data = await response.json();
          setTenants([{ id: "", name: "All Tenants" }, ...data]);
        }
      } catch (error) {
        console.error("Error fetching tenants:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchTenants();
  }, []);

  const selectedTenant = tenants.find((tenant) => tenant.id === selectedTenantId) || tenants[0];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-[250px] justify-between", className)}
          disabled={loading}
        >
          <div className="flex items-center">
            <Building className="mr-2 h-4 w-4" />
            {loading
              ? "Loading tenants..."
              : selectedTenant?.name || "Select tenant"}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[250px] p-0">
        <Command>
          <CommandInput placeholder="Search tenant..." />
          <CommandEmpty>No tenant found.</CommandEmpty>
          <CommandGroup>
            {tenants.map((tenant) => (
              <CommandItem
                key={tenant.id}
                value={tenant.id}
                onSelect={(currentValue) => {
                  onTenantChange(currentValue === "" ? null : currentValue);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selectedTenantId === tenant.id ? "opacity-100" : "opacity-0"
                  )}
                />
                {tenant.name}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
