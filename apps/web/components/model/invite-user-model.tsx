"use client";

import { useState } from "react";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

import { inviteUser } from "@/services";
import { InviteDialog } from "./invite-dialog";
import { useRouter } from "next/navigation";

export function InviteUserForm({
  tenantId,
  userRole,
}: {
  tenantId: string;
  userRole: string;
}) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const { t } = useLanguage();

  async function onSubmit(values: {
    email: string;
    role: string;
    customRoleId?: string;
  }) {
    setIsLoading(true);
    toast.loading(t("inviteMember.sending"));

    try {
      // Prepare invitation data
      const invitationData: any = {
        email: values.email,
        role: values.role,
        tenantId: tenantId,
      };

      // Add customRoleId if role is CUSTOM
      if (values.role === "CUSTOM" && values.customRoleId) {
        invitationData.customRoleId = values.customRoleId;
      }

      const result = await inviteUser(invitationData);

      toast.remove();

      if (result.error) {
        toast.error(result.error);
      } else {
        router.refresh();
        toast.success(
          t("inviteMember.invitationSent", { email: values.email })
        );
        setOpen(false);
      }
    } catch (error) {
      console.error("Invite user error:", error);
      toast.error(t("inviteMember.invitationFailed"));
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <InviteDialog
      subTitle={t("inviteMember.inviteDescription")}
      title={t("inviteMember.inviteTeamMember")}
      trigger={t("inviteMember.inviteMember")}
      open={open}
      onSubmit={onSubmit}
      setOpen={setOpen}
      isLoading={isLoading}
      userRole={userRole}
    />
  );
}
