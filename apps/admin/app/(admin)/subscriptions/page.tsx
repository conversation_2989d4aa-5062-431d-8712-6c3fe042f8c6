"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency, formatDate } from "@/lib/utils";
import { Eye } from "lucide-react";
import Link from "next/link";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
} from "recharts";

// Mock data - will be replaced with real API calls
const mockSubscriptions = [
  {
    id: "1",
    tenantName: "Acme Corp",
    tenantId: "1",
    plan: "Enterprise",
    startDate: "2023-01-15T12:00:00Z",
    endDate: "2024-01-15T12:00:00Z",
    status: "active",
    billingInterval: "year",
    amount: 12000,
    additionalUsers: 5,
    additionalStorage: 50,
  },
  {
    id: "2",
    tenantName: "Startup Inc",
    tenantId: "2",
    plan: "Team",
    startDate: "2023-02-20T14:30:00Z",
    endDate: "2023-08-20T14:30:00Z",
    status: "trial",
    billingInterval: "month",
    amount: 500,
    additionalUsers: 0,
    additionalStorage: 0,
  },
  {
    id: "3",
    tenantName: "Tech Solutions",
    tenantId: "3",
    plan: "Pro",
    startDate: "2023-03-10T09:15:00Z",
    endDate: "2024-03-10T09:15:00Z",
    status: "active",
    billingInterval: "year",
    amount: 6000,
    additionalUsers: 3,
    additionalStorage: 20,
  },
  {
    id: "4",
    tenantName: "Global Services",
    tenantId: "4",
    plan: "Starter",
    startDate: "2023-04-05T16:45:00Z",
    endDate: "2023-05-05T16:45:00Z",
    status: "inactive",
    billingInterval: "month",
    amount: 200,
    additionalUsers: 0,
    additionalStorage: 0,
  },
  {
    id: "5",
    tenantName: "Digital Innovations",
    tenantId: "5",
    plan: "Pro",
    startDate: "2023-05-12T10:20:00Z",
    endDate: "2024-05-12T10:20:00Z",
    status: "active",
    billingInterval: "year",
    amount: 6000,
    additionalUsers: 2,
    additionalStorage: 10,
  },
];

const mockPlanDistribution = [
  { name: "Enterprise", value: 1 },
  { name: "Pro", value: 2 },
  { name: "Team", value: 1 },
  { name: "Starter", value: 1 },
];

const mockBillingDistribution = [
  { name: "Monthly", value: 2 },
  { name: "Yearly", value: 3 },
];

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

export default function SubscriptionsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Subscriptions</h1>
        <p className="text-muted-foreground">
          Manage all subscriptions in the system
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Plan Distribution</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={mockPlanDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockPlanDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value} tenant(s)`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Billing Interval</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={mockBillingDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockBillingDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value} tenant(s)`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Subscriptions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left font-medium">Tenant</th>
                  <th className="px-4 py-3 text-left font-medium">Plan</th>
                  <th className="px-4 py-3 text-left font-medium">Start Date</th>
                  <th className="px-4 py-3 text-left font-medium">End Date</th>
                  <th className="px-4 py-3 text-left font-medium">Status</th>
                  <th className="px-4 py-3 text-left font-medium">Billing</th>
                  <th className="px-4 py-3 text-left font-medium">Amount</th>
                  <th className="px-4 py-3 text-right font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {mockSubscriptions.map((subscription) => (
                  <tr key={subscription.id} className="border-b">
                    <td className="px-4 py-3">{subscription.tenantName}</td>
                    <td className="px-4 py-3">{subscription.plan}</td>
                    <td className="px-4 py-3">{formatDate(subscription.startDate)}</td>
                    <td className="px-4 py-3">{formatDate(subscription.endDate)}</td>
                    <td className="px-4 py-3">
                      <span
                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                          subscription.status === "active"
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : subscription.status === "trial"
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                            : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        }`}
                      >
                        {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      {subscription.billingInterval === "year" ? "Yearly" : "Monthly"}
                    </td>
                    <td className="px-4 py-3">{formatCurrency(subscription.amount)}</td>
                    <td className="px-4 py-3 text-right">
                      <Link href={`/subscriptions/${subscription.id}`}>
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View</span>
                        </Button>
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
