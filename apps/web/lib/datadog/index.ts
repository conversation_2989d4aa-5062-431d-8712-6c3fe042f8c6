import { datadogRum } from "@datadog/browser-rum";

// Check if we're in a browser environment
const isBrowser = typeof window !== "undefined";

export function initDatadog() {
  // Only initialize in browser and preferably in production
  if (!isBrowser) {
    return;
  }

  // Check if Datadog is enabled via environment variable
  const datadogEnabled = process.env.NEXT_PUBLIC_DATADOG_ENABLED === "true";
  if (!datadogEnabled) {
    console.log("Datadog RUM not initialized: disabled by configuration");
    return;
  }

  // Skip initialization if required environment variables are missing
  const applicationId = process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID;
  const clientToken = process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN;
  const site = process.env.NEXT_PUBLIC_DATADOG_SITE;
  const env = process.env.NODE_ENV || "development";
  if (!applicationId || !clientToken) {
    console.warn(
      "Datadog RUM not initialized: missing required environment variables"
    );
    return;
  }

  try {
    // Initialize the Datadog RUM
    datadogRum.init({
      applicationId: applicationId,
      clientToken: clientToken,
      site: (site || "datadoghq.com") as any,
      service: "apex-ai-web",
      env: env,
      // Specify a version number to identify releases
      version: process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0",
      sessionSampleRate: 100,
      trackUserInteractions: true,
      defaultPrivacyLevel: "mask-user-input",
    });

    // Add global metadata if needed
    datadogRum.setGlobalContext({
      app: "apex-ai-web",
    });

    console.log("Datadog RUM initialized successfully");
  } catch (error) {
    console.error("Failed to initialize Datadog RUM:", error);
  }
}

export function trackEvent(name: string, data?: Record<string, any>) {
  if (!isBrowser) {
    return;
  }

  try {
    datadogRum.addAction(name, data);
  } catch (error) {
    console.error("Failed to track Datadog event:", error);
  }
}

export function setUser(userId: string, userInfo?: Record<string, any>) {
  if (!isBrowser) {
    return;
  }

  try {
    datadogRum.setUser({
      id: userId,
      ...userInfo,
    });
  } catch (error) {
    console.error("Failed to set Datadog user:", error);
  }
}
