"use server";

import type {
  GetServerSidePropsContext,
  NextApiRequest,
  NextApiResponse,
} from "next";
import { getServerSession as getNextAuthServerSession } from "next-auth";

import { authOptions } from "./auth-options";

export interface GetServerSessionOptions {
  req: NextApiRequest | GetServerSidePropsContext["req"];
  res: NextApiResponse | GetServerSidePropsContext["res"];
}

export const getServerSession = async ({
  req,
  res,
}: GetServerSessionOptions) => {
  const session = await getNextAuthServerSession(req, res, authOptions);

  if (!session || !session.user?.email) {
    return { user: null, session: null };
  }

  return { user: session.user, session };
};
