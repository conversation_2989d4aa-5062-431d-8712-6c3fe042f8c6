"use client";

import { usePathname } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  Users,
  Building,
  MemoryStick,
  Brain,
  Database,
  Unplug,
  Shield,
  UserCircle,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";

export default function SettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname() ?? "";
  const { t } = useLanguage();

  const navItems = [
    {
      title: t("settings.organization.title"),
      href: "/settings",
      icon: Building,
      subTitle: t("settings.organization.description"),
    },
    {
      title: t("settings.members.title"),
      href: "/settings/members",
      subTitle: t("settings.members.description"),
      icon: Users,
    },
    {
      title: t("settings.roles.title") || "Roles",
      href: "/settings/roles",
      subTitle:
        t("settings.roles.description") ||
        "Manage custom roles and permissions",
      icon: Shield,
    },
    {
      title: t("settings.groups.title") || "Groups",
      href: "/settings/groups",
      subTitle:
        t("settings.groups.description") ||
        "Manage groups and workspace access",
      icon: UserCircle,
    },
    {
      title: t("settings.embedded.title"),
      href: "/settings/embedded",
      subTitle: t("settings.embedded.description"),
      icon: MemoryStick,
    },
    {
      title: t("settings.llm.title"),
      href: "/settings/llm-setting",
      subTitle: t("settings.llm.description"),
      icon: Brain,
    },
    {
      title: t("settings.vectordb.title"),
      href: "/settings/vectordb",
      subTitle: t("settings.vectordb.description"),
      icon: Database,
    },
    {
      title: t("settings.integrations.title"),
      href: "/settings/integrations",
      subTitle: t("settings.integrations.description"),
      icon: Unplug,
    },
  ];
  const currentNav = navItems.find((item) => pathname === item.href);

  return (
    <div className="flex flex-col h-full">
      <div className="flex-none space-y-0.5">
        <h3 className="text-2xl font-bold tracking-tight">
          {currentNav?.title}
        </h3>
        <p className="text-muted-foreground">{currentNav?.subTitle}</p>
      </div>
      <Separator className="my-2 flex-none" />
      <div className="flex flex-1 gap-8 min-h-0">
        {/* Sidebar navigation */}
        <aside className="w-52 shrink-0 flex-none">
          <nav className="flex flex-col space-y-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item?.href;

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center rounded-md px-3 py-2 text-sm font-medium",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  <span>{item.title}</span>
                </Link>
              );
            })}
          </nav>
        </aside>

        {/* Vertical Separator */}
        <Separator orientation="vertical" className="h-full" />

        {/* Main content */}
        <main className="flex-1 overflow-y-auto min-h-0">{children}</main>
      </div>
    </div>
  );
}
