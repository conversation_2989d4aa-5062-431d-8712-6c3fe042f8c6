"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { updateEmbedded } from "@/services";
import { useState } from "react";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

const PROVIDERS = [
  {
    value: "azure",
    label: "Azure OpenAI",
    fields: ["apiKey", "modelName", "deploymentName", "endpoint"],
  },
  {
    value: "openai",
    label: "OpenAI",
    fields: ["apiKey", "modelName"],
  },
];

export default function EmbeddedSettingsPage({ embedded }) {
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState(embedded || {});
  const [selectedProvider, setSelectedProvider] = useState(
    settings.provider || "azure"
  );
  const disabled = true;
  const { t } = useLanguage();

  // Create field labels and placeholders with translations
  const FIELD_LABELS = {
    apiKey: t("embedded.apiKey"),
    modelName: t("embedded.modelName"),
    deploymentName: t("embedded.deploymentName"),
    endpoint: t("embedded.endpoint"),
  };

  const FIELD_PLACEHOLDERS = {
    apiKey: t("embedded.enterApiKey"),
    modelName: t("embedded.enterModelName"),
    deploymentName: t("embedded.enterDeploymentName"),
    endpoint: t("embedded.enterEndpoint"),
  };

  const currentProvider = PROVIDERS.find((p) => p.value === selectedProvider);

  const handleProviderChange = (value) => {
    setSelectedProvider(value);
    // Reset fields for the new provider
    const newSettings = {
      provider: value,
      apiKey: settings.apiKey || "",
    };
    setSettings(newSettings);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await updateEmbedded({ settings });
      toast.success(t("embedded.updateSuccess"));
    } catch (error) {
      console.error("Error updating embedded settings:", error);
      toast.error(t("embedded.updateFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>{t("embedded.providerConfiguration")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="provider">{t("embedded.provider")}</Label>
            <Select
              disabled={disabled}
              value={selectedProvider}
              onValueChange={handleProviderChange}
            >
              <SelectTrigger>
                <SelectValue placeholder={t("embedded.selectProvider")} />
              </SelectTrigger>
              <SelectContent>
                {PROVIDERS.map((provider) => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {currentProvider?.fields.map((field) => (
            <div key={field} className="space-y-2">
              <Label htmlFor={field}>{FIELD_LABELS[field]}</Label>
              <Input
                disabled={disabled}
                id={field}
                type={field === "apiKey" ? "password" : "text"}
                value={settings[field] || ""}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    provider: selectedProvider,
                    [field]: e.target.value,
                  })
                }
                placeholder={FIELD_PLACEHOLDERS[field]}
              />
            </div>
          ))}

          <Button type="submit" disabled={disabled || isLoading}>
            {isLoading ? t("embedded.saving") : t("embedded.saveChanges")}
          </Button>
        </CardContent>
      </Card>
    </form>
  );
}
