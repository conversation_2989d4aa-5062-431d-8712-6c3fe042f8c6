import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import { PlanType, PlanSchema, PlanData } from "./seed-subscription-schema";

// Extend PrismaClient type to include our models
interface CustomPrismaClient extends PrismaClient {
  plan: any;
}

// Cast PrismaClient to our custom interface
const prisma = new PrismaClient() as unknown as CustomPrismaClient;

async function main() {
  console.log("Seeding plans...");

  console.log(
    "Note: This script assumes the Plan model exists in your Prisma schema."
  );
  console.log(
    "If you encounter errors, make sure your database schema is up to date."
  );

  try {
    // Delete existing plans
    await prisma.plan.deleteMany({});
    console.log("Deleted existing plans");
  } catch (error) {
    console.log("Error deleting plans:", error);
  }

  // Create plans based on the pricing table
  const plansData: PlanData[] = [
    {
      name: "Starter",
      type: PlanType.STARTER,
      description: "Perfect for small teams or individuals getting started.",
      includedUsers: 4,
      additionalUserFee: 50,
      stripePriceId: "price_1RIrc9RRDK7OUsBT0aZ9whzA", // Monthly price ID
      stripeYearlyPriceId: "price_1RIrc9RRDK7OUsBTVUeOlGAK", // Yearly price ID
      stripeUserPriceId: "price_1RIrcARRDK7OUsBTlcG9DAI0", // Monthly user price ID
      stripeUserYearlyPriceId: "price_1RIrcBRRDK7OUsBTh8tJrfHG", // Yearly user price ID
      vectorStoreGB: 20,
      price: 249, // CHF 249 per month (for display purposes only)
      isActive: true,
    },
    {
      name: "Business",
      type: PlanType.BUSINESS,
      description: "Ideal for growing businesses with moderate usage needs.",
      includedUsers: 15,
      additionalUserFee: 40,
      stripePriceId: "price_1RIrcBRRDK7OUsBTFi1hDCom", // Monthly price ID
      stripeYearlyPriceId: "price_1RIrcCRRDK7OUsBTcCkSPCjp", // Yearly price ID
      stripeUserPriceId: "price_1RIrcDRRDK7OUsBTUHdRDGGN", // Monthly user price ID
      stripeUserYearlyPriceId: "price_1RIrcDRRDK7OUsBTEJXS3fyb", // Yearly user price ID
      vectorStoreGB: 60,
      price: 649, // CHF 649 per month (for display purposes only)
      isActive: true,
    },
    {
      name: "Enterprise",
      type: PlanType.ENTERPRISE,
      description: "For enterprise teams with higher usage requirements.",
      includedUsers: 30,
      additionalUserFee: 30,
      stripePriceId: "price_1RIrcERRDK7OUsBTaiqV8Hc5", // Monthly price ID
      stripeYearlyPriceId: "price_1RIrcERRDK7OUsBT8M5xxNrl", // Yearly price ID
      stripeUserPriceId: "price_1RIrcFRRDK7OUsBTrievVVBH", // Monthly user price ID
      stripeUserYearlyPriceId: "price_1RIrcFRRDK7OUsBTdCUUaFAf", // Yearly user price ID
      vectorStoreGB: 120,
      price: 1049, // CHF 1049 per month (for display purposes only)
      isActive: true,
    },
    {
      name: "Custom",
      type: PlanType.CUSTOM,
      description:
        "Custom solution for large organizations with specific needs.",
      includedUsers: 100,
      additionalUserFee: 25,
      stripePriceId: "",
      stripeYearlyPriceId: "",
      stripeUserPriceId: "",
      stripeUserYearlyPriceId: "",
      vectorStoreGB: 200,
      price: 0, // Contact Us
      isActive: true,
    },
  ];

  // Validate all plans against the schema
  for (const planData of plansData) {
    try {
      // Validate the plan data
      const validatedPlan = PlanSchema.parse(planData);

      // Create the plan in the database
      await prisma.plan.create({
        data: validatedPlan,
      });

      console.log(`✅ Created plan: ${validatedPlan.name}`);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error(
          `❌ Validation error for plan ${planData.name}:`,
          error.errors
        );
      } else {
        console.error(`❌ Error creating plan ${planData.name}:`, error);
      }
    }
  }

  console.log("Plans seeded successfully!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
