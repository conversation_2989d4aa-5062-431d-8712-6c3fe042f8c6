import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

/**
 * GET /api/roles/[id]
 * Get a specific custom role
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    if (!customRole) {
      return NextResponse.json(
        { error: "Role not found" },
        { status: 404 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: customRole.tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    return NextResponse.json({ customRole });
  } catch (error) {
    console.error("Error fetching custom role:", error);
    return NextResponse.json(
      { error: "Failed to fetch custom role" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/roles/[id]
 * Update a custom role
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, permissionIds } = body;

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
    });

    if (!customRole) {
      return NextResponse.json(
        { error: "Role not found" },
        { status: 404 }
      );
    }

    // Verify user has owner access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: customRole.tenantId,
        role: "OWNER",
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only owners can update custom roles" },
        { status: 403 }
      );
    }

    // Check if a different role with the same name already exists
    if (name) {
      const existingRole = await db.customRole.findFirst({
        where: {
          name,
          tenantId: customRole.tenantId,
          id: { not: roleId },
        },
      });

      if (existingRole) {
        return NextResponse.json(
          { error: "A role with this name already exists" },
          { status: 400 }
        );
      }
    }

    // Update the role
    const updatedRole = await db.customRole.update({
      where: { id: roleId },
      data: {
        name: name || customRole.name,
        description: description !== undefined ? description : customRole.description,
      },
    });

    // Update permissions if provided
    if (permissionIds) {
      // Delete existing permissions
      await db.customRolePermission.deleteMany({
        where: { customRoleId: roleId },
      });

      // Add new permissions
      if (permissionIds.length > 0) {
        const permissionConnections = permissionIds.map((permissionId: string) => ({
          permissionId,
          customRoleId: roleId,
        }));

        await db.customRolePermission.createMany({
          data: permissionConnections,
        });
      }
    }

    // Get the updated role with permissions
    const finalRole = await db.customRole.findUnique({
      where: { id: roleId },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    return NextResponse.json({ customRole: finalRole });
  } catch (error) {
    console.error("Error updating custom role:", error);
    return NextResponse.json(
      { error: "Failed to update custom role" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/roles/[id]
 * Delete a custom role
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
    });

    if (!customRole) {
      return NextResponse.json(
        { error: "Role not found" },
        { status: 404 }
      );
    }

    // Verify user has owner access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: customRole.tenantId,
        role: "OWNER",
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only owners can delete custom roles" },
        { status: 403 }
      );
    }

    // Check if the role is in use
    const membersUsingRole = await db.membership.count({
      where: { customRoleId: roleId },
    });

    if (membersUsingRole > 0) {
      return NextResponse.json(
        { error: "This role is currently assigned to members and cannot be deleted" },
        { status: 400 }
      );
    }

    // Delete the role and its permissions
    await db.customRolePermission.deleteMany({
      where: { customRoleId: roleId },
    });

    await db.customRole.delete({
      where: { id: roleId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting custom role:", error);
    return NextResponse.json(
      { error: "Failed to delete custom role" },
      { status: 500 }
    );
  }
}
