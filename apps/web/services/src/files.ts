import { apiUrl, fetchJson } from "..";

export const getFile = async (tenantId, userId, fileId = null) => {
  if (fileId) {
    const response = await fetchJson(
      `${apiUrl}/files?id=${fileId}`,
      userId,
      tenantId
    );
    return response;
  }
  const response = await fetchJson(`${apiUrl}/files`, userId, tenantId);
  return response;
};

export const deleteFile = async (fileId, tenantId, userId) => {
  try {
    const url = `${apiUrl}/files?id=${fileId}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete files api error": error });
    return { error: error.message || "Error deleting files" };
  }
};

export const updateFile = async (data, tenantId, userId) => {
  try {
    const response = await fetch(`${apiUrl}/files`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Update file api calling error": error });
    return { error: "Error Updating The file" };
  }
};

export const createFile = async (data, tenantId, userId) => {
  try {
    const response = await fetch(`${apiUrl}/files`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create files api calling error": error });
    return { error: "Error Creating The File" };
  }
};
