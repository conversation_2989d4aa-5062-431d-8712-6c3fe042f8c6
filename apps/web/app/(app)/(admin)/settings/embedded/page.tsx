import EmbeddedSettingsPage from "@/components/wrapper-screens/organization-settings/embedded/embedded-settings-page";
import { getEmbedded } from "@/services";
import { cookies } from "next/headers";

export default async function Page() {
  const tenantId = cookies().get("currentOrganizationId")?.value ?? "";

  try {
    // Handle case where folder data is not found
    const embedded = await getEmbedded({ tenantId });
    return <EmbeddedSettingsPage embedded={embedded?.settings} />;
  } catch (error) {
    console.error("Error loading folder:", error);
  }
}
