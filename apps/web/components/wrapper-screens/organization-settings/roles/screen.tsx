"use client";

import { useState, useEffect } from "react";
import { useLanguage } from "@/lib/language-context";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Plus, Pencil, Trash2 } from "lucide-react";
import { toast } from "react-hot-toast";
import { getCookie } from "@/utils/cookies";
import RoleForm from "./role-form";

// Define interfaces for our data structures
interface Permission {
  id: string;
  action: string;
  resource: string;
  description?: string;
}

interface RolePermission {
  permissionId: string;
  permission?: Permission;
}

interface CustomRole {
  id: string;
  name: string;
  description?: string;
  permissions: RolePermission[];
  tenantId: string;
  createdAt?: string;
  updatedAt?: string;
}

interface Organization {
  id: string;
  name: string;
  [key: string]: any;
}

interface RoleFormData {
  name: string;
  description?: string;
  permissionIds: string[];
}

interface RolesSettingsPageProps {
  organization?: Organization;
  userRole?: string;
}

export default function RolesSettingsPage({
  organization,
  userRole,
}: RolesSettingsPageProps) {
  const { t } = useLanguage();
  const [roles, setRoles] = useState<CustomRole[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentRole, setCurrentRole] = useState<CustomRole | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<CustomRole | null>(null);

  const tenantId = organization?.id || getCookie("currentOrganizationId");

  // Fetch roles and permissions
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch permissions
        const permissionsResponse = await fetch(`/api/permissions`);
        if (permissionsResponse.ok) {
          const permissionsData = await permissionsResponse.json();
          console.log({ permissionsData });
          setPermissions(permissionsData.permissions);
        }

        // Fetch roles
        const rolesResponse = await fetch(`/api/roles?tenantId=${tenantId}`);
        if (rolesResponse.ok) {
          const rolesData = await rolesResponse.json();
          setRoles(rolesData.customRoles);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load roles and permissions");
      } finally {
        setIsLoading(false);
      }
    };

    if (tenantId) {
      fetchData();
    }
  }, [tenantId]);

  // Handle role creation
  const handleCreateRole = async (roleData: RoleFormData) => {
    try {
      const response = await fetch("/api/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...roleData,
          tenantId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setRoles([...roles, data.customRole as CustomRole]);
        setIsCreateDialogOpen(false);
        toast.success("Role created successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to create role");
      }
    } catch (error) {
      console.error("Error creating role:", error);
      toast.error("Failed to create role");
    }
  };

  // Handle role update
  const handleUpdateRole = async (roleData: RoleFormData) => {
    try {
      if (!currentRole) {
        toast.error("No role selected for update");
        return;
      }

      const response = await fetch(`/api/roles/${currentRole.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(roleData),
      });

      if (response.ok) {
        const data = await response.json();
        setRoles(
          roles.map((role) =>
            role.id === currentRole.id ? (data.customRole as CustomRole) : role
          )
        );
        setIsEditDialogOpen(false);
        toast.success("Role updated successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update role");
      }
    } catch (error) {
      console.error("Error updating role:", error);
      toast.error("Failed to update role");
    }
  };

  // Handle role deletion
  const handleDeleteRole = async () => {
    try {
      if (!roleToDelete) {
        toast.error("No role selected for deletion");
        return;
      }

      const response = await fetch(`/api/roles/${roleToDelete.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setRoles(roles.filter((role) => role.id !== roleToDelete.id));
        setDeleteDialogOpen(false);
        toast.success("Role deleted successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete role");
      }
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error("Failed to delete role");
    }
  };

  // Only owners can manage roles
  if (userRole !== "OWNER") {
    return (
      <div className="container py-6">
        <h1 className="text-2xl font-bold mb-6">{t("roles.title")}</h1>
        <p>{t("roles.ownerOnly")}</p>
      </div>
    );
  }

  return (
    <div className=" ">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t("roles.title")}</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t("roles.createRole")}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("roles.customRoles")}</CardTitle>
          <CardDescription>{t("roles.customRolesDescription")}</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-4">{t("common.loading")}</div>
          ) : roles.length === 0 ? (
            <div className="text-center py-4">{t("roles.noRoles")}</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("roles.name")}</TableHead>
                  <TableHead>{t("roles.description")}</TableHead>
                  <TableHead>{t("roles.permissions")}</TableHead>
                  <TableHead className="text-right">
                    {t("common.actions")}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell className="font-medium">{role.name}</TableCell>
                    <TableCell>{role.description || "-"}</TableCell>
                    <TableCell>
                      {role.permissions.length} {t("roles.permissionsCount")}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setCurrentRole(role);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setRoleToDelete(role);
                          setDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Role Sheet */}
      <Sheet open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <SheetContent
          side="right"
          className="w-full sm:max-w-[700px] overflow-y-auto"
        >
          <SheetHeader>
            <SheetTitle>{t("roles.createRole")}</SheetTitle>
            <SheetDescription>
              {t("roles.createRoleDescription")}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <RoleForm
              permissions={permissions}
              onSubmit={handleCreateRole}
              onCancel={() => setIsCreateDialogOpen(false)}
            />
          </div>
        </SheetContent>
      </Sheet>

      {/* Edit Role Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <SheetContent
          side="right"
          className="w-full sm:max-w-[700px] overflow-y-auto"
        >
          <SheetHeader>
            <SheetTitle>{t("roles.editRole")}</SheetTitle>
            <SheetDescription>
              {t("roles.editRoleDescription")}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            {currentRole && (
              <RoleForm
                permissions={permissions}
                initialData={currentRole}
                onSubmit={handleUpdateRole}
                onCancel={() => setIsEditDialogOpen(false)}
              />
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Delete Role Confirmation */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("roles.deleteRoleTitle")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("roles.deleteRoleDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRole}>
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
