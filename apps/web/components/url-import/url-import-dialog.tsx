"use client";

import { useState, useEffect } from "react";
import { getCookie } from "@/utils/cookies";
import { useRouter } from "next/navigation";
import { createFile } from "@/services";
import toast from "react-hot-toast";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Globe, AlertCircle, Check } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useLanguage } from "@/lib/language-context";
import { workspaceChatService } from "@/services/workspace-chat";

interface UrlImportDialogProps {
  open: boolean; // Required by Dialog component
  onOpenChange: (_open: boolean) => void; // Required by Dialog component
  workspaceSlug: string;
  workspaceId: string;
  pageId: string;
  folderId?: string;
  onSuccess?: () => void;
}

export function UrlImportDialog({
  open,
  onOpenChange,
  workspaceSlug,
  workspaceId,
  pageId,
  folderId,
  onSuccess,
}: UrlImportDialogProps) {
  const { t } = useLanguage();
  const router = useRouter();
  const userId = getCookie("userId") || "";
  const tenantId = getCookie("currentOrganizationId") || "";

  const [url, setUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [hasSitemap, setHasSitemap] = useState(false);
  const [sitemapUrls, setSitemapUrls] = useState<string[]>([]);
  const [selectedSitemapUrls, setSelectedSitemapUrls] = useState<string[]>([]);
  const [showSitemapSelector, setShowSitemapSelector] = useState(false);
  const [isBatchImporting, setIsBatchImporting] = useState(false);

  // Import options
  const [contentCleaningLevel, setContentCleaningLevel] = useState<
    "basic" | "medium" | "aggressive"
  >("basic");
  const [crawlDepth, setCrawlDepth] = useState(1);

  // Content state
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setUrl("");
      setTitle("");
      setContent("");
      setError("");
      setIsLoading(false);
      setIsSubmitting(false);
      setIsDuplicate(false);
      setHasSitemap(false);
      setSitemapUrls([]);
      setSelectedSitemapUrls([]);
      setShowSitemapSelector(false);
      setIsBatchImporting(false);
      setContentCleaningLevel("basic");
      setCrawlDepth(1);
    }
  }, [open]);

  // Check for duplicate URL
  useEffect(() => {
    const checkDuplicate = async () => {
      if (!url || !workspaceId) return;

      try {
        const response = await fetch("/api/url-import", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-user-id": userId,
            "x-tenant-id": tenantId,
          },
          body: JSON.stringify({
            url,
            action: "check-duplicate",
            workspaceId,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setIsDuplicate(data.isDuplicate);
        }
      } catch (error) {
        console.error("Error checking duplicate URL:", error);
      }
    };

    checkDuplicate();
  }, [url, workspaceId, userId, tenantId]);

  const handleFetchUrl = async () => {
    if (!url) {
      setError(t("common.urlRequired"));
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Fetch the URL content directly
      const response = await fetch("/api/url-import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userId,
          "x-tenant-id": tenantId,
        },
        body: JSON.stringify({
          url,
          action: "fetch",
          options: {
            contentCleaningLevel,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch URL");
      }

      const data = await response.json();

      setTitle(data.title);
      setContent(data.content);
      setHasSitemap(data.hasSitemap);

      if (data.hasSitemap) {
        fetchSitemap(data.sitemapUrl);

        // Show sitemap selector if sitemap is available and crawl depth > 1
        if (crawlDepth > 1) {
          setShowSitemapSelector(true);
        }
      }
    } catch (error) {
      console.error("Error fetching URL:", error);
      setError(error.message || "Failed to fetch URL");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSitemap = async (sitemapUrl: string) => {
    try {
      const response = await fetch("/api/url-import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userId,
          "x-tenant-id": tenantId,
        },
        body: JSON.stringify({
          url: sitemapUrl,
          action: "sitemap",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch sitemap");
      }

      const data = await response.json();
      setSitemapUrls(data.urls || []);
    } catch (error) {
      console.error("Error fetching sitemap:", error);
    }
  };

  const handleSave = async () => {
    if (!title || !content) {
      setError(t("common.titleAndContentRequired"));
      return;
    }

    setIsSubmitting(true);

    try {
      const file = new Blob([content], { type: "text/html" });
      const formData = new FormData();
      formData.append("file", file);
      formData.append("tenantId", tenantId);
      formData.append("workspaceSlug", workspaceSlug);
      formData.append("pageId", pageId);

      const uploadResponse = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });
      const uploadResponseData = await uploadResponse.json();

      // Create file with the imported content as HTML
      const fileData = {
        name: title,
        type: "text/html",
        extension: "html",
        content,
        workspaceSlug,
        pageId,
        parentId: pageId ?? folderId,
        folderId,
        url: uploadResponseData.url,
        metadata: {
          sourceUrl: url,
          importedAt: new Date().toISOString(),
          contentCleaningLevel,
        },
        vectorizationStatus: "PENDING", // Set initial status to PENDING
      };

      const result = await createFile(fileData, tenantId, userId);
      workspaceChatService.uploadForIndexing({
        userId,
        document_path: result.data?.[0]?.url,
        workspaceSlug,
        tenantId,
        file_id: result.data?.[0]?.id,
      });

      if (result.error) {
        throw new Error(result.error);
      }

      // Start vectorization process
      try {
        const fileId = result.file?.id;
        if (fileId) {
          // Import the workspaceChatService
          const { workspaceChatService } = await import(
            "@/services/workspace-chat"
          );

          // Start vectorization
          await workspaceChatService.uploadForIndexing({
            userId,
            document_path: result.file?.url, // Use the URL as the document path
            workspaceSlug,
            tenantId,
            file_id: fileId,
          });

          // Show success message with vectorization info
          toast.success(
            t("workspace.urlImportAndVectorizationStarted", {
              defaultValue: "Content imported and vectorization started",
            })
          );
        } else {
          // Show regular success message if file ID is not available
          toast.success(t("workspace.urlImportSuccess"));
        }
      } catch (vectorizationError) {
        console.error("Error starting vectorization:", vectorizationError);
        // Still consider the import successful even if vectorization fails
        toast.success(
          t("workspace.urlImportSuccessButVectorizationFailed", {
            defaultValue:
              "Content imported successfully but vectorization failed",
          })
        );
      }

      onOpenChange(false);

      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page to show the new file
      router.refresh();
    } catch (error) {
      console.error("Error saving imported content:", error);
      setError(error.message || "Failed to save imported content");
      toast.error(t("workspace.urlImportError"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBatchImport = async () => {
    if (selectedSitemapUrls.length === 0) {
      setError(t("workspace.noUrlsSelected"));
      return;
    }

    setIsBatchImporting(true);
    setError("");

    try {
      let successCount = 0;
      let failureCount = 0;

      // Process each URL sequentially
      for (const batchUrl of selectedSitemapUrls) {
        try {
          // Fetch content for each URL
          const response = await fetch("/api/url-import", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "x-user-id": userId,
              "x-tenant-id": tenantId,
            },
            body: JSON.stringify({
              url: batchUrl,
              action: "fetch",
              options: {
                contentCleaningLevel,
              },
            }),
          });

          if (!response.ok) {
            failureCount++;
            continue;
          }
          const data = await response.json();
          const file = new Blob([data.content], { type: "text/html" });
          const formData = new FormData();
          formData.append("file", file);
          formData.append("tenantId", tenantId);
          formData.append("workspaceSlug", workspaceSlug);
          formData.append("pageId", pageId);

          const uploadResponse = await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });
          const uploadResponseData = await uploadResponse.json();

          // Create file for each URL as HTML
          const fileData = {
            name: data.title || `Imported from ${batchUrl}`,
            type: "text/html",
            extension: "html",
            content: data.content,
            workspaceSlug,
            pageId,
            parentId: pageId ?? folderId,
            folderId,
            url: uploadResponseData.url,
            metadata: {
              sourceUrl: batchUrl,
              importedAt: new Date().toISOString(),
              batchImported: true,
              parentUrl: url,
              contentCleaningLevel,
            },
            vectorizationStatus: "PENDING", // Set initial status to PENDING
          };

          const result = await createFile(fileData, tenantId, userId);

          if (result.error) {
            failureCount++;
          } else {
            successCount++;

            // Start vectorization process for the file
            try {
              const fileId = result.file?.id;
              if (fileId) {
                // Import the workspaceChatService
                const { workspaceChatService } = await import(
                  "@/services/workspace-chat"
                );

                // Start vectorization
                await workspaceChatService.uploadForIndexing({
                  userId,
                  document_path: result.file?.url, // Use the URL as the document path
                  workspaceSlug,
                  tenantId,
                  file_id: fileId,
                });

                console.log(`Vectorization started for ${batchUrl}`);
              }
            } catch (vectorizationError) {
              console.error(
                `Error starting vectorization for ${batchUrl}:`,
                vectorizationError
              );
              // Continue with the batch import even if vectorization fails for some files
            }
          }
        } catch (error) {
          console.error(`Error importing ${batchUrl}:`, error);
          failureCount++;
        }
      }

      if (successCount > 0) {
        toast.success(
          t("workspace.batchImportAndVectorizationSuccess", {
            count: successCount,
            defaultValue: `${successCount} URLs imported and vectorization started`,
          })
        );

        if (failureCount > 0) {
          toast.error(
            t("workspace.batchImportPartialFailure", { count: failureCount })
          );
        }

        onOpenChange(false);

        if (onSuccess) {
          onSuccess();
        }

        // Refresh the page to show the new files
        router.refresh();
      } else {
        toast.error(t("workspace.batchImportFailure"));
      }
    } catch (error) {
      console.error("Error during batch import:", error);
      setError(error.message || "Failed to perform batch import");
      toast.error(t("workspace.batchImportError"));
    } finally {
      setIsBatchImporting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("workspace.importFromUrl")}</DialogTitle>
          <DialogDescription>
            {t("workspace.importFromUrlDescription")}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex flex-col gap-4">
            <div className="flex items-end gap-2">
              <div className="flex-1">
                <Input
                  placeholder="https://example.com"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <Button onClick={handleFetchUrl} disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Globe className="h-4 w-4 mr-2" />
                )}
                {t("common.fetch")}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label
                  htmlFor="contentCleaningLevel"
                  className="text-sm font-medium"
                >
                  {t("workspace.contentCleaningLevel")}
                </label>
                <select
                  id="contentCleaningLevel"
                  value={contentCleaningLevel}
                  onChange={(e) =>
                    setContentCleaningLevel(
                      e.target.value as "basic" | "medium" | "aggressive"
                    )
                  }
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="basic">
                    {t("workspace.cleaningLevelBasic")}
                  </option>
                  <option value="medium">
                    {t("workspace.cleaningLevelMedium")}
                  </option>
                  <option value="aggressive">
                    {t("workspace.cleaningLevelAggressive")}
                  </option>
                </select>
              </div>

              <div className="space-y-2">
                <label htmlFor="crawlDepth" className="text-sm font-medium">
                  {t("workspace.crawlDepth")}
                </label>
                <select
                  id="crawlDepth"
                  value={crawlDepth}
                  onChange={(e) => setCrawlDepth(parseInt(e.target.value))}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="1">{t("workspace.crawlDepth1")}</option>
                  <option value="2">{t("workspace.crawlDepth2")}</option>
                  <option value="3">{t("workspace.crawlDepth3")}</option>
                </select>
              </div>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{t("common.error")}</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isDuplicate && (
            <Alert className="bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800">
              <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              <AlertTitle>{t("common.warning")}</AlertTitle>
              <AlertDescription>
                {t("workspace.urlAlreadyImported")}
              </AlertDescription>
            </Alert>
          )}

          {/* Sitemap selector */}
          {hasSitemap && showSitemapSelector && sitemapUrls.length > 0 && (
            <div className="border rounded-md p-4">
              <h3 className="text-lg font-medium mb-2">
                {t("workspace.sitemapUrls")}
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                {t("workspace.selectUrlsToImport")}
              </p>

              <div className="space-y-2 max-h-[200px] overflow-y-auto">
                {sitemapUrls.map((sitemapUrl, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`sitemap-url-${index}`}
                      checked={selectedSitemapUrls.includes(sitemapUrl)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedSitemapUrls([
                            ...selectedSitemapUrls,
                            sitemapUrl,
                          ]);
                        } else {
                          setSelectedSitemapUrls(
                            selectedSitemapUrls.filter(
                              (url) => url !== sitemapUrl
                            )
                          );
                        }
                      }}
                      className="rounded"
                    />
                    <label
                      htmlFor={`sitemap-url-${index}`}
                      className="text-sm truncate"
                    >
                      {sitemapUrl}
                    </label>
                  </div>
                ))}
              </div>

              <div className="mt-4 flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedSitemapUrls([])}
                >
                  {t("common.deselectAll")}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedSitemapUrls([...sitemapUrls])}
                >
                  {t("common.selectAll")}
                </Button>
              </div>

              <div className="mt-4">
                <Button
                  onClick={handleBatchImport}
                  disabled={
                    isBatchImporting || selectedSitemapUrls.length === 0
                  }
                  className="w-full"
                >
                  {isBatchImporting ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : null}
                  {t("workspace.batchImport", {
                    count: selectedSitemapUrls.length,
                  })}
                </Button>
              </div>
            </div>
          )}

          {content && (
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="title"
                  className="block text-sm font-medium mb-1"
                >
                  {t("common.title")}
                </label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>
              <div className="text-sm text-muted-foreground">
                {t("workspace.contentImportedAsHtml")}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("common.cancel")}
          </Button>

          <Button
            onClick={handleSave}
            disabled={isSubmitting || !content || !title}
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Check className="h-4 w-4 mr-2" />
            )}
            {t("common.save")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
