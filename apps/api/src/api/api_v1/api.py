from fastapi import APIRouter, Depends
from src.api.api_v1.endpoints import workspace_chat, usage_stats, web_search, global_search, notifications
from src.api.auth import validate_token

# Create the main API router
api_router = APIRouter()

# Add the workspace chat router with token validation
api_router.include_router(
    workspace_chat.router,
    prefix="/workspace-chat",
    tags=["workspace-chat"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the usage stats router with token validation
api_router.include_router(
    usage_stats.router,
    prefix="/usages",
    tags=["chat-usages"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the web search router with token validation
api_router.include_router(
    web_search.router,
    prefix="/web-search",
    tags=["web-search"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}, 429: {"description": "Too Many Requests"}}
)

# Add the global search router with token validation
api_router.include_router(
    global_search.router,
    prefix="/global-search",
    tags=["global-search"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the notifications router (no token validation needed for internal broadcasting)
api_router.include_router(
    notifications.router,
    prefix="/notifications",
    tags=["notifications"],
    responses={404: {"description": "Not found"}, 500: {"description": "Internal Server Error"}}
)