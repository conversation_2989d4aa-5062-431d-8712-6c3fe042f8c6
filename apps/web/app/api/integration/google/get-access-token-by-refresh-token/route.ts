import { NextRequest, NextResponse } from "next/server";

import axios from "axios";
import db from "@/lib/shared-db";

export async function GET(req: NextRequest) {
  const url = req.url;
  const urlParams = new URLSearchParams(url.split("?")[1]);
  const refresh_token = urlParams.get("refresh_token");
  const id = urlParams.get("id") ?? "";

  if (!refresh_token) {
    return NextResponse.json(
      { message: "Authorization code is missing" },
      { status: 400 }
    );
  }

  const tokenEndpoint = `https://oauth2.googleapis.com/token`;
  const params = new URLSearchParams();

  params.append("client_id", process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ?? "");
  params.append("grant_type", "refresh_token");
  params.append("refresh_token", refresh_token);
  params.append("client_secret", process.env.GOOGLE_CLIENT_SECRET ?? "");

  try {
    const response = await axios.post(tokenEndpoint, params, {
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
    });
    if (response.data?.access_token) {
      const integration = await db.integration.update({
        where: { id },
        data: { accessToken: response.data?.access_token },
      });
      if (integration.id) {
        return NextResponse.json({ integration }, { status: 200 });
      } else {
        return NextResponse.json(
          { error: "Failed to fetch access token" },
          { status: 500 }
        );
      }
    } else {
      return NextResponse.json(
        { error: "Failed to fetch access token" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Token acquisition failed:", error?.response?.data);

    return NextResponse.json(
      { error: "Failed to fetch access token" },
      { status: 500 }
    );
  }
}
