import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "6months"; // '30days', '6months', '12months', etc.
    
    // Parse the period
    const match = period.match(/^(\d+)(days|months)$/);
    if (!match) {
      return NextResponse.json(
        { error: "Invalid period format. Use format like '30days' or '6months'" },
        { status: 400 }
      );
    }
    
    const amount = parseInt(match[1], 10);
    const unit = match[2];
    
    // Calculate the start date
    const startDate = new Date();
    if (unit === 'days') {
      startDate.setDate(startDate.getDate() - amount);
    } else if (unit === 'months') {
      startDate.setMonth(startDate.getMonth() - amount);
    }

    // Get all subscriptions created in the period
    const subscriptions = await db.subscription.findMany({
      where: {
        createdAt: {
          gte: startDate
        }
      },
      include: {
        plan: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Group by month or day
    const groupedData = {};
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    // Function to calculate MRR for a subscription
    const calculateMRR = (subscription) => {
      // Get base plan price
      const basePrice = subscription.billingInterval === 'month' 
        ? subscription.plan.price || 0
        : (subscription.plan.price || 0) / 12;
      
      // Add additional users cost
      const additionalUsersCost = subscription.additionalUsers * 
        (subscription.billingInterval === 'month' 
          ? subscription.plan.additionalUserFee || 0
          : (subscription.plan.additionalUserFee || 0) / 12);
      
      // Add additional storage cost (simplified calculation)
      const additionalStorageCost = subscription.additionalStorageGB * 10; // Assuming $10 per GB
      
      // Calculate total monthly revenue for this subscription
      return basePrice + additionalUsersCost + additionalStorageCost;
    };

    // Create a map of active subscriptions for each month/day
    const activeSubscriptionsByPeriod = {};
    
    // For each subscription, add it to all periods from its creation date to now
    subscriptions.forEach(subscription => {
      const creationDate = new Date(subscription.createdAt);
      let currentDate = new Date(creationDate);
      const endDate = new Date(); // Today
      
      while (currentDate <= endDate) {
        let periodKey;
        
        if (unit === 'days') {
          // Format as YYYY-MM-DD
          periodKey = currentDate.toISOString().split('T')[0];
        } else {
          // Format as Month YYYY (e.g., "Jan 2023")
          periodKey = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
        }
        
        if (!activeSubscriptionsByPeriod[periodKey]) {
          activeSubscriptionsByPeriod[periodKey] = [];
        }
        
        // Only add if the subscription was active in this period
        // (not canceled or created after this period)
        if (
          (!subscription.canceledAt || new Date(subscription.canceledAt) > currentDate) &&
          new Date(subscription.createdAt) <= currentDate
        ) {
          activeSubscriptionsByPeriod[periodKey].push(subscription);
        }
        
        // Move to next period
        if (unit === 'days') {
          currentDate.setDate(currentDate.getDate() + 1);
        } else {
          currentDate.setMonth(currentDate.getMonth() + 1);
        }
      }
    });
    
    // Calculate MRR for each period
    for (const [periodKey, activeSubscriptions] of Object.entries(activeSubscriptionsByPeriod)) {
      const mrr = activeSubscriptions.reduce((sum, subscription) => {
        return sum + calculateMRR(subscription);
      }, 0);
      
      groupedData[periodKey] = {
        revenue: mrr,
        subscriptionCount: activeSubscriptions.length
      };
    }
    
    // Convert to array format for charts
    let timeSeriesData = [];
    
    if (unit === 'days') {
      // For days, we want to ensure all days in the range are represented
      const endDate = new Date();
      let currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const day = currentDate.toISOString().split('T')[0];
        timeSeriesData.push({
          date: day,
          revenue: groupedData[day]?.revenue || 0,
          subscriptionCount: groupedData[day]?.subscriptionCount || 0
        });
        
        currentDate.setDate(currentDate.getDate() + 1);
      }
    } else {
      // For months, convert the grouped data to array
      for (const [monthYear, data] of Object.entries(groupedData)) {
        timeSeriesData.push({
          month: monthYear,
          revenue: data.revenue,
          subscriptionCount: data.subscriptionCount
        });
      }
      
      // Sort by date
      timeSeriesData.sort((a, b) => {
        const [aMonth, aYear] = a.month.split(' ');
        const [bMonth, bYear] = b.month.split(' ');
        
        if (aYear !== bYear) {
          return parseInt(aYear) - parseInt(bYear);
        }
        
        return monthNames.indexOf(aMonth) - monthNames.indexOf(bMonth);
      });
    }
    
    // Calculate current MRR
    const currentMRR = await calculateCurrentMRR();
    
    // Calculate MRR growth
    const firstPeriodMRR = timeSeriesData.length > 0 ? timeSeriesData[0].revenue : 0;
    const lastPeriodMRR = timeSeriesData.length > 0 ? timeSeriesData[timeSeriesData.length - 1].revenue : 0;
    
    const mrrGrowth = firstPeriodMRR > 0 
      ? ((lastPeriodMRR - firstPeriodMRR) / firstPeriodMRR) * 100 
      : 0;
    
    return NextResponse.json({
      period,
      currentMRR,
      mrrGrowth: Math.round(mrrGrowth * 100) / 100,
      dailyData: unit === 'days' ? timeSeriesData : null,
      monthlyData: unit === 'months' ? timeSeriesData : null
    });
  } catch (error) {
    console.error("Error fetching revenue history metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch revenue history metrics" },
      { status: 500 }
    );
  }
}

// Helper function to calculate current MRR
async function calculateCurrentMRR() {
  // Get all active subscriptions
  const activeSubscriptions = await db.subscription.findMany({
    where: {
      isActive: true
    },
    include: {
      plan: true
    }
  });

  // Calculate MRR
  let mrr = 0;

  activeSubscriptions.forEach(subscription => {
    // Get base plan price
    const basePrice = subscription.billingInterval === 'month' 
      ? subscription.plan.price || 0
      : (subscription.plan.price || 0) / 12;
    
    // Add additional users cost
    const additionalUsersCost = subscription.additionalUsers * 
      (subscription.billingInterval === 'month' 
        ? subscription.plan.additionalUserFee || 0
        : (subscription.plan.additionalUserFee || 0) / 12);
    
    // Add additional storage cost (simplified calculation)
    const additionalStorageCost = subscription.additionalStorageGB * 10; // Assuming $10 per GB
    
    // Calculate total monthly revenue for this subscription
    const subscriptionMrr = basePrice + additionalUsersCost + additionalStorageCost;
    
    mrr += subscriptionMrr;
  });

  return Math.round(mrr * 100) / 100;
}
