"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { Loader2, Save, Trash2 } from "lucide-react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON>ialogFooter,
  <PERSON><PERSON><PERSON><PERSON>ogHead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/alert-dialog";
import { updateUserProfile, deleteUserAccount } from "@/services/src/user";

import { signOut } from "next-auth/react";
import { removeCookie } from "@/utils/cookies";
import { useLanguage } from "@/lib/language-context";

// Define the form schema with translations
const createProfileFormSchema = (t) =>
  z.object({
    forename: z.string().min(2, {
      message: t("auth.nameMinLength"),
    }),
    surname: z.string().min(2, {
      message: t("auth.nameMinLength"),
    }),
    email: z.string().email({
      message: t("auth.validEmail"),
    }),
    image: z.string().optional(),
  });

type ProfileFormValues = z.infer<ReturnType<typeof createProfileFormSchema>>;

type ProfilePageProps = {
  user: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
};

export default function ProfilePage({ user }: ProfilePageProps) {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  // Create the schema with translations
  const profileFormSchema = createProfileFormSchema(t);

  // Parse the name into forename and surname
  const nameParts = user?.name?.split(" ") || ["", ""];
  const defaultForename = nameParts[0] || "";
  const defaultSurname = nameParts.slice(1).join(" ") || "";

  // Initialize form with existing user data
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      forename: defaultForename,
      surname: defaultSurname,
      email: user?.email || "",
    },
  });

  async function onSubmit(values: ProfileFormValues) {
    setIsLoading(true);
    toast.loading(t("common.saving"));

    try {
      const response = await updateUserProfile(user?.id, values);

      if (response.error) {
        toast.remove();
        toast.error(response.error);
      } else {
        toast.remove();
        toast.success(t("profile.profileUpdated"));
        // Refresh the data
        router.refresh();
      }
    } catch (error) {
      console.error("Update profile error:", error);
      toast.remove();
      toast.error(t("profile.updateFailed"));
    } finally {
      setIsLoading(false);
    }
  }

  async function handleDeleteAccount() {
    setIsDeleting(true);
    toast.loading(t("profile.deletingAccount"));

    try {
      const response = await deleteUserAccount(user.id);

      if (response.error) {
        toast.remove();
        toast.error(response.error);
      } else {
        toast.remove();
        toast.success(t("profile.accountDeleted"));
        // Sign out and redirect to home page
        removeCookie("currentOrganizationId");
        removeCookie("userId");
        signOut({
          callbackUrl: "/",
        });
      }
    } catch (error) {
      console.error("Delete account error:", error);
      toast.remove();
      toast.error(t("profile.deleteFailed"));
    } finally {
      setIsDeleting(false);
    }
  }

  // Get user initials for avatar
  const getInitials = (name: string = "") => {
    return (
      name
        .split(" ")
        .map((part) => part[0])
        .join("")
        .toUpperCase()
        .substring(0, 2) || "?"
    );
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">{t("common.profile")}</h1>

      <div className="grid grid-cols-1 gap-6">
        {/* Profile Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>{t("profile.profileInformation")}</CardTitle>
            <CardDescription>
              {t("profile.updateProfileDetails")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-6">
              <Avatar className="h-16 w-16">
                <AvatarImage
                  src={user?.image || ""}
                  alt={user?.name || "User"}
                />
                <AvatarFallback className="text-lg">
                  {getInitials(user?.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-lg font-medium">{user?.name}</h3>
                <p className="text-sm text-muted-foreground">{user?.email}</p>
              </div>
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="forename"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.firstName")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("profile.firstNamePlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="surname"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.lastName")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("profile.lastNamePlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("common.email")}</FormLabel>
                      <FormControl>
                        <Input
                          disabled
                          placeholder={t("profile.emailPlaceholder")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("common.saving")}
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {t("common.saveChanges")}
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Danger Zone Card */}
        <Card className="border-destructive/20">
          <CardHeader>
            <CardTitle className="text-destructive">
              {t("profile.dangerZone")}
            </CardTitle>
            <CardDescription>
              {t("profile.deleteAccountWarning")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" disabled={isDeleting}>
                  {isDeleting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("profile.deletingAccount")}
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t("profile.deleteAccount")}
                    </>
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t("profile.areYouSure")}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t("profile.deleteAccountWarning")}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteAccount}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {t("profile.confirmDeleteAccount")}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
