"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Key, More<PERSON><PERSON><PERSON><PERSON>, Plus, Trash } from "lucide-react";
import { toast } from "react-hot-toast";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Mocked sample API key data
type A<PERSON><PERSON><PERSON> = {
  id: string;
  name: string;
  key: string;
  createdAt: string;
  lastUsed?: string;
};

const mockApiKeys: ApiKey[] = [
  {
    id: "1",
    name: "Production API Key",
    key: "sk_prod_1234567890abcdef",
    createdAt: "2023-11-15T10:00:00Z",
    lastUsed: "2023-11-30T15:23:45Z",
  },
  {
    id: "2",
    name: "Development API Key",
    key: "sk_dev_0987654321fedcba",
    createdAt: "2023-10-20T08:30:00Z",
    lastUsed: "2023-11-29T09:15:20Z",
  },
  {
    id: "3",
    name: "Testing API Key",
    key: "sk_test_abcdef1234567890",
    createdAt: "2023-09-10T14:20:00Z",
  },
];

export function ApiKeysTable({ tenantId }: { tenantId: string }) {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>(mockApiKeys);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newKeyName, setNewKeyName] = useState("");
  const [generatedKey, setGeneratedKey] = useState<string | null>(null);
  const [selectedKeyId, setSelectedKeyId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateKey = () => {
    if (!newKeyName.trim()) {
      toast.error("Please enter a name for your API key");
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      const newKey = {
        id: Math.random().toString(36).substring(2, 11),
        name: newKeyName,
        key: `sk_${Math.random().toString(36).substring(2, 15)}_${Math.random().toString(36).substring(2, 15)}`,
        createdAt: new Date().toISOString(),
      };

      setApiKeys([newKey, ...apiKeys]);
      setGeneratedKey(newKey.key);
      setNewKeyName("");
      setIsLoading(false);
    }, 1000);
  };

  const handleDeleteKey = () => {
    if (!selectedKeyId) return;

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setApiKeys(apiKeys.filter((key) => key.id !== selectedKeyId));
      setSelectedKeyId(null);
      setIsDeleteDialogOpen(false);
      setIsLoading(false);
      toast.success("API key deleted successfully");
    }, 1000);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>API Keys</CardTitle>
            <CardDescription>
              Manage your API keys for accessing the API.
            </CardDescription>
          </div>
          <Button
            onClick={() => {
              setGeneratedKey(null);
              setIsCreateDialogOpen(true);
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Create API Key
          </Button>
        </CardHeader>
        <CardContent>
          {apiKeys.length === 0 ? (
            <div className="flex h-32 items-center justify-center rounded-md border border-dashed">
              <div className="text-center">
                <Key className="mx-auto h-8 w-8 text-muted-foreground" />
                <h3 className="mt-2 text-sm font-medium">No API keys</h3>
                <p className="mt-1 text-sm text-muted-foreground">
                  You haven't created any API keys yet.
                </p>
              </div>
            </div>
          ) : (
            <div className="rounded-md border">
              <div className="grid grid-cols-4 border-b px-4 py-3 font-medium text-sm">
                <div>Name</div>
                <div>API Key</div>
                <div>Created</div>
                <div className="text-right">Actions</div>
              </div>
              <div className="divide-y">
                {apiKeys.map((apiKey) => (
                  <div key={apiKey.id} className="grid grid-cols-4 px-4 py-3 text-sm">
                    <div className="font-medium">{apiKey.name}</div>
                    <div className="font-mono">••••••••••••{apiKey.key.slice(-4)}</div>
                    <div className="text-muted-foreground">
                      {formatDate(apiKey.createdAt)}
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(apiKey.key)}
                      >
                        <Copy className="h-3.5 w-3.5" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-3.5 w-3.5" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => {
                              setSelectedKeyId(apiKey.id);
                              setIsDeleteDialogOpen(true);
                            }}
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create API Key Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {generatedKey ? "API Key Created" : "Create API Key"}
            </DialogTitle>
            <DialogDescription>
              {generatedKey
                ? "Your API key has been created. Please copy it now as you won't be able to see it again."
                : "Create a new API key for accessing your organization's resources."}
            </DialogDescription>
          </DialogHeader>

          {generatedKey ? (
            <div className="space-y-4">
              <div className="rounded-md bg-muted p-4">
                <div className="font-mono text-sm break-all">{generatedKey}</div>
              </div>
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  className="mr-2"
                  onClick={() => copyToClipboard(generatedKey)}
                >
                  <Copy className="mr-2 h-4 w-4" />
                  Copy
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Done
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <label
                    htmlFor="api-key-name"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    API Key Name
                  </label>
                  <Input
                    id="api-key-name"
                    placeholder="Production API Key"
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Give your API key a name to remember what it's used for.
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleCreateKey} disabled={isLoading}>
                  {isLoading ? "Creating..." : "Create API Key"}
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete API Key Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete API Key</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this API key? This action cannot
              be undone and applications using this key will no longer be able
              to access the API.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteKey}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isLoading}
            >
              {isLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}