"use client";

import { useState, useEffect } from "react";
import {
  Folder,
  File,
  Upload,
  Plus,
  FolderPlus,
  MoreVertical,
  Trash2,
  Edit2,
  Share2,
  List,
  LayoutGrid,
  LayoutList,
  Globe,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useFolderOperations } from "@/hooks/use-folder-operations";
import { useFileOperations } from "@/hooks/use-file-operations";
import { useLanguage } from "@/lib/language-context";
import { fetchWorkspaceBySlugWithCache } from "@/utils/workspace";
import { CreateGuard, ReadGuard } from "@/components/permission-guard";
import MoreButton from "./more-button";
import { UrlImportButton } from "@/components/url-import/url-import-button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function WorkspacePage({
  workspaceSlug,
  pageId,
  page,
  tenantId,
  usageSummary,
}) {
  const { t } = useLanguage();
  const router = useRouter();
  const [workspaceTitle, setWorkspaceTitle] = useState("");

  // Fetch the workspace name when component mounts
  useEffect(() => {
    const getWorkspaceName = async () => {
      try {
        const workspace = await fetchWorkspaceBySlugWithCache(workspaceSlug);
        if (workspace && workspace.name) {
          setWorkspaceTitle(workspace.name);
        } else {
          // Fallback to formatting the slug if workspace data is not available
          const formattedTitle = workspaceSlug
            .split("-")
            .slice(0, -1)
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
          setWorkspaceTitle(formattedTitle);
        }
      } catch (error) {
        console.error("Error fetching workspace name:", error);
        // Fallback to formatting the slug
        const formattedTitle = workspaceSlug
          .split("-")
          .slice(0, -1)
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        setWorkspaceTitle(formattedTitle);
      }
    };

    getWorkspaceName();
  }, [workspaceSlug]);

  // Check if the page is synced with Google Drive or OneDrive
  const isSyncedWithCloud = page?.gDriveFolderId || page?.oneDriveFolderId;
  const syncProvider =
    page?.gDriveFolderId && page?.oneDriveFolderId
      ? "both Google Drive and OneDrive"
      : page?.gDriveFolderId
        ? "Google Drive"
        : page?.oneDriveFolderId
          ? "OneDrive"
          : null;

  // State for page, folders and files
  const [folders] = useState(page?.folders ?? []);

  const [isLoading, setIsLoading] = useState(false);
  const [updateData, setUpdateData]: any = useState({});
  const [files, setFiles] = useState(page?.files ?? []);
  const [newFolderName, setNewFolderName] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateFolderOpen, setIsCreateFolderOpen]: any = useState(null);
  const [isUploadOpen, setIsUploadOpen] = useState(false);

  // View options - can be "tiles", "grid", or "list"
  const [viewMode, setViewMode] = useState<"tiles" | "grid" | "list">("tiles");
  const { handleCreateFolder, handleRenameFolder, handleDeleteFolder } =
    useFolderOperations({
      workspaceSlug,
      tenantId,
      pageId: pageId as string,
      setIsLoading,
    });

  const { handleFileUpload, handleRenameFile, handleDeleteFile } =
    useFileOperations({
      workspaceSlug,
      tenantId,
      pageId: pageId as string,
      setIsLoading,
      usageSummary,
    });

  // Create a new folder
  const handleCreateFolderClick = async () => {
    // Create folder as a child of the current folder
    if (await handleCreateFolder(newFolderName)) {
      setIsCreateFolderOpen(null);
      setNewFolderName("");
    }
  };

  const handleRename = async () => {
    if (updateData?.type === "folder") {
      if (await handleRenameFolder(updateData.id, newFolderName)) {
        setNewFolderName("");
        setUpdateData(null);
        setIsCreateFolderOpen(null);
      }
    } else {
      if (await handleRenameFile(updateData?.id, newFolderName)) {
        setNewFolderName("");
        setUpdateData(null);
        setIsCreateFolderOpen(null);
      }
    }
  };

  // Handle file upload
  const handleFileUploadChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileUpload(e, "", files, setFiles, setIsUploadOpen);
  };

  // Delete an item
  const handleDelete = async (id: string, type: "folder" | "file") => {
    if (type === "folder") {
      await handleDeleteFolder(id);
    } else {
      await handleDeleteFile(id);
    }
  };

  // Filter items based on search term
  const filteredFolders = folders.filter((folder: any) =>
    folder.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredFiles = files.filter(
    (file: any) =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.extension?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get file icon based on extension
  const getFileIcon = (extension?: string) => {
    switch (extension?.toLowerCase()) {
      case "pdf":
        return <File className="h-6 w-6 text-red-500" />;
      case "doc":
      case "docx":
        return <File className="h-6 w-6 text-blue-500" />;
      case "xls":
      case "xlsx":
        return <File className="h-6 w-6 text-green-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <File className="h-6 w-6 text-purple-500" />;
      default:
        return <File className="h-6 w-6 text-gray-500" />;
    }
  };

  // Get vectorization status badge
  const getVectorizationStatusBadge = (status?: string) => {
    console.log("Status:", status);

    switch (status) {
      case "PENDING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Pending
          </span>
        );
      case "PROCESSING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Processing
          </span>
        );
      case "COMPLETED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Indexed
          </span>
        );
      case "FAILED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Failed
          </span>
        );
      default:
        return null;
    }
  };
  if (!page) {
    return (
      <div className="h-full flex items-center justify-center">
        {t("workspace.loading")}
      </div>
    );
  }

  return (
    <div className="h-full px-1 py-2 md:px-4">
      {/* Header with title */}
      <div className="flex items-center justify-between">
        <Link href={`/workspace/${workspaceSlug}/pages`} className="mb-6">
          <h1 className="text-2xl font-bold md:text-3xl tracking-tight">
            {page.name}
          </h1>
          <p className="text-muted-foreground mt-1">
            {workspaceTitle} {t("workspace.workspace")}
            <span> &gt; {page.name}</span>
          </p>
        </Link>
        <Button
          onClick={() => router.push(`/workspace/${workspaceSlug}/members`)}
        >
          <Plus className="h-4 w-4 mr-2" />
          {t("workspace.addRemoveMember")}
        </Button>
      </div>

      {/* Cloud sync notification banner */}
      {isSyncedWithCloud && (
        <div className="mb-6 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 p-4 rounded-md">
          <div className="flex items-start">
            <div className="flex-shrink-0 pt-0.5">
              <svg
                className="h-5 w-5 text-blue-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {t("workspace.syncedWithCloud", { provider: syncProvider })}
              </p>
              {(page?.gDriveFolderId || page?.oneDriveFolderId) && (
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  <span className="font-semibold">
                    {t("integration.syncDisclaimer")}
                  </span>
                </p>
              )}
              {page?.gDriveFolderName && (
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  <span className="font-semibold">Google Drive path:</span>{" "}
                  {page.gDriveFolderName}
                </p>
              )}
              {page?.oneDriveFolderName && (
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  <span className="font-semibold">SharePoint path:</span>{" "}
                  {page.oneDriveFolderName}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* SharePoint-like command bar */}
      <div className="flex flex-col md:flex-row justify-between gap-3 mb-6 bg-muted/30 p-3 rounded-md">
        <div className="flex flex-wrap gap-2">
          {/* + New button - disabled when synced with cloud */}
          <CreateGuard resource="FOLDER">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="default"
                  size="sm"
                  className="gap-2"
                  disabled={isSyncedWithCloud}
                >
                  <Plus className="h-4 w-4" />
                  {t("workspace.new")}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() => setIsCreateFolderOpen("create")}
                >
                  <FolderPlus className="h-4 w-4 mr-2" />
                  {t("workspace.newFolder")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CreateGuard>

          {/* Upload button - disabled when synced with cloud */}
          <CreateGuard resource="FILE">
            <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                  disabled={isSyncedWithCloud}
                >
                  <Upload className="h-4 w-4" />
                  {t("workspace.upload")}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t("workspace.uploadFiles")}</DialogTitle>
                  <DialogDescription>
                    {t("workspace.uploadFilesDescription")}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <Input
                    type="file"
                    multiple
                    onChange={handleFileUploadChange}
                    className="cursor-pointer"
                  />
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsUploadOpen(false)}
                  >
                    {t("common.cancel")}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CreateGuard>

          {/* URL Import button - disabled when synced with cloud */}
          <UrlImportButton
            workspaceSlug={workspaceSlug}
            workspaceId={page.workspaceId}
            pageId={pageId}
            disabled={isSyncedWithCloud}
          />
        </div>

        <div className="flex gap-3 items-center">
          {/* Search */}
          <div className="relative">
            <Input
              type="search"
              placeholder={t("workspace.searchFilesAndFolders")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-9 max-w-64 min-w-40 pr-8"
            />
            <svg
              className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          {/* View options */}
          <div className="flex gap-1 items-center">
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "list"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.listView")}
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "grid"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.gridView")}
              onClick={() => setViewMode("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "tiles"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.tileView")}
              onClick={() => setViewMode("tiles")}
            >
              <LayoutList className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      {/* All items (folders and files) */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">{t("workspace.allItems")}</h2>
          {filteredFolders.length + filteredFiles.length > 0 && (
            <p className="text-sm text-muted-foreground">
              {filteredFolders.length} {t("workspace.folder")}
              {filteredFolders.length !== 1 ? t("workspace.folders") : ""},{" "}
              {filteredFiles.length} {t("workspace.file")}
              {filteredFiles.length !== 1 ? "s" : ""}
            </p>
          )}
        </div>

        {/* List View */}
        {viewMode === "list" && (
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="text-left px-4 py-2 font-medium text-sm">
                    {t("workspace.name")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm hidden md:table-cell">
                    {t("workspace.modified")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm hidden lg:table-cell">
                    {t("workspace.type")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm hidden md:table-cell">
                    {t("workspace.size")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm w-20">
                    {t("workspace.actions")}
                  </th>
                </tr>
              </thead>
              <tbody>
                {/* Folders first */}
                {filteredFolders.map((folder: any) => (
                  <tr key={folder.id} className="border-t hover:bg-muted/20">
                    <td className="px-4 py-2">
                      <ReadGuard
                        resource="FOLDER"
                        fallback={
                          <div className="flex items-center gap-2 opacity-50 cursor-not-allowed">
                            <Folder className="h-5 w-5 text-blue-500 shrink-0" />
                            <span
                              className="overflow-hidden text-ellipsis whitespace-nowrap font-medium max-w-[200px] inline-block"
                              title={`No access`}
                            >
                              No access
                              <span className="ml-1 text-xs text-red-500">
                                🔒
                              </span>
                            </span>
                          </div>
                        }
                      >
                        <Link
                          href={`/workspace/${workspaceSlug}/folder/${folder.id}?page=${pageId}`}
                          className="flex items-center gap-2"
                        >
                          <Folder className="h-5 w-5 text-blue-500 shrink-0" />
                          <span
                            className="overflow-hidden text-ellipsis whitespace-nowrap font-medium max-w-[200px] inline-block"
                            title={folder.name}
                          >
                            {folder.name}
                          </span>
                        </Link>
                      </ReadGuard>
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      {new Date(folder.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden lg:table-cell">
                      {t("workspace.folder")}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      —
                    </td>
                    <td className="px-4 py-2 w-20">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>
                            {t("workspace.actions")}
                          </DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Edit2 className="h-4 w-4 mr-2" />
                            {t("workspace.rename")}
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Share2 className="h-4 w-4 mr-2" />
                            {t("workspace.share")}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDelete(folder.id, "folder")}
                            className="text-red-500"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            {t("common.delete")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}

                {/* Then files */}
                {filteredFiles.map((file: any) => (
                  <tr key={file.id} className="border-t hover:bg-muted/20">
                    <td className="px-4 py-2">
                      <ReadGuard
                        resource="FILE"
                        fallback={
                          <div className="flex items-center gap-2 opacity-50 cursor-not-allowed">
                            {getFileIcon(file.extension)}
                            <span
                              className="overflow-hidden text-ellipsis whitespace-nowrap font-medium max-w-[200px] inline-block"
                              title={`No access`}
                            >
                              No access
                              <span className="ml-1 text-xs text-red-500">
                                🔒
                              </span>
                            </span>
                          </div>
                        }
                      >
                        <Link
                          href={`/workspace/${workspaceSlug}/file/${file.id}?page=${pageId}`}
                          className="flex items-center gap-2"
                        >
                          <div>
                            <span className="truncate font-medium">
                              {file.name}
                            </span>
                            <div className="mt-1">
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  {getVectorizationStatusBadge(
                                    file?.vectorizationStatus
                                  )}
                                </TooltipTrigger>
                                <TooltipContent>
                                  {t("workspace.vectorizationStatusTooltip")}
                                </TooltipContent>
                              </Tooltip>
                            </div>
                          </div>
                        </Link>
                      </ReadGuard>
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      {new Date(file.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden lg:table-cell">
                      {file.extension ? `.${file.extension}` : "File"}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      {file.size}
                    </td>
                    <td className="px-4 py-2 w-20">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>
                            {t("workspace.actions")}
                          </DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Edit2 className="h-4 w-4 mr-2" />
                            {t("workspace.rename")}
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Share2 className="h-4 w-4 mr-2" />
                            {t("workspace.share")}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDelete(file.id, "file")}
                            className="text-red-500"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            {t("workspace.delete")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}

                {filteredFolders.length === 0 && filteredFiles.length === 0 && (
                  <tr>
                    <td
                      colSpan={5}
                      className="px-4 py-8 text-center text-muted-foreground"
                    >
                      {t("workspace.noItemsFound")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Grid View */}
        {viewMode === "grid" && (
          <div>
            {filteredFolders.length > 0 || filteredFiles.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                {/* Folders first */}
                {filteredFolders.map((folder: any) => (
                  <ReadGuard
                    key={folder.id}
                    resource="FOLDER"
                    fallback={
                      <div className="group relative bg-card opacity-50 rounded-md border p-3 flex flex-col items-center text-center cursor-not-allowed">
                        <Folder className="h-12 w-12 text-blue-500 mb-2" />
                        <div className="w-full px-1">
                          <span
                            className="text-sm font-medium overflow-hidden text-ellipsis whitespace-nowrap inline-block w-full"
                            title={`No access`}
                          >
                            No access
                            <span className="ml-1 text-xs text-red-500">
                              🔒
                            </span>
                          </span>
                        </div>
                        {/* <span className="text-xs text-muted-foreground mt-1">
                          {t("workspace.folder")}
                        </span> */}
                      </div>
                    }
                  >
                    <Link
                      href={`/workspace/${workspaceSlug}/folder/${folder.id}?page=${pageId}`}
                      className="group relative bg-card hover:bg-muted/20 rounded-md border p-3 flex flex-col items-center text-center"
                    >
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <MoreButton
                          handleDelete={handleDelete}
                          file={folder}
                          setIsCreateFolderOpen={setIsCreateFolderOpen}
                          type={"folder"}
                          setUpdateData={setUpdateData}
                          setNewFolderName={setNewFolderName}
                        />
                      </div>
                      <Folder className="h-12 w-12 text-blue-500 mb-2" />
                      <div className="w-full px-1">
                        <span
                          className="text-sm font-medium overflow-hidden text-ellipsis whitespace-nowrap inline-block w-full"
                          title={folder.name}
                        >
                          {folder.name}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground mt-1">
                        {t("workspace.folder")}
                      </span>
                    </Link>
                  </ReadGuard>
                ))}

                {/* Then files */}
                {filteredFiles.map((file: any) => (
                  <ReadGuard
                    key={file.id}
                    resource="FILE"
                    fallback={
                      <div className="group relative bg-card opacity-50 rounded-md border p-3 flex flex-col items-center text-center cursor-not-allowed">
                        <div className="h-12 w-12 flex items-center justify-center mb-2">
                          {getFileIcon(file.extension)}
                        </div>
                        <div className="w-full px-1">
                          <span
                            className="text-sm font-medium overflow-hidden text-ellipsis whitespace-nowrap inline-block w-full"
                            title={`No access`}
                          >
                            No access
                            <span className="ml-1 text-xs text-red-500">
                              🔒
                            </span>
                          </span>
                        </div>
                        {/* <span className="text-xs text-muted-foreground mt-1">
                          {file.extension
                            ? `.${file.extension}`
                            : t("workspace.file")}
                          · {file.size}
                        </span> */}
                      </div>
                    }
                  >
                    <Link
                      href={`/workspace/${workspaceSlug}/file/${file.id}?page=${pageId}`}
                      className="group relative bg-card hover:bg-muted/20 rounded-md border p-3 flex flex-col items-center text-center"
                    >
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <MoreButton
                          handleDelete={handleDelete}
                          file={file}
                          setIsCreateFolderOpen={setIsCreateFolderOpen}
                          type={"file"}
                          setNewFolderName={setNewFolderName}
                          setUpdateData={setUpdateData}
                        />
                      </div>
                      <div className="h-12 w-12 flex items-center justify-center mb-2">
                        {getFileIcon(file.extension)}
                      </div>
                      <div className="w-full px-1">
                        <span
                          className="text-sm font-medium overflow-hidden text-ellipsis whitespace-nowrap inline-block w-full"
                          title={file.name}
                        >
                          {file.name}
                        </span>
                        <div className="mt-1">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              {getVectorizationStatusBadge(
                                file?.vectorizationStatus
                              )}
                            </TooltipTrigger>
                            <TooltipContent>
                              {t("workspace.vectorizationStatusTooltip")}
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </div>
                      <span className="text-xs text-muted-foreground mt-1">
                        {file.extension
                          ? `.${file.extension}`
                          : t("workspace.file")}
                        · {file.size}
                      </span>
                    </Link>
                  </ReadGuard>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-8">
                {t("workspace.noItemsFound")}
              </p>
            )}
          </div>
        )}

        {/* Tiles View */}
        {viewMode === "tiles" && (
          <div>
            {/* Folders section */}
            {filteredFolders.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  {t("workspace.folders")}
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {filteredFolders.map((folder: any) => (
                    <ReadGuard
                      key={folder.id}
                      resource="FOLDER"
                      fallback={
                        <Card className="group opacity-50">
                          <CardHeader className="p-4 pb-2">
                            <div className="flex justify-between items-start">
                              <div className="flex items-center flex-1 min-w-0 pr-4">
                                <Folder className="h-8 w-8 mr-2 text-blue-500 flex-shrink-0" />
                                <CardTitle
                                  className="text-base overflow-hidden text-ellipsis whitespace-nowrap w-full"
                                  title={` No access`}
                                >
                                  No access
                                  <span className="ml-1 text-xs text-red-500">
                                    🔒
                                  </span>
                                </CardTitle>
                              </div>
                            </div>
                          </CardHeader>
                          {/* <CardContent className="p-4 pt-2">
                            <p className="text-sm text-muted-foreground">
                              {t("workspace.modified")}{" "}
                              {new Date(folder.createdAt).toLocaleDateString()}
                            </p>
                          </CardContent> */}
                          <CardFooter className="p-4 pt-0">
                            <Button
                              variant="ghost"
                              disabled
                              className="w-full justify-start p-0 h-8 cursor-not-allowed"
                            >
                              {t("workspace.noAccess")}
                            </Button>
                          </CardFooter>
                        </Card>
                      }
                    >
                      <Card className="group hover:border-primary transition-colors">
                        <CardHeader className="p-4 pb-2">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center flex-1 min-w-0 pr-4">
                              <Folder className="h-8 w-8 mr-2 text-blue-500 flex-shrink-0" />
                              <CardTitle
                                className="text-base overflow-hidden text-ellipsis whitespace-nowrap w-full"
                                title={folder.name}
                              >
                                {folder.name}
                              </CardTitle>
                            </div>
                            <div className="flex-shrink-0">
                              <MoreButton
                                handleDelete={handleDelete}
                                file={folder}
                                setIsCreateFolderOpen={setIsCreateFolderOpen}
                                type={"folder"}
                                setNewFolderName={setNewFolderName}
                                setUpdateData={setUpdateData}
                                isSyncedWithCloud={isSyncedWithCloud}
                              />
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="p-4 pt-2">
                          <p className="text-sm text-muted-foreground">
                            {t("workspace.modified")}{" "}
                            {new Date(folder.createdAt).toLocaleDateString()}
                          </p>
                        </CardContent>
                        <CardFooter className="p-4 pt-0">
                          <Button
                            variant="ghost"
                            asChild
                            className="w-full justify-start p-0 h-8"
                          >
                            <Link
                              href={`/workspace/${workspaceSlug}/folder/${folder.id}?page=${pageId}`}
                            >
                              {t("workspace.openFolder")}
                            </Link>
                          </Button>
                        </CardFooter>
                      </Card>
                    </ReadGuard>
                  ))}
                </div>
              </div>
            )}

            {/* Files section */}
            {filteredFiles.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  {t("workspace.files")}
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {filteredFiles.map((file: any) => (
                    <ReadGuard
                      key={file.id}
                      resource="FILE"
                      fallback={
                        <Card className="group opacity-50">
                          <CardHeader className="p-4 pb-2">
                            <div className="flex justify-between items-start">
                              <div className="flex items-center flex-1 min-w-0 pr-4">
                                {getFileIcon(file.extension)}
                                <div className="ml-2 overflow-hidden w-full">
                                  <div className="w-full overflow-hidden">
                                    <CardTitle
                                      className="text-base overflow-hidden text-ellipsis whitespace-nowrap w-full"
                                      title={`No access`}
                                    >
                                      No access
                                      <span className="ml-1 text-xs text-red-500">
                                        🔒
                                      </span>
                                    </CardTitle>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </CardHeader>

                          <CardFooter className="p-4 pt-0">
                            <Button
                              variant="ghost"
                              disabled
                              className="w-full justify-start p-0 h-8 cursor-not-allowed"
                            >
                              {t("workspace.noAccess")}
                            </Button>
                          </CardFooter>
                        </Card>
                      }
                    >
                      <Card className="group hover:border-primary transition-colors">
                        <CardHeader className="p-4 pb-2">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center flex-1 min-w-0 pr-4">
                              {getFileIcon(file.extension)}
                              <div className="ml-2 overflow-hidden w-full">
                                <div className="w-full overflow-hidden">
                                  <CardTitle
                                    className="text-base overflow-hidden text-ellipsis whitespace-nowrap w-full"
                                    title={file.name}
                                  >
                                    {file.name}
                                  </CardTitle>
                                </div>
                                <div className="mt-1">
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      {getVectorizationStatusBadge(
                                        file?.vectorizationStatus
                                      )}
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      {t(
                                        "workspace.vectorizationStatusTooltip"
                                      )}
                                    </TooltipContent>
                                  </Tooltip>
                                </div>
                                <p className="text-xs text-muted-foreground">
                                  {file.extension ? `.${file.extension}` : ""} ·{" "}
                                  {file.size}
                                </p>
                              </div>
                            </div>
                            <div className="flex-shrink-0">
                              <MoreButton
                                handleDelete={handleDelete}
                                file={file}
                                setIsCreateFolderOpen={setIsCreateFolderOpen}
                                type={"file"}
                                setNewFolderName={setNewFolderName}
                                setUpdateData={setUpdateData}
                                isSyncedWithCloud={isSyncedWithCloud}
                              />
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="p-4 pt-2">
                          <p className="text-sm text-muted-foreground">
                            {t("workspace.modified")}{" "}
                            {new Date(file.createdAt).toLocaleDateString()}
                          </p>
                        </CardContent>
                        <CardFooter className="p-4 pt-0">
                          <Button
                            variant="ghost"
                            asChild
                            className="w-full justify-start p-0 h-8"
                          >
                            <Link
                              href={`/workspace/${workspaceSlug}/file/${file.id}?page=${pageId}`}
                            >
                              {t("workspace.viewFile")}
                            </Link>
                          </Button>
                        </CardFooter>
                      </Card>
                    </ReadGuard>
                  ))}
                </div>
              </div>
            )}

            {filteredFolders.length === 0 && filteredFiles.length === 0 && (
              <p className="text-muted-foreground text-center py-8">
                {t("workspace.noItemsFound")}
              </p>
            )}
          </div>
        )}
      </div>
      {/* Create Folder Dialog */}
      <CreateFolderDialog
        isLoading={isLoading}
        title={
          updateData?.type === "folder"
            ? t("workspace.renameFolder")
            : t("workspace.renameFile")
        }
        subTitle={
          updateData?.type === "folder"
            ? t("workspace.enterFolderName")
            : t("workspace.enterFileName")
        }
        isOpen={isCreateFolderOpen === "rename"}
        onClose={() => {
          setIsCreateFolderOpen(null);
          setNewFolderName("");
          setUpdateData(null);
        }}
        folderName={newFolderName}
        onFolderNameChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setNewFolderName(e.target.value)
        }
        onCreateFolder={() => {
          handleRename();
        }}
        label={
          updateData?.type === "folder"
            ? t("workspace.folderName")
            : t("workspace.newFileName")
        }
        labelPlaceholder={
          updateData?.type === "folder"
            ? t("workspace.enterFolderNamePlaceholder")
            : t("workspace.enterFileNamePlaceholder")
        }
        buttonText={
          updateData?.type === "folder"
            ? t("workspace.renameFolder")
            : t("workspace.renameFile")
        }
      />
      <CreateFolderDialog
        isLoading={isLoading}
        title={t("workspace.createFolder")}
        subTitle={t("workspace.enterFolderNamePlaceholder")}
        isOpen={isCreateFolderOpen === "create"}
        onClose={() => {
          setIsCreateFolderOpen(null);
          setNewFolderName("");
          setUpdateData(null);
        }}
        folderName={newFolderName}
        onFolderNameChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setNewFolderName(e.target.value)
        }
        onCreateFolder={() => {
          handleCreateFolderClick();
        }}
        label={t("workspace.folderName")}
        labelPlaceholder={t("workspace.enterFolderNamePlaceholder")}
        buttonText={t("workspace.createFolder")}
      />
    </div>
  );
}

// MoreButton component has been moved to its own file

export const CreateFolderDialog = ({
  title,
  subTitle,
  label,
  buttonText,
  labelPlaceholder,
  isOpen,
  onClose,
  folderName,
  onFolderNameChange,
  onCreateFolder,
  isLoading,
}) => {
  const { t } = useLanguage();
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{subTitle}</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="folderName">{label}</Label>
            <Input
              id="folderName"
              value={folderName}
              onChange={onFolderNameChange}
              placeholder={labelPlaceholder}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              onClose();
            }}
          >
            {t("common.cancel")}
          </Button>
          <Button disabled={isLoading} onClick={onCreateFolder}>
            {buttonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
