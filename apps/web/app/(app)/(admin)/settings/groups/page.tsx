"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useLanguage } from "@/lib/language-context";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getGroups,
  removeUserFromGroup,
  removeWorkspaceFromGroup,
} from "@/services/src/group";
import { CreateGroupDialog } from "@/components/model/create-group-dialog";
import { AddUserToGroupDialog } from "@/components/model/add-user-to-group-dialog";
import { AssignWorkspaceToGroupDialog } from "@/components/model/assign-workspace-to-group-dialog";
import {
  Plus,
  UserPlus,
  FolderPlus,
  Trash2,
  Users,
  ChevronsUpDown,
} from "lucide-react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { getCookie } from "@/utils/cookies";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";

// Define types for our components
interface Group {
  id: string;
  name: string;
  description?: string;
  tenantId: string;
  groupMembers: GroupMember[];
  groupWorkspaces: GroupWorkspace[];
}

interface GroupMember {
  id: string;
  userId: string;
  groupId: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
}

interface GroupWorkspace {
  id: string;
  groupId: string;
  workspaceId: string;
  workspace: {
    id: string;
    name: string;
    description?: string;
  };
}

export default function GroupsPage() {
  // tenantSlug is used in the dependency array of useEffect
  const { data: session } = useSession();
  const { t } = useLanguage();
  const router = useRouter();
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [activeTab, setActiveTab] = useState("members");
  const [searchTerm, setSearchTerm] = useState("");
  const [openGroupSelector, setOpenGroupSelector] = useState(false);
  const tenantId = getCookie("currentOrganizationId") as string;
  const userId = getCookie("userId") as string;

  useEffect(() => {
    const fetchGroups = async () => {
      if (userId && tenantId) {
        try {
          setLoading(true);
          const result = await getGroups(userId, tenantId);
          console.log({ result });
          if (result?.data) {
            setGroups(result.data);
            if (result.data.length > 0 && !selectedGroup) {
              setSelectedGroup(result.data[0]);
            }
          }
        } catch (error) {
          console.error("Error fetching groups:", error);
          toast.error(t("groups.failedToFetchGroups"));
        } finally {
          setLoading(false);
        }
      }
    };

    fetchGroups();
  }, [session, selectedGroup]);

  const handleRemoveUser = async (userId: string) => {
    if (!selectedGroup || !tenantId) return;

    try {
      const result = await removeUserFromGroup(
        selectedGroup.id,
        userId,
        tenantId
      );
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.userRemovedFromGroup"));

      // Update the local state
      setGroups(
        groups.map((group) => {
          if (group.id === selectedGroup.id) {
            return {
              ...group,
              groupMembers: group.groupMembers.filter(
                (member) => member.userId !== userId
              ),
            };
          }
          return group;
        })
      );

      // Update the selected group
      setSelectedGroup({
        ...selectedGroup,
        groupMembers: selectedGroup.groupMembers.filter(
          (member) => member.userId !== userId
        ),
      });

      router.refresh();
    } catch (error) {
      toast.error(t("groups.failedToRemoveUser"));
    }
  };

  const handleRemoveWorkspace = async (workspaceId: string) => {
    if (!selectedGroup || !tenantId) return;

    try {
      const result = await removeWorkspaceFromGroup(
        selectedGroup.id,
        workspaceId,
        tenantId
      );
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.workspaceRemovedFromGroup"));

      // Update the local state
      setGroups(
        groups.map((group) => {
          if (group.id === selectedGroup.id) {
            return {
              ...group,
              groupWorkspaces: group.groupWorkspaces.filter(
                (ws) => ws.workspaceId !== workspaceId
              ),
            };
          }
          return group;
        })
      );

      // Update the selected group
      setSelectedGroup({
        ...selectedGroup,
        groupWorkspaces: selectedGroup.groupWorkspaces.filter(
          (ws) => ws.workspaceId !== workspaceId
        ),
      });

      router.refresh();
    } catch (error) {
      toast.error(t("groups.failedToRemoveWorkspace"));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">{t("common.loading")}</h2>
          <p className="text-muted-foreground">{t("groups.loadingGroups")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      {groups.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <Card className="w-full max-w-md">
            <CardContent className="flex flex-col items-center justify-center p-8">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <UserPlus className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-xl font-semibold mb-2">
                {t("groups.noGroupsYet")}
              </h2>
              <p className="text-muted-foreground mb-6 text-center">
                {t("groups.createFirstGroup")}
              </p>
              <CreateGroupDialog
                tenantId={tenantId}
                trigger={
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    {t("groups.createGroup")}
                  </Button>
                }
              />
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="space-y-2">
          {/* Modern group selector with search */}
          <div className="flex justify-between items-center my-4">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
              <div className="flex items-center gap-3">
                <Popover
                  open={openGroupSelector}
                  onOpenChange={setOpenGroupSelector}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={openGroupSelector}
                      className="w-full md:w-64 justify-between"
                    >
                      <div className="flex items-center">
                        <Users className="mr-2 h-4 w-4" />
                        {selectedGroup
                          ? selectedGroup.name
                          : t("groups.selectGroup")}
                      </div>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command>
                      <CommandInput
                        placeholder={t("groups.searchGroups")}
                        value={searchTerm}
                        onValueChange={setSearchTerm}
                      />
                      <CommandEmpty>{t("groups.noGroupsFound")}</CommandEmpty>
                      <CommandGroup>
                        {groups.map((group) => (
                          <CommandItem
                            key={group.id}
                            value={group.id}
                            onSelect={() => {
                              setSelectedGroup(group);
                              setOpenGroupSelector(false);
                            }}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span>{group.name}</span>
                              <span className="text-xs bg-muted text-muted-foreground px-2 py-0.5 rounded-full">
                                {group.groupMembers.length}{" "}
                                {group.groupMembers.length === 1
                                  ? t("common.user")
                                  : t("common.users")}
                              </span>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded-md">
                  {groups.length}{" "}
                  {groups.length === 1 ? t("groups.group") : t("groups.groups")}
                </span>
              </div>
            </div>
            <CreateGroupDialog
              tenantId={tenantId}
              trigger={
                <Button size="default" className="gap-2">
                  <Plus className="h-4 w-4" />
                  {t("groups.createGroup")}
                </Button>
              }
            />
          </div>

          {/* Content area */}
          {selectedGroup ? (
            <div className="space-y-6">
              <Card className="border shadow-sm hover:shadow-md transition-all duration-200">
                <CardContent className="p-0">
                  <Tabs
                    defaultValue="members"
                    value={activeTab}
                    onValueChange={setActiveTab}
                    className="w-full"
                  >
                    <div className="border-b">
                      <div className="container">
                        <TabsList className="h-12 w-full justify-start rounded-none border-b-0 bg-transparent p-0">
                          <TabsTrigger
                            value="members"
                            className="h-12 rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                          >
                            {t("groups.members")}
                          </TabsTrigger>
                          <TabsTrigger
                            value="workspaces"
                            className="h-12 rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                          >
                            {t("groups.workspaces")}
                          </TabsTrigger>
                        </TabsList>
                      </div>
                    </div>

                    <div className="p-6">
                      <TabsContent value="members" className="mt-0 p-0">
                        <div className="flex justify-between items-center mb-6">
                          <h3 className="text-lg font-medium">
                            {t("groups.groupMembers")}
                          </h3>
                          <AddUserToGroupDialog
                            groupId={selectedGroup.id}
                            tenantId={tenantId}
                            trigger={
                              <Button
                                size="sm"
                                variant="outline"
                                className="gap-2"
                              >
                                <UserPlus className="h-4 w-4" />
                                {t("groups.addUser")}
                              </Button>
                            }
                          />
                        </div>

                        {selectedGroup.groupMembers.length === 0 ? (
                          <div className="flex flex-col items-center justify-center py-12 px-4">
                            <div className="bg-muted/50 p-3 rounded-full mb-4">
                              <UserPlus className="h-8 w-8 text-muted-foreground" />
                            </div>
                            <h3 className="text-lg font-medium mb-2">
                              {t("groups.noMembersYet")}
                            </h3>
                            <p className="text-muted-foreground text-center mb-6">
                              {t("groups.addMembersDescription")}
                            </p>
                            <AddUserToGroupDialog
                              groupId={selectedGroup.id}
                              tenantId={tenantId}
                              trigger={
                                <Button size="sm">
                                  <UserPlus className="mr-2 h-4 w-4" />
                                  {t("groups.addUser")}
                                </Button>
                              }
                            />
                          </div>
                        ) : (
                          <div className="border rounded-lg overflow-hidden">
                            <Table>
                              <TableHeader>
                                <TableRow className="bg-muted/50 hover:bg-muted/50">
                                  <TableHead>{t("common.user")}</TableHead>
                                  <TableHead>{t("common.email")}</TableHead>
                                  <TableHead className="w-[100px] text-right">
                                    {t("common.actions")}
                                  </TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {selectedGroup.groupMembers.map((member) => (
                                  <TableRow key={member.id}>
                                    <TableCell>
                                      <div className="flex items-center gap-3">
                                        <Avatar className="h-8 w-8">
                                          <AvatarImage
                                            src={member.user.image}
                                            alt={member.user.name}
                                          />
                                          <AvatarFallback className="bg-primary/10 text-primary">
                                            {member.user.name
                                              ?.split(" ")
                                              .map((n: string) => n[0])
                                              .join("")}
                                          </AvatarFallback>
                                        </Avatar>
                                        <span className="font-medium">
                                          {member.user.name}
                                        </span>
                                      </div>
                                    </TableCell>
                                    <TableCell className="text-muted-foreground">
                                      {member.user.email}
                                    </TableCell>
                                    <TableCell className="text-right">
                                      <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-8 w-8 text-muted-foreground hover:text-destructive"
                                          >
                                            <Trash2 className="h-4 w-4" />
                                          </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                          <AlertDialogHeader>
                                            <AlertDialogTitle>
                                              {t("groups.removeUserConfirm")}
                                            </AlertDialogTitle>
                                            <AlertDialogDescription>
                                              {t(
                                                "groups.removeUserDescription"
                                              )}
                                            </AlertDialogDescription>
                                          </AlertDialogHeader>
                                          <AlertDialogFooter>
                                            <AlertDialogCancel>
                                              {t("common.cancel")}
                                            </AlertDialogCancel>
                                            <AlertDialogAction
                                              onClick={() =>
                                                handleRemoveUser(member.user.id)
                                              }
                                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                            >
                                              {t("common.remove")}
                                            </AlertDialogAction>
                                          </AlertDialogFooter>
                                        </AlertDialogContent>
                                      </AlertDialog>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="workspaces" className="mt-0 p-0">
                        <div className="flex justify-between items-center mb-6">
                          <h3 className="text-lg font-medium">
                            {t("groups.assignedWorkspaces")}
                          </h3>
                          <AssignWorkspaceToGroupDialog
                            groupId={selectedGroup.id}
                            tenantId={tenantId}
                            trigger={
                              <Button
                                size="sm"
                                variant="outline"
                                className="gap-2"
                              >
                                <FolderPlus className="h-4 w-4" />
                                {t("groups.assignWorkspace")}
                              </Button>
                            }
                          />
                        </div>

                        {selectedGroup.groupWorkspaces.length === 0 ? (
                          <div className="flex flex-col items-center justify-center py-12 px-4">
                            <div className="bg-muted/50 p-3 rounded-full mb-4">
                              <FolderPlus className="h-8 w-8 text-muted-foreground" />
                            </div>
                            <h3 className="text-lg font-medium mb-2">
                              {t("groups.noWorkspacesYet")}
                            </h3>
                            <p className="text-muted-foreground text-center mb-6">
                              {t("groups.assignWorkspacesDescription")}
                            </p>
                            <AssignWorkspaceToGroupDialog
                              groupId={selectedGroup.id}
                              tenantId={tenantId}
                              trigger={
                                <Button size="sm">
                                  <FolderPlus className="mr-2 h-4 w-4" />
                                  {t("groups.assignWorkspace")}
                                </Button>
                              }
                            />
                          </div>
                        ) : (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {selectedGroup.groupWorkspaces.map((gw) => (
                              <Card key={gw.id} className="overflow-hidden">
                                <CardHeader className="p-4 pb-2 flex flex-row justify-between items-start">
                                  <div>
                                    <CardTitle className="text-base">
                                      {gw.workspace.name}
                                    </CardTitle>
                                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                      {gw.workspace.description ||
                                        t("groups.noDescription")}
                                    </p>
                                  </div>
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 text-muted-foreground hover:text-destructive"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>
                                          {t("groups.removeWorkspaceConfirm")}
                                        </AlertDialogTitle>
                                        <AlertDialogDescription>
                                          {t(
                                            "groups.removeWorkspaceDescription"
                                          )}
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>
                                          {t("common.cancel")}
                                        </AlertDialogCancel>
                                        <AlertDialogAction
                                          onClick={() =>
                                            handleRemoveWorkspace(
                                              gw.workspace.id
                                            )
                                          }
                                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                        >
                                          {t("common.remove")}
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                </CardHeader>
                              </Card>
                            ))}
                          </div>
                        )}
                      </TabsContent>
                    </div>
                  </Tabs>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <Card className="max-w-md mx-auto p-6 text-center border shadow-sm">
                <div className="bg-primary/10 p-3 rounded-full mx-auto mb-4 w-fit">
                  <UserPlus className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-medium mb-2">
                  {t("groups.selectGroupPrompt")}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {t("groups.selectGroupDescription")}
                </p>
              </Card>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
