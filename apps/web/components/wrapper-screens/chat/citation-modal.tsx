"use client";

import React, { useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ExternalLink, FileText } from "lucide-react";
import { useLanguage } from "@/lib/language-context";

interface Source {
  content: string;
  metadata: {
    workspace_id?: string;
    fileId?: string;
    page?: number;
    fileName?: string;
    workspace?: {
      slug?: string;
      name?: string;
    };
    [key: string]: any;
  };
}

interface CitationModalProps {
  isOpen: boolean;
  onClose: () => void;
  source: Source | null;
  highlightedText?: string;
}

export function CitationModal({
  isOpen,
  onClose,
  source,
  highlightedText,
}: CitationModalProps) {
  const { t } = useLanguage();
  const highlightRef = useRef<HTMLDivElement>(null);
  const name =
    source?.metadata?.file_name ?? source?.metadata?.source?.split("/").pop();
  const fileName = decodeURI(name?.split("-").pop() ?? "Document");
  const workspaceName = source?.metadata?.workspace?.name;
  const fileId = source?.metadata?.fileId;
  const workspaceSlug =
    source?.metadata?.workspace?.slug ?? source?.metadata?.slug;
  const pageNumber =
    source?.metadata?.page || source?.metadata?.page === 0
      ? source?.metadata?.page + 1
      : null;
  const hasFileLink = fileId && workspaceSlug;
  console.log({ source });

  // Function to find and highlight text in content
  const getHighlightedContent = () => {
    if (!source?.content) return "";

    if (!highlightedText) {
      return source.content;
    }

    // Try to find the exact text to highlight
    const textToHighlight = highlightedText.trim();

    // If the text is too long, use a shorter version for matching
    const searchText =
      textToHighlight.length > 100
        ? textToHighlight.substring(0, 100)
        : textToHighlight;

    // Try different methods to find the relevant text
    let position = -1;

    // Method 1: Direct match
    position = source.content.indexOf(searchText);

    // Method 2: Fuzzy match if direct match fails
    if (position === -1 && searchText.length > 20) {
      // Try to find a significant portion of the text (first 20 chars)
      const partialText = searchText.substring(0, 20);
      position = source.content.indexOf(partialText);
    }

    // Method 3: Check if there's a relevance score in metadata
    // const relevanceScore = source.metadata?.relevanceScore;
    // const hasRelevanceInfo = typeof relevanceScore === "number";

    // // If we still can't find a match and there's no relevance info, return the content as is
    // if (position === -1 && !hasRelevanceInfo) {
    //   console.log("No match found for citation text, showing full document");
    //   return source.content;
    // }

    // Get context around the highlighted text (about 200 chars before and after)
    const startPos = position !== -1 ? Math.max(0, position - 200) : 0;
    const endPos =
      position !== -1
        ? Math.min(source.content.length, position + searchText.length + 200)
        : Math.min(source.content.length, 500); // Show first 500 chars if no match

    // Create parts for highlighting
    let beforeHighlight: string = "";
    let highlight: string = "";
    let afterHighlight: string = "";

    if (position !== -1) {
      beforeHighlight = source.content.substring(startPos, position);
      highlight = source.content.substring(
        position,
        position + searchText.length
      );
      afterHighlight = source.content.substring(
        position + searchText.length,
        endPos
      );
    } else {
      // If we couldn't find a match but have relevance info, highlight the first paragraph
      const firstParagraphEnd = source.content.indexOf("\n\n", 0);
      const highlightEnd = firstParagraphEnd !== -1 ? firstParagraphEnd : 200;

      beforeHighlight = "";
      highlight = source.content.substring(0, highlightEnd);
      afterHighlight = source.content.substring(highlightEnd, endPos);
    }

    return (
      <>
        {startPos > 0 && <div className="text-muted-foreground">...</div>}
        <span>{beforeHighlight}</span>
        <div
          ref={highlightRef}
          className="bg-yellow-100 dark:bg-yellow-900/30 px-1 py-0.5 rounded border-l-2 border-yellow-400 dark:border-yellow-600 my-2"
        >
          {highlight}
        </div>
        <span>{afterHighlight}</span>
        {endPos < source.content.length && (
          <div className="text-muted-foreground">...</div>
        )}
      </>
    );
  };

  // Scroll to highlighted text when modal opens
  useEffect(() => {
    if (isOpen && highlightRef.current) {
      setTimeout(() => {
        highlightRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }, 100);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl w-[90vw] max-h-[85vh] flex flex-col">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <DialogTitle
                className="text-lg font-semibold truncate"
                title={fileName}
              >
                {fileName}
              </DialogTitle>

              {/* Relevance indicator */}
              {source?.metadata?.relevanceScore && (
                <div
                  className="flex items-center gap-0.5 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-md"
                  title={`${t("chat.relevance")}: ${Math.round(
                    (source.metadata.relevanceScore || 0) * 100
                  )}%`}
                >
                  <div className="text-xs text-gray-600 dark:text-gray-300 mr-1">
                    {Math.round((source.metadata.relevanceScore || 0) * 100)}%
                  </div>
                  <div className="flex items-center gap-0.5">
                    {[...Array(5)].map((_, i) => {
                      const score = source.metadata.relevanceScore || 0;
                      const filled = i < Math.round(score * 5);
                      return (
                        <div
                          key={i}
                          className={`h-2 w-2 rounded-full ${
                            filled
                              ? "bg-primary"
                              : "bg-gray-200 dark:bg-gray-700"
                          }`}
                        />
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
            {workspaceName && (
              <p className="text-sm text-muted-foreground truncate">
                {workspaceName}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {hasFileLink && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={() => {
                  window.open(
                    `/workspace/${workspaceSlug}/file/${fileId}`,
                    "_blank"
                  );
                }}
              >
                <span>{t("chat.openDocument")}</span>
                <ExternalLink className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        </DialogHeader>

        <div className="mt-4 flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900/50 rounded-md text-sm relative">
          <div className="sticky top-0 z-10 bg-gray-100 dark:bg-gray-800 p-2 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-primary/70" />
              <span className="font-medium">{fileName}</span>

              {/* Relevance percentage in sticky header */}
              {source?.metadata?.relevanceScore && (
                <div className="text-xs text-primary bg-primary/10 px-1.5 py-0.5 rounded-full">
                  {Math.round((source.metadata.relevanceScore || 0) * 100)}%
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              {pageNumber !== null && (
                <div className="px-2 py-0.5 bg-primary/10 text-primary text-xs rounded-full font-medium">
                  {t("chat.page")} {pageNumber}
                </div>
              )}
            </div>
          </div>
          <div className="p-4">{getHighlightedContent()}</div>

          {/* Floating action button to open document */}
          {hasFileLink && (
            <div className="sticky bottom-4 float-right mr-4">
              <Button
                size="sm"
                className="rounded-full shadow-md flex items-center gap-1.5 px-3 bg-primary/90 hover:bg-primary"
                onClick={() => {
                  window.open(
                    `/workspace/${workspaceSlug}/file/${fileId}`,
                    "_blank"
                  );
                }}
              >
                <span>{t("chat.openDocument")}</span>
                <ExternalLink className="h-3.5 w-3.5" />
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
