import { fetchJson } from "..";

export interface SearchResult {
  id: string;
  type: "file" | "page" | "folder" | "workspace" | "chat" | "message";
  title: string;
  content?: string;
  snippet?: string;
  workspace_id?: string;
  slug?: string;
  workspace_name?: string;
  created_at?: string;
  updated_at?: string;
  score?: number;
  metadata?: Record<string, any>;
}

export interface GlobalSearchResponse {
  results: SearchResult[];
  total_count: number;
  categories: Record<string, number>;
  has_more: boolean;
  search_time_ms: number;
  error?: string;
}

export interface SearchFilters {
  types?: string[];
  workspace_ids?: string[];
  date_from?: string;
  date_to?: string;
}

export interface GlobalSearchParams {
  query: string;
  tenantId: string;
  types?: string[];
  workspaceIds?: string[];
  limit?: number;
  skip?: number;
}

export const performGlobalSearch = async (
  params: GlobalSearchParams
): Promise<GlobalSearchResponse> => {
  try {
    const searchParams = new URLSearchParams({
      query: params.query,
      tenantId: params.tenantId,
      limit: (params.limit || 20).toString(),
      skip: (params.skip || 0).toString(),
    });

    if (params.types && params.types.length > 0) {
      searchParams.append("types", params.types.join(","));
    }

    if (params.workspaceIds && params.workspaceIds.length > 0) {
      searchParams.append("workspaceIds", params.workspaceIds.join(","));
    }

    const response = await fetchJson(
      `/api/global-search?${searchParams.toString()}`
    );

    return response;
  } catch (error: any) {
    console.error("Error in global search service:", error);

    return {
      results: [],
      total_count: 0,
      categories: {},
      has_more: false,
      search_time_ms: 0,
      error: error.message || "Failed to perform search",
    };
  }
};

export const getSearchTypeDisplayName = (type: string): string => {
  const typeMap: Record<string, string> = {
    file: "Files",
    page: "Pages",
    folder: "Folders",
    workspace: "Workspaces",
    chat: "Chats",
    message: "Messages",
  };
  return typeMap[type] || type;
};

export const getSearchTypeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    file: "📄",
    page: "📝",
    folder: "📁",
    workspace: "🏢",
    chat: "💬",
    message: "📨",
  };
  return iconMap[type] || "📄";
};

export const formatSearchResultDate = (dateString?: string): string => {
  if (!dateString) return "";

  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return "Today";
  } else if (diffInDays === 1) {
    return "Yesterday";
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks} week${weeks > 1 ? "s" : ""} ago`;
  } else if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return `${months} month${months > 1 ? "s" : ""} ago`;
  } else {
    const years = Math.floor(diffInDays / 365);
    return `${years} year${years > 1 ? "s" : ""} ago`;
  }
};

export const highlightSearchTerm = (
  text: string,
  searchTerm: string
): string => {
  if (!searchTerm || !text) return text;

  const regex = new RegExp(`(${searchTerm})`, "gi");
  return text.replace(regex, "<mark>$1</mark>");
};

export const truncateText = (text: string, maxLength: number = 150): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};
