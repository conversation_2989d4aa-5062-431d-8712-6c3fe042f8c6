import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

// Update comment (edit content or resolve/unresolve)
export async function PUT(
  req: Request,
  { params }: { params: { commentId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { content, status } = await req.json();

    // Find the comment
    const comment = await db.comment.findFirst({
      where: {
        id: params.commentId,
      },
      include: {
        author: true,
        message: {
          include: {
            chat: true,
          },
        },
      },
    });

    if (!comment) {
      return new NextResponse("Comment not found", { status: 404 });
    }

    // Check permissions
    const isAuthor = comment.authorId === session.userId;
    const isAdmin = session.role === "ADMIN" || session.role === "OWNER";

    if (!isAuthor && !isAdmin) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Prepare update data
    const updateData: any = {};

    if (content !== undefined) {
      if (!isAuthor) {
        return new NextResponse("Only the author can edit comment content", {
          status: 403,
        });
      }
      updateData.content = content.trim();
    }

    if (status !== undefined) {
      if (!["ACTIVE", "RESOLVED"].includes(status)) {
        return new NextResponse("Invalid status", { status: 400 });
      }
      updateData.status = status;
    }

    // Update the comment
    const updatedComment = await db.comment.update({
      where: { id: params.commentId },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        replies: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    return NextResponse.json({ comment: updatedComment });
  } catch (error) {
    console.error("[COMMENT_PUT]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

// Delete comment
export async function DELETE(
  req: Request,
  { params }: { params: { commentId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Find the comment
    const comment = await db.comment.findFirst({
      where: {
        id: params.commentId,
      },
      include: {
        author: true,
        message: {
          include: {
            chat: true,
          },
        },
      },
    });

    if (!comment) {
      return new NextResponse("Comment not found", { status: 404 });
    }

    // Check permissions
    const isAuthor = comment.authorId === session.userId;
    const isAdmin = session.role === "ADMIN" || session.role === "OWNER";

    if (!isAuthor && !isAdmin) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Soft delete by setting status to DELETED
    await db.comment.update({
      where: { id: params.commentId },
      data: { status: "DELETED" },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[COMMENT_DELETE]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
