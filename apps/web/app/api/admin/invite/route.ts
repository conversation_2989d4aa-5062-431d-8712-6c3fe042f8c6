import { NextResponse, NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import crypto from "crypto";
import Mailer from "@/lib/email/send-email";
import { getInviteEmailTemplate } from "@/lib/email/templates/invite-template";
import {
  checkActiveSubscription,
  checkUserLimit,
} from "@/lib/subscription-check";

export async function POST(request: NextRequest) {
  const token = await getToken({ req: request });
  if (!token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const input = await request.json();
  const { email, role, tenantId, name, customRoleId } = input;

  if (!email || !role || !tenantId) {
    return NextResponse.json(
      { error: "Email, role, and tenantId are required" },
      { status: 400 }
    );
  }

  // If role is CUSTOM, customRoleId is required
  if (role === "CUSTOM" && !customRoleId) {
    return NextResponse.json(
      { error: "Custom role ID is required when role is CUSTOM" },
      { status: 400 }
    );
  }

  try {
    // Verify the user has admin access to this tenant
    const adminMembership = await db.membership.findFirst({
      where: {
        userId: token?.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
      include: {
        tenant: true,
        user: true,
      },
    });

    if (!adminMembership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Check if tenant has an active subscription
    const { hasActiveSubscription } = await checkActiveSubscription(tenantId);

    if (!hasActiveSubscription) {
      return NextResponse.json(
        { error: "You need an active subscription to invite members" },
        { status: 403 }
      );
    }

    // Check if tenant has reached its user limit
    const { hasReachedLimit, currentCount, limit } =
      await checkUserLimit(tenantId);

    if (hasReachedLimit) {
      return NextResponse.json(
        {
          error: "subscription.userLimitReached",
          message: "subscription.userLimitMessage",
          data: { currentCount, limit },
        },
        { status: 403 }
      );
    }

    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    // Check if user with given email exists
    const existingUser = await db.user.findFirst({
      where: { emailHash },
    });

    // Check if an active invitation already exists for this email and tenant
    const existingInvitation = await db.invitation.findFirst({
      where: {
        emailHash,
        tenantId,
        accepted: false,
        expires: { gt: new Date() },
      },
    });

    if (existingInvitation) {
      return NextResponse.json(
        { error: "An active invitation already exists for this email" },
        { status: 400 }
      );
    }

    // Check if user already has membership in this tenant
    if (existingUser?.emailHash === emailHash) {
      const existingMembership = await db.membership.findFirst({
        where: {
          userId: existingUser.id,
          tenantId,
        },
      });

      if (existingMembership) {
        return NextResponse.json(
          { error: "User is already a member of this organization" },
          { status: 400 }
        );
      }
    }

    // Generate invitation token
    const inviteToken = crypto.randomBytes(32).toString("hex");

    // Set expiration time (48 hours from now)
    const expires = new Date();
    expires.setHours(expires.getHours() + 48);
    // Verify custom role exists and belongs to this tenant if role is CUSTOM
    if (role === "CUSTOM" && customRoleId) {
      const customRole = await db.customRole.findFirst({
        where: {
          id: customRoleId,
          tenantId,
        },
      });

      if (!customRole) {
        return NextResponse.json(
          { error: "Custom role not found or does not belong to this tenant" },
          { status: 400 }
        );
      }
    }

    // Create invitation record
    const invitation = await db.invitation.create({
      data: {
        email,
        emailHash,
        role: role,
        tenantId,
        inviterId: adminMembership.userId,
        token: inviteToken,
        expires,
        ...(role === "CUSTOM" && customRoleId ? { customRoleId } : {}),
      },
      include: {
        tenant: true,
        inviter: true,
        customRole: role === "CUSTOM",
      },
    });

    // Send invitation email
    const inviterName = adminMembership.user?.name || "An administrator";
    const tenantName = adminMembership.tenant?.name || "an organization";
    const inviteUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/invitations/${inviteToken}`;

    // Get custom role name if applicable
    let displayRole = role;
    if (role === "CUSTOM" && invitation.customRole) {
      displayRole = `Custom Role: ${invitation.customRole.name}`;
    }

    const html = getInviteEmailTemplate({
      tenantName,
      inviterName,
      role: displayRole,
      tempPassword: "", // Not needed for new flow
      loginUrl: inviteUrl,
    });

    try {
      await Mailer.send({
        from: process.env.NEXT_PUBLIC_SEND_EMAIL_FROM || "",
        to: email,
        subject: `You've been invited to join ${tenantName} on Swiss Knowledge Hub`,
        html,
      });
    } catch (emailError) {
      console.error("Failed to send invite email:", emailError);
      // Continue even if email fails, the user can still use the link
    }

    return NextResponse.json(
      {
        data: invitation,
        message: "Invitation sent successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Invite user error:", error);
    return NextResponse.json(
      { error: "Internal Server Error: Failed to invite user" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const tenantId = url.searchParams.get("tenantId");
  const userId = url.searchParams.get("userId");

  if (!tenantId) {
    return NextResponse.json(
      { error: "tenantId is required" },
      { status: 400 }
    );
  }

  try {
    // Verify the user has admin access to this tenant
    const adminMembership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!adminMembership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Fetch all members for this tenant
    const members = await db.membership.findMany({
      where: { tenantId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({ data: members }, { status: 200 });
  } catch (error) {
    console.error("Get members error:", error);
    return NextResponse.json(
      { error: "Internal Server Error: Failed to get members" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const invitationId = url.searchParams.get("id");
    const tenantId = url.searchParams.get("tenantId");

    if (!invitationId || !tenantId) {
      return NextResponse.json(
        { error: "Invitation ID and tenantId are required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Find and delete the invitation
    const invitation = await db.invitation.findFirst({
      where: {
        id: invitationId,
        tenantId,
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "Invitation not found" },
        { status: 404 }
      );
    }

    await db.invitation.delete({
      where: { id: invitationId },
    });

    return NextResponse.json(
      {
        message: "Invitation cancelled successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Cancel invitation error:", error);
    return NextResponse.json(
      { error: "Failed to cancel invitation" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const invitationId = url.searchParams.get("id");
    const tenantId = url.searchParams.get("tenantId");

    if (!invitationId || !tenantId) {
      return NextResponse.json(
        { error: "Invitation ID and tenantId are required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
      include: {
        tenant: true,
        user: true,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Find the invitation
    const invitation = await db.invitation.findFirst({
      where: {
        id: invitationId,
        tenantId,
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "Invitation not found" },
        { status: 404 }
      );
    }

    // Update the invitation with a new expiration date
    const updatedInvitation = await db.invitation.update({
      where: { id: invitationId },
      data: {
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      },
    });
    // Send invitation email
    const inviterName = membership.user?.name || "An administrator";
    const tenantName = membership.tenant?.name || "an organization";
    const inviteUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/invitations/${invitation?.token}`;

    const html = getInviteEmailTemplate({
      tenantName,
      inviterName,
      role: invitation?.role,
      tempPassword: "", // Not needed for new flow
      loginUrl: inviteUrl,
    });

    try {
      await Mailer.send({
        from: process.env.NEXT_PUBLIC_SEND_EMAIL_FROM || "",
        to: invitation?.email,
        subject: `You've been invited to join ${tenantName} on Swiss Knowledge Hub`,
        html,
      });
    } catch (emailError) {
      console.error("Failed to send invite email:", emailError);
      // Continue even if email fails, the user can still use the link
    }
    return NextResponse.json({
      data: updatedInvitation,
      message: "Invitation resent successfully",
    });
  } catch (error) {
    console.error("Resend invitation error:", error);
    return NextResponse.json(
      { error: "Failed to resend invitation" },
      { status: 500 }
    );
  }
}
