"use client";

import { Button } from "@/components/ui/button";
import { TenantTable } from "@/components/tenants/tenant-table";
import { Plus } from "lucide-react";
import { useEffect, useState } from "react";

export default function TenantsPage() {
  const [tenants, setTenants] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  console.log({ tenants });

  useEffect(() => {
    async function fetchTenants() {
      try {
        const response = await fetch("/api/tenants");
        if (!response.ok) {
          throw new Error("Failed to fetch tenants");
        }
        const data = await response.json();
        setTenants(data);
      } catch (err) {
        setError("Error loading tenants");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchTenants();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Tenants</h1>
          <p className="text-muted-foreground">
            Manage all tenants in the system
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Tenant
        </Button>
      </div>

      {loading ? (
        <div className="flex h-64 items-center justify-center">
          <p className="text-lg">Loading tenants...</p>
        </div>
      ) : error ? (
        <div className="flex h-64 items-center justify-center">
          <p className="text-lg text-red-500">{error}</p>
        </div>
      ) : (
        <TenantTable tenants={tenants} />
      )}
    </div>
  );
}
