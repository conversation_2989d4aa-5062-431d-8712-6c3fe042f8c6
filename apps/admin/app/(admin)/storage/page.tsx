"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { UsageChart } from "@/components/dashboard/usage-chart";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";

// Mock data - will be replaced with real API calls
const mockMonthlyStorageUsage = [
  { name: "Jan", value: 100 },
  { name: "Feb", value: 150 },
  { name: "Mar", value: 200 },
  { name: "Apr", value: 250 },
  { name: "May", value: 300 },
  { name: "<PERSON>", value: 350 },
  { name: "Jul", value: 400 },
];

const mockStorageDistribution = [
  { name: "Documents", value: 45 },
  { name: "Images", value: 25 },
  { name: "Vectors", value: 20 },
  { name: "Other", value: 10 },
];

const mockTenantStorageUsage = [
  { name: "Acme Corp", allocated: 200, used: 120 },
  { name: "Startup Inc", allocated: 100, used: 45 },
  { name: "Tech Solutions", allocated: 150, used: 78 },
  { name: "Global Services", allocated: 50, used: 12 },
  { name: "Digital Innovations", allocated: 120, used: 65 },
];

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

export default function StoragePage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Storage Usage</h1>
        <p className="text-muted-foreground">
          Monitor storage usage across all tenants
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <UsageChart
          title="Monthly Storage Growth (GB)"
          data={mockMonthlyStorageUsage}
          dataKey="value"
          valueFormatter={(value) => `${value} GB`}
        />

        <Card>
          <CardHeader>
            <CardTitle>Storage Type Distribution</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={mockStorageDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockStorageDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value}%`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Storage Usage by Tenant</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={mockTenantStorageUsage}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value) => `${value} GB`} />
              <Legend />
              <Bar dataKey="allocated" name="Allocated Storage (GB)" fill="#8884d8" />
              <Bar dataKey="used" name="Used Storage (GB)" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Storage Utilization</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left font-medium">Tenant</th>
                  <th className="px-4 py-3 text-left font-medium">Allocated (GB)</th>
                  <th className="px-4 py-3 text-left font-medium">Used (GB)</th>
                  <th className="px-4 py-3 text-left font-medium">Utilization (%)</th>
                  <th className="px-4 py-3 text-left font-medium">Status</th>
                </tr>
              </thead>
              <tbody>
                {mockTenantStorageUsage.map((tenant, index) => {
                  const utilization = (tenant.used / tenant.allocated) * 100;
                  let status = "Good";
                  let statusClass = "text-green-600";
                  
                  if (utilization > 90) {
                    status = "Critical";
                    statusClass = "text-red-600";
                  } else if (utilization > 75) {
                    status = "Warning";
                    statusClass = "text-yellow-600";
                  }
                  
                  return (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-3">{tenant.name}</td>
                      <td className="px-4 py-3">{tenant.allocated}</td>
                      <td className="px-4 py-3">{tenant.used}</td>
                      <td className="px-4 py-3">{utilization.toFixed(1)}%</td>
                      <td className={`px-4 py-3 ${statusClass}`}>{status}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
