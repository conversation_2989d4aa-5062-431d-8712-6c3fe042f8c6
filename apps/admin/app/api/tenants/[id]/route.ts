import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      {
        error: "Unauthorized",
      },
      { status: 401 }
    );
  }

  const { id } = params;
  const tenant = await db.tenant.findUnique({
    where: {
      id,
    },
  });

  if (!tenant) {
    return NextResponse.json(
      {
        error: "Tenant not found",
      },
      { status: 404 }
    );
  }

  // In a real implementation, you would query the database here
  return NextResponse.json(tenant);
}
