import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import { StorageTierSchema, StorageTierData } from "./seed-subscription-schema";

// Extend PrismaClient type to include our models
interface CustomPrismaClient extends PrismaClient {
  storageTier: any;
}

// Cast PrismaClient to our custom interface
const prisma = new PrismaClient() as unknown as CustomPrismaClient;

async function main() {
  console.log("Seeding storage tiers...");

  console.log(
    "Note: This script assumes the StorageTier model exists in your Prisma schema."
  );
  console.log(
    "If you encounter errors, make sure your database schema is up to date."
  );

  try {
    // Delete existing storage tiers
    await prisma.storageTier.deleteMany({});
    console.log("Deleted existing storage tiers");
  } catch (error) {
    console.log("Error deleting storage tiers:", error);
  }

  // Create storage tiers based on the pricing table
  const storageTiersData: StorageTierData[] = [
    {
      name: "10GB",
      sizeGB: 10,
      price: 29, // CHF 29 per month
      stripePriceId: "price_1RIrcGRRDK7OUsBTyJ7QcAJz", // Monthly price ID
      stripeYearlyPriceId: "price_1RIrcHRRDK7OUsBTMvtyhDXi", // Yearly price ID
      isActive: true,
    },
    {
      name: "50GB",
      sizeGB: 50,
      price: 79, // CHF 79 per month
      stripePriceId: "price_1RIrcHRRDK7OUsBTVSEywt9d", // Monthly price ID
      stripeYearlyPriceId: "price_1RIrcIRRDK7OUsBTLH1wOzwj", // Yearly price ID
      isActive: true,
    },
    {
      name: "100GB",
      sizeGB: 100,
      price: 149, // CHF 149 per month
      stripePriceId: "price_1RIrcIRRDK7OUsBT4XRY1hdc", // Monthly price ID
      stripeYearlyPriceId: "price_1RIrcJRRDK7OUsBTA4BYMlGe", // Yearly price ID
      isActive: true,
    },
  ];

  // Validate all storage tiers against the schema
  for (const tierData of storageTiersData) {
    try {
      // Validate the storage tier data
      const validatedTier = StorageTierSchema.parse(tierData);

      // Create the storage tier in the database
      await prisma.storageTier.create({
        data: validatedTier,
      });

      console.log(`✅ Created storage tier: ${validatedTier.name}`);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error(
          `❌ Validation error for storage tier ${tierData.name}:`,
          error.errors
        );
      } else {
        console.error(
          `❌ Error creating storage tier ${tierData.name}:`,
          error
        );
      }
    }
  }

  console.log("Storage tiers seeded successfully!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
