"use client";

import { useLanguage } from "@/lib/language-context";
import { Button } from "@/components/ui/button";
import { Globe, LogIn, LayoutDashboard } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Check } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useTheme } from "next-themes";
import { useSession } from "next-auth/react";

// Session button component that shows Sign In or Dashboard based on session status
function SessionButton() {
  const { data: session, status } = useSession();
  const { t } = useLanguage();

  if (status === "loading") {
    return (
      <Button variant="outline" size="sm" disabled>
        <span className="h-4 w-4 animate-spin mr-2" />
        Loading...
      </Button>
    );
  }

  if (session) {
    return (
      <Button variant="outline" size="sm" asChild>
        <Link href="/dashboard">
          <LayoutDashboard className="h-4 w-4 mr-2" />
          {t ? t("common.dashboard") : "Dashboard"}
        </Link>
      </Button>
    );
  }

  return (
    <Button variant="outline" size="sm" asChild>
      <Link href="/sign-in">
        <LogIn className="h-4 w-4 mr-2" />
        {t ? t("common.signIn") : "Sign In"}
      </Link>
    </Button>
  );
}

export function PrivacyPolicyHeader() {
  const { language, setLanguage } = useLanguage();
  const { resolvedTheme } = useTheme();

  const handleLanguageChange = (newLanguage: "en" | "de") => {
    setLanguage(newLanguage);

    // Force reload to apply language change to all components
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  const getLanguageName = (code: string) => {
    switch (code) {
      case "en":
        return "English";
      case "de":
        return "Deutsch";
      default:
        return code.toUpperCase();
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/" className="flex items-center gap-2">
            <Image
              src={`/logo-${resolvedTheme}.png`}
              alt="Swiss Knowledge Hub"
              width={24}
              height={24}
              className="h-6 w-6"
            />
            <span className="font-semibold">Swiss Knowledge Hub</span>
          </Link>
        </div>
        <div className="flex items-center gap-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="gap-1.5">
                <Globe className="h-4 w-4" />
                <span>{getLanguageName(language)}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => handleLanguageChange("en")}
                className="flex items-center justify-between"
              >
                English
                {language === "en" && <Check className="h-4 w-4 ml-2" />}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleLanguageChange("de")}
                className="flex items-center justify-between"
              >
                Deutsch
                {language === "de" && <Check className="h-4 w-4 ml-2" />}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <SessionButton />
        </div>
      </div>
    </header>
  );
}
