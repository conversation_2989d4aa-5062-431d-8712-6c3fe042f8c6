import ChatHistory from "@/components/wrapper-screens/chat-history";
import { getChat } from "@/services";
import { cookies } from "next/headers";

export default async function Page() {
  const userId = cookies().get("userId")?.value ?? "";
  const tenantId = cookies().get("currentOrganizationId")?.value ?? "";

  try {
    // Handle case where folder data is not found
    const chatHistory = (await getChat({ userId, tenantId })) ?? [];
    return <ChatHistory chatHistory={chatHistory?.chat} />;
  } catch (error) {
    console.error("Error loading folder:", error);
  }
}
