"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatCurrency, formatNumber } from "@/lib/utils";
import { useEffect, useState } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";

export default function FinanceMetricsPage() {
  // State for financial metrics
  const [revenueMetrics, setRevenueMetrics] = useState({
    mrr: 0,
    arr: 0,
    activeSubscriptions: 0,
  });

  // State for revenue by plan
  const [revenueByPlan, setRevenueByPlan] = useState([]);

  // State for revenue over time
  const [revenueOverTime, setRevenueOverTime] = useState([]);

  // State for loading
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchFinanceMetrics() {
      try {
        // Fetch revenue metrics
        const revenueResponse = await fetch("/api/metrics/finance/revenue");
        if (revenueResponse.ok) {
          const revenueData = await revenueResponse.json();
          setRevenueMetrics(revenueData);
          setRevenueByPlan(revenueData.revenueByPlan || []);
        }

        // Fetch revenue over time
        const timeResponse = await fetch(
          "/api/metrics/finance/revenue/history?period=6months"
        );
        if (timeResponse.ok) {
          const timeData = await timeResponse.json();
          setRevenueOverTime(timeData.monthlyData || []);
        }
      } catch (error) {
        console.error("Error fetching finance metrics:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchFinanceMetrics();
  }, []);

  // Colors for the pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  // Fallback data for charts if API returns empty
  const fallbackRevenueData = [
    { name: "Jan", value: 0 },
    { name: "Feb", value: 0 },
    { name: "Mar", value: 0 },
    { name: "Apr", value: 0 },
    { name: "May", value: 0 },
    { name: "Jun", value: 0 },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Recurring Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : formatCurrency(revenueMetrics.mrr)}
            </div>
            <p className="text-xs text-muted-foreground">
              From {revenueMetrics.activeSubscriptions || 0} active
              subscriptions
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Annual Recurring Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : formatCurrency(revenueMetrics.arr)}
            </div>
            <p className="text-xs text-muted-foreground">
              Projected annual revenue
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Average Revenue Per User
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading
                ? "Loading..."
                : formatCurrency(
                    revenueMetrics.mrr /
                      (revenueMetrics.activeSubscriptions || 1)
                  )}
            </div>
            <p className="text-xs text-muted-foreground">
              Monthly average per subscription
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Over Time</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={
                    revenueOverTime.length > 0
                      ? revenueOverTime
                      : fallbackRevenueData
                  }
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [formatCurrency(value), "Revenue"]}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                    name="Monthly Revenue"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Revenue by Plan</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={revenueByPlan}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="revenue"
                  >
                    {revenueByPlan.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [formatCurrency(value), "Revenue"]}
                  />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
