import { create } from 'zustand';

export const useSocialAccountStore = create((set) => ({
  disconnectData: {
    showModal: false,
    id: '',
    platform: '',
  },
  pageConnectData: {
    showModal: false,
    platform: '',
    pages: [],
  },
  disconnect(showModal, item) {
    set((prev) => ({
      disconnectData: {
        showModal: showModal,
        id: item?.id,
        platform: item?.platform,
      },
    }));
  },
  pageConnect(showModal, item) {
    set((prev) => ({
      pageConnectData: {
        showModal: showModal,
        pages: item?.pages,
        platform: item?.platform,
      },
    }));
  },
}));
