import { z } from "zod";

// Define the PlanType enum to match the one in the Prisma schema
export enum PlanType {
  STARTER = "STARTER",
  BUSINESS = "BUSINESS",
  CUSTOM = "CUSTOM",
  ENTERPRISE = "ENTERPRISE",
}

// Schema for Plan data
export const PlanSchema = z.object({
  id: z.string().optional(), // Optional for creation
  name: z.string().min(1, "Plan name is required"),
  type: z.nativeEnum(PlanType, {
    errorMap: () => ({ message: "Invalid plan type" }),
  }),
  description: z.string().optional(),
  stripePriceId: z.string().optional(), // Monthly price ID for the base plan
  stripeYearlyPriceId: z.string().optional(), // Yearly price ID for the base plan
  includedUsers: z.number().int().positive("Must include at least 1 user"),
  additionalUserFee: z
    .number()
    .nonnegative("Additional user fee cannot be negative"),
  stripeUserPriceId: z.string().optional(), // Monthly price ID for additional users
  stripeUserYearlyPriceId: z.string().optional(), // Yearly price ID for additional users
  vectorStoreGB: z
    .number()
    .int()
    .positive("Vector store size must be positive"),
  price: z.number().nullable().optional(), // Base price of the plan (for display purposes only)
  isActive: z.boolean().default(true),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// Schema for Subscription data
export const SubscriptionSchema = z.object({
  id: z.string().optional(), // Optional for creation
  tenantId: z.string().min(1, "Tenant ID is required"),
  planId: z.string().min(1, "Plan ID is required"),
  startDate: z.date().default(() => new Date()),
  endDate: z.date().optional(),
  isActive: z.boolean().default(true),
  additionalUsers: z.number().int().nonnegative().default(0),
  additionalStorageGB: z.number().int().nonnegative().default(0),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// Schema for StorageTier data
export const StorageTierSchema = z.object({
  id: z.string().optional(), // Optional for creation
  name: z.string().min(1, "Storage tier name is required"),
  sizeGB: z.number().int().positive("Size must be positive"),
  price: z.number().positive("Price must be positive"),
  stripePriceId: z.string().min(1, "Stripe price ID is required"),
  stripeYearlyPriceId: z.string().min(1, "Stripe yearly price ID is required"),
  isActive: z.boolean().default(true),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// Schema for VectorStoreUsage data
export const VectorStoreUsageSchema = z.object({
  id: z.string().optional(), // Optional for creation
  tenantId: z.string().min(1, "Tenant ID is required"),
  usageGB: z.number().positive("Usage must be positive"),
  timestamp: z.date().default(() => new Date()),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

// Types based on the schemas
export type PlanData = z.infer<typeof PlanSchema>;
export type SubscriptionData = z.infer<typeof SubscriptionSchema>;
export type StorageTierData = z.infer<typeof StorageTierSchema>;
export type VectorStoreUsageData = z.infer<typeof VectorStoreUsageSchema>;

// Schema for plan upgrade request
export const PlanUpgradeRequestSchema = z.object({
  tenantId: z.string().min(1, "Tenant ID is required"),
  planId: z.string().min(1, "Plan ID is required"),
  additionalUsers: z.number().int().nonnegative().optional(),
  additionalStorageGB: z.number().int().nonnegative().optional(),
});

// Schema for vector store usage request
export const VectorStoreUsageRequestSchema = z.object({
  tenantId: z.string().min(1, "Tenant ID is required"),
  usageGB: z.number().positive("Usage must be positive"),
});

// Type for plan upgrade request
export type PlanUpgradeRequest = z.infer<typeof PlanUpgradeRequestSchema>;
export type VectorStoreUsageRequest = z.infer<
  typeof VectorStoreUsageRequestSchema
>;
