"use client";

import React, { useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { FileText, ExternalLink, Search, Globe } from "lucide-react";
import { Message, Source } from "../types";
import { useLanguage } from "@/lib/language-context";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

interface MessageCitationsProps {
  displayedMessage: Message;
  index: number;
  toggleCitationAccordion?: (messageId: string) => void; // Made optional
  setSelectedSource: (source: Source) => void;
  setIsCitationModalOpen: (isOpen: boolean) => void;
}

export const MessageCitations: React.FC<MessageCitationsProps> = ({
  displayedMessage,
  index,
  toggleCitationAccordion,
  setSelectedSource,
  setIsCitationModalOpen,
}) => {
  const { t } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
    // Also call the parent toggle function if provided
    if (toggleCitationAccordion) {
      toggleCitationAccordion(displayedMessage.id || `msg-${index}`);
    }
  };

  if (!displayedMessage.sources || displayedMessage.sources.length === 0) {
    return null;
  }

  // Separate document and web sources
  const documentSources = displayedMessage.sources.filter(
    (source) =>
      source.metadata?.source !== "web" &&
      source.metadata?.type !== "web_search"
  );

  const webSources = displayedMessage.sources.filter(
    (source) =>
      source.metadata?.source === "web" ||
      source.metadata?.type === "web_search"
  );

  const hasDocumentSources = documentSources.length > 0;
  const hasWebSources = webSources.length > 0;

  return (
    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 animate-fadeIn">
      <Accordion type="single" collapsible className="w-full border-none">
        <AccordionItem value="citations" className="border-none">
          <AccordionTrigger
            className="py-1.5 px-2 hover:no-underline hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-md group transition-colors"
            onClick={handleToggle}
          >
            <div className="flex justify-between items-center gap-2 w-full">
              <div className="flex items-center gap-1">
                <div className="bg-primary/10 dark:bg-primary/20 p-1 rounded-full">
                  <FileText className="h-3.5 w-3.5 text-primary" />
                </div>
                <div className="text-xs font-medium text-primary ">
                  {t("chat.sources")} ({displayedMessage.sources?.length || 0})
                </div>
              </div>
              <div className="text-[10px] text-muted-foreground mr-1 opacity-70 group-hover:opacity-100">
                {t("chat.citationsAccordion")}
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-2 pb-1 animate-in fade-in-50 duration-300">
            {/* Tabs for document and web sources */}
            {hasDocumentSources && hasWebSources ? (
              <Tabs defaultValue="documents" className="w-full">
                <TabsList className="mb-2">
                  <TabsTrigger value="documents" className="text-xs">
                    <FileText className="h-3.5 w-3.5 mr-1" />
                    {t("chat.documentSources")} ({documentSources.length})
                  </TabsTrigger>
                  <TabsTrigger value="web" className="text-xs">
                    <Globe className="h-3.5 w-3.5 mr-1" />
                    {t("chat.webSources")} ({webSources.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="documents" className="mt-0">
                  <SourceGrid
                    sources={documentSources}
                    setSelectedSource={setSelectedSource}
                    setIsCitationModalOpen={setIsCitationModalOpen}
                    t={t}
                  />
                </TabsContent>

                <TabsContent value="web" className="mt-0">
                  <WebSourceGrid sources={webSources} t={t} />
                </TabsContent>
              </Tabs>
            ) : hasDocumentSources ? (
              <SourceGrid
                sources={documentSources}
                setSelectedSource={setSelectedSource}
                setIsCitationModalOpen={setIsCitationModalOpen}
                t={t}
              />
            ) : (
              <WebSourceGrid sources={webSources} t={t} />
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

// Component for document sources grid
const SourceGrid = ({
  sources,
  setSelectedSource,
  setIsCitationModalOpen,
  t,
}: {
  sources: Source[];
  setSelectedSource: (source: Source) => void;
  setIsCitationModalOpen: (isOpen: boolean) => void;
  t: (translationKey: string, options?: any) => string;
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
      {sources.map((source, idx) => {
        // Get file name from metadata if available
        const name =
          source?.metadata?.file_name ??
          source?.metadata?.source?.split("/").pop();
        const fileName = decodeURI(name?.split("-").pop() ?? "Document");
        const fileId = source.metadata?.fileId;
        const workspaceId =
          source?.metadata?.workspace?.slug ?? source?.metadata?.slug;
        const workspaceName = source.metadata?.workspace?.name;
        const pageNumber =
          source?.metadata?.page || source?.metadata?.page === 0
            ? source?.metadata?.page + 1
            : null;
        const hasFileLink = fileId && workspaceId;

        return (
          <div
            key={idx}
            className={`
              group relative p-2.5 rounded-lg border border-gray-200 dark:border-gray-700
              bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800
              transition-all duration-200 cursor-pointer
              ${hasFileLink ? "hover:shadow-md" : ""}
            `}
            onClick={() => {
              // Open the citation modal with the selected source
              setSelectedSource(source);
              setIsCitationModalOpen(true);
            }}
          >
            <div className="flex items-start gap-2">
              <div className="mt-0.5 flex-shrink-0">
                <FileText className="h-4 w-4 text-primary/70" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div
                    className="text-xs font-medium truncate flex items-center gap-1"
                    title={fileName}
                  >
                    {fileName}
                    <Search className="h-3 w-3 text-primary/70 inline-block ml-1" />
                  </div>

                  {/* Relevance indicator - improved to show more granular relevance */}
                  {source.metadata?.relevanceScore && (
                    <div
                      className="flex items-center gap-0.5"
                      title={`${t("chat.relevance")}: ${Math.round(
                        (source.metadata.relevanceScore || 0) * 100
                      )}%`}
                    >
                      {[...Array(5)].map((_, i) => {
                        const score = source.metadata.relevanceScore || 0;
                        // More granular display - show half-filled dots for better precision
                        const value = score * 5 - i;
                        let fillClass = "bg-gray-200 dark:bg-gray-700"; // Empty

                        if (value >= 1) {
                          fillClass = "bg-primary"; // Filled
                        } else if (value >= 0.4) {
                          fillClass = "bg-primary/60"; // Half-filled
                        }

                        return (
                          <div
                            key={i}
                            className={`h-1.5 w-1.5 rounded-full ${fillClass}`}
                          />
                        );
                      })}
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  {workspaceName && (
                    <div className="text-[10px] text-muted-foreground truncate">
                      {workspaceName}
                    </div>
                  )}
                  {pageNumber && (
                    <div className="text-[10px] bg-primary/10 text-primary px-1.5 py-0.5 rounded-full ml-1">
                      {t("chat.page")} {pageNumber}
                    </div>
                  )}
                </div>

                <div className="mt-1 text-[10px] text-muted-foreground line-clamp-2">
                  {source.metadata?.relevantText
                    ? source.metadata.relevantText.substring(0, 100) + "..."
                    : source.content.substring(0, 100) + "..."}
                </div>
              </div>
            </div>

            <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-primary/10 dark:bg-primary/20 p-1 rounded-full">
              <Search className="h-3 w-3 text-primary" />
            </div>

            {hasFileLink && (
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <ExternalLink className="h-3 w-3 text-primary" />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Component for web sources grid
const WebSourceGrid = ({
  sources,
  t,
}: {
  sources: Source[];
  t: (translationKey: string) => string;
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
      {sources.map((source, idx) => {
        const title = source.metadata?.title || "Web Result";
        const link = source.metadata?.link || "#";
        const displayLink =
          source.metadata?.displayLink || new URL(link).hostname;

        return (
          <div
            key={idx}
            className="group relative p-2.5 rounded-lg border border-gray-200 dark:border-gray-700
                      bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800
                      transition-all duration-200 cursor-pointer hover:shadow-md"
            onClick={() => {
              // Open the web link in a new tab
              window.open(link, "_blank", "noopener,noreferrer");
            }}
          >
            <div className="flex items-start gap-2">
              <div className="mt-0.5 flex-shrink-0">
                <Globe className="h-4 w-4 text-primary/70" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div
                    className="text-xs font-medium truncate flex items-center gap-1"
                    title={title}
                  >
                    {title}
                  </div>

                  {/* Web source indicator */}
                  <div className="flex items-center gap-1">
                    <span className="text-[10px] px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full font-medium">
                      🌐
                    </span>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="text-[10px] text-blue-500 truncate">
                    {displayLink}
                  </div>
                </div>

                <div className="mt-1 text-[10px] text-muted-foreground line-clamp-3">
                  {source.content ||
                    source.metadata?.snippet ||
                    "No content available"}
                </div>
              </div>
            </div>

            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <ExternalLink className="h-3 w-3 text-primary" />
            </div>
          </div>
        );
      })}
    </div>
  );
};
