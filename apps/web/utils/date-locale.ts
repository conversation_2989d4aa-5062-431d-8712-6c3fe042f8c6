import { de, enUS, fr, es, it, nl, pt, ru, ja, zhCN } from "date-fns/locale";
import type { Language } from "@/lib/language-context";

// Map of supported languages to their date-fns locales
export const localeMap = {
  en: enUS,
  de: de,
  fr: fr,
  es: es,
  it: it,
  nl: nl,
  pt: pt,
  ru: ru,
  ja: ja,
  zh: zhCN,
  // Add more languages as needed
};

// Get the appropriate locale based on the current language
export const getLocale = (language: Language) => {
  // Return the locale for the current language, or fall back to English if not found
  return localeMap[language as keyof typeof localeMap] || enUS;
};
