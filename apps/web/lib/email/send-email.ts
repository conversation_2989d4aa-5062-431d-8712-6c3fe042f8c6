import { EmailClient } from "@azure/communication-email";
export const Mailer = {
  send: async (emailData: any) => {
    const connectionString =
      process.env.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING;

    // Use mock service in development if env vars are not set
    if (!connectionString) {
      console.warn("Connection String environment variable is missing.");
      return {
        error: "Email Connection String environment variable is missing.",
      };
    }
    const client = new EmailClient(connectionString);

    const payload = {
      senderAddress:
        emailData?.from ||
        process.env.NEXT_PUBLIC_SEND_EMAIL_FROM ||
        "<EMAIL>",
      recipients: {
        to: [{ address: emailData?.to }],
      },
      subject: emailData?.subject,
      content: {
        subject: emailData?.subject,
        // plainText: "Hello world via email.",
        html: emailData?.html,
      },
    };

    try {
      const poller = await client.beginSend(payload);
      const result = await poller.pollUntilDone();
      return result;
    } catch (error) {
      console.error("Error sending email:", error);
      throw error;
    }
  },
};

export default Mailer;
