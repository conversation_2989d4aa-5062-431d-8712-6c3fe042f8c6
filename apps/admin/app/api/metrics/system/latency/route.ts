import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get("days") || "7", 10);
    
    // Calculate the start date
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get all API requests in the period
    const apiRequests = await db.aPIRequest.findMany({
      where: {
        timestamp: {
          gte: startDate
        },
        success: true // Only include successful requests for latency metrics
      },
      select: {
        endpoint: true,
        latencyMs: true,
        tenantId: true,
        timestamp: true
      }
    });

    // Calculate average latency
    const totalLatency = apiRequests.reduce((sum, req) => sum + (req.latencyMs || 0), 0);
    const averageLatency = apiRequests.length > 0 
      ? Math.round(totalLatency / apiRequests.length) 
      : 0;

    // Calculate P95 latency
    const sortedLatencies = apiRequests
      .map(req => req.latencyMs || 0)
      .sort((a, b) => a - b);
    
    const p95Index = Math.floor(sortedLatencies.length * 0.95);
    const p95Latency = sortedLatencies.length > 0 
      ? sortedLatencies[p95Index] || sortedLatencies[sortedLatencies.length - 1] 
      : 0;

    // Calculate P99 latency
    const p99Index = Math.floor(sortedLatencies.length * 0.99);
    const p99Latency = sortedLatencies.length > 0 
      ? sortedLatencies[p99Index] || sortedLatencies[sortedLatencies.length - 1] 
      : 0;

    // Group by endpoint
    const endpointGroups = {};
    
    apiRequests.forEach(req => {
      const endpoint = req.endpoint || 'unknown';
      if (!endpointGroups[endpoint]) {
        endpointGroups[endpoint] = {
          latencies: [],
          count: 0
        };
      }
      
      endpointGroups[endpoint].latencies.push(req.latencyMs || 0);
      endpointGroups[endpoint].count++;
    });
    
    // Calculate metrics for each endpoint
    const latencyByEndpoint = Object.entries(endpointGroups).map(([endpoint, data]) => {
      const latencies = (data as any).latencies.sort((a, b) => a - b);
      const count = (data as any).count;
      
      const endpointAvgLatency = Math.round(latencies.reduce((sum, val) => sum + val, 0) / count);
      
      const p95Index = Math.floor(latencies.length * 0.95);
      const endpointP95Latency = latencies[p95Index] || latencies[latencies.length - 1];
      
      return {
        endpoint,
        requestCount: count,
        averageLatency: endpointAvgLatency,
        p95Latency: endpointP95Latency
      };
    });
    
    // Sort by average latency (descending)
    latencyByEndpoint.sort((a, b) => b.averageLatency - a.averageLatency);

    // Group by tenant
    const tenantGroups = {};
    
    apiRequests.forEach(req => {
      const tenantId = req.tenantId || 'unknown';
      if (!tenantGroups[tenantId]) {
        tenantGroups[tenantId] = {
          latencies: [],
          count: 0
        };
      }
      
      tenantGroups[tenantId].latencies.push(req.latencyMs || 0);
      tenantGroups[tenantId].count++;
    });
    
    // Get tenant names
    const tenantIds = Object.keys(tenantGroups).filter(id => id !== 'unknown');
    const tenants = await db.tenant.findMany({
      where: {
        id: {
          in: tenantIds
        }
      },
      select: {
        id: true,
        name: true
      }
    });
    
    // Calculate metrics for each tenant
    const latencyByTenant = Object.entries(tenantGroups).map(([tenantId, data]) => {
      const latencies = (data as any).latencies.sort((a, b) => a - b);
      const count = (data as any).count;
      
      const tenantAvgLatency = Math.round(latencies.reduce((sum, val) => sum + val, 0) / count);
      
      const tenant = tenants.find(t => t.id === tenantId);
      
      return {
        tenantId,
        tenantName: tenant?.name || 'Unknown',
        requestCount: count,
        averageLatency: tenantAvgLatency
      };
    });
    
    // Sort by average latency (descending)
    latencyByTenant.sort((a, b) => b.averageLatency - a.averageLatency);

    // Calculate latency over time
    const latencyOverTime = [];
    
    if (days <= 14) {
      // Daily aggregation for shorter periods
      const dailyGroups = {};
      
      apiRequests.forEach(req => {
        const day = req.timestamp.toISOString().split('T')[0];
        if (!dailyGroups[day]) {
          dailyGroups[day] = {
            latencies: [],
            count: 0
          };
        }
        
        dailyGroups[day].latencies.push(req.latencyMs || 0);
        dailyGroups[day].count++;
      });
      
      // Calculate daily averages
      for (const [day, data] of Object.entries(dailyGroups)) {
        const latencies = (data as any).latencies;
        const count = (data as any).count;
        
        latencyOverTime.push({
          date: day,
          averageLatency: Math.round(latencies.reduce((sum, val) => sum + val, 0) / count)
        });
      }
      
      // Sort by date
      latencyOverTime.sort((a, b) => a.date.localeCompare(b.date));
    }

    return NextResponse.json({
      period: `${days} days`,
      totalRequests: apiRequests.length,
      averageLatency,
      p95Latency,
      p99Latency,
      latencyByEndpoint,
      latencyByTenant,
      latencyOverTime
    });
  } catch (error) {
    console.error("Error fetching latency metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch latency metrics" },
      { status: 500 }
    );
  }
}
