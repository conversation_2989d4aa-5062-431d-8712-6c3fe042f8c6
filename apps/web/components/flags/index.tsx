import React from "react";

interface FlagProps {
  width?: string | number;
  height?: string | number;
  style?: React.CSSProperties;
}

export const UKFlag: React.FC<FlagProps> = ({ width = 24, height = 24, style = {} }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 60 30"
      width={width}
      height={height}
      style={style}
    >
      <clipPath id="a">
        <path d="M0 0v30h60V0z" />
      </clipPath>
      <clipPath id="b">
        <path d="M30 15h30v15zv15H0zH0V0zV0h30z" />
      </clipPath>
      <g clipPath="url(#a)">
        <path d="M0 0v30h60V0z" fill="#012169" />
        <path d="M0 0l60 30m0-30L0 30" stroke="#fff" strokeWidth="6" />
        <path
          d="M0 0l60 30m0-30L0 30"
          clipPath="url(#b)"
          stroke="#C8102E"
          strokeWidth="4"
        />
        <path d="M30 0v30M0 15h60" stroke="#fff" strokeWidth="10" />
        <path d="M30 0v30M0 15h60" stroke="#C8102E" strokeWidth="6" />
      </g>
    </svg>
  );
};

export const GermanFlag: React.FC<FlagProps> = ({ width = 24, height = 24, style = {} }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 5 3"
      width={width}
      height={height}
      style={style}
    >
      <path d="M0 0h5v3H0z" fill="#000" />
      <path d="M0 1h5v2H0z" fill="#D00" />
      <path d="M0 2h5v1H0z" fill="#FFCE00" />
    </svg>
  );
};

export const FlagIcon: React.FC<{ locale: string } & FlagProps> = ({
  locale,
  width,
  height,
  style,
}) => {
  switch (locale) {
    case "en":
      return <UKFlag width={width} height={height} style={style} />;
    case "de":
      return <GermanFlag width={width} height={height} style={style} />;
    default:
      return null;
  }
};
