"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatNumber, formatDate } from "@/lib/utils";
import { useEffect, useState } from "react";
import {
  useMetrics,
  dateRangeToParams,
} from "@/components/metrics/metrics-context";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";

export default function UserMetricsPage() {
  // State for user metrics
  const [userMetrics, setUserMetrics] = useState({
    activeUserCount: 0,
    totalUsers: 0,
    activeUserPercentage: 0,
    newUsers: 0,
    userGrowthRate: 0,
  });

  // State for active users over time
  const [activeUsersOverTime, setActiveUsersOverTime] = useState([]);

  // State for user role distribution
  const [roleDistribution, setRoleDistribution] = useState([]);

  // State for feature usage
  const [featureUsage, setFeatureUsage] = useState([]);

  // State for loading
  const [loading, setLoading] = useState(true);

  // Get metrics context
  const { dateRange, setIsLoading } = useMetrics();

  useEffect(() => {
    async function fetchUserMetrics() {
      try {
        setIsLoading(true);
        setLoading(true);

        // Create date range params
        const dateParams = dateRangeToParams(dateRange);

        // Fetch basic user metrics
        const metricsResponse = await fetch(
          `/api/metrics/users/active?period=month&${dateParams}`
        );
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          setUserMetrics({
            ...metricsData,
            newUsers: 0, // This would come from another API
            userGrowthRate: 0, // This would come from another API
          });
        }

        // Fetch active users over time
        const timeResponse = await fetch(
          `/api/metrics/users/active/history?period=6months&${dateParams}`
        );
        if (timeResponse.ok) {
          const timeData = await timeResponse.json();
          setActiveUsersOverTime(timeData.monthlyData || []);
        }

        // Fetch user role distribution
        const rolesResponse = await fetch(
          `/api/metrics/security/roles?${dateParams}`
        );
        if (rolesResponse.ok) {
          const rolesData = await rolesResponse.json();
          setRoleDistribution(
            rolesData.roleDistribution.map((role) => ({
              name: role.role,
              value: role.count,
            })) || []
          );
        }

        // Fetch feature usage
        const featureResponse = await fetch(
          `/api/metrics/features/usage?${dateParams}`
        );
        if (featureResponse.ok) {
          const featureData = await featureResponse.json();
          setFeatureUsage(featureData.featureUsage || []);
        }
      } catch (error) {
        console.error("Error fetching user metrics:", error);
      } finally {
        setLoading(false);
        setIsLoading(false);
      }
    }

    fetchUserMetrics();
  }, [dateRange, setIsLoading]);

  // Colors for the pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  // Fallback data for charts if API returns empty
  const fallbackFeatureData = [
    { name: "Chat", value: 0 },
    { name: "Search", value: 0 },
    { name: "Upload", value: 0 },
    { name: "Share", value: 0 },
    { name: "Export", value: 0 },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : formatNumber(userMetrics.totalUsers)}
            </div>
            <p className="text-xs text-muted-foreground">
              {userMetrics.userGrowthRate > 0 ? "+" : ""}
              {userMetrics.userGrowthRate}% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading
                ? "Loading..."
                : formatNumber(userMetrics.activeUserCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {userMetrics.activeUserPercentage}% of total users
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              New Users (30d)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : formatNumber(userMetrics.newUsers)}
            </div>
            <p className="text-xs text-muted-foreground">
              {userMetrics.totalUsers > 0
                ? `${Math.round(
                    (userMetrics.newUsers / userMetrics.totalUsers) * 100
                  )}% growth rate`
                : "0% growth rate"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Average Session Duration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : "12m 30s"}
            </div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Active Users Over Time</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={activeUsersOverTime}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [`${value} users`, "Active Users"]}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="activeUsers"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                    name="Active Users"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Users by Role</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={roleDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {roleDistribution.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} users`, "Count"]} />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Feature Usage</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          {loading ? (
            <div className="flex h-full items-center justify-center">
              <p>Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={
                  featureUsage.length > 0 ? featureUsage : fallbackFeatureData
                }
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip
                  formatter={(value) => [`${value} uses`, "Usage Count"]}
                />
                <Legend />
                <Bar dataKey="value" fill="#8884d8" name="Usage Count" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
