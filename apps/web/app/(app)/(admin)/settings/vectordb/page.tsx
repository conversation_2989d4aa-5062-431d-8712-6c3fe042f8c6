import VectorDBSettingsPage from "@/components/wrapper-screens/organization-settings/vectordb/vectordb-settings-page";
import { getVectorDB } from "@/services";
import { cookies } from "next/headers";

export default async function Page() {
  const tenantId = cookies().get("currentOrganizationId")?.value ?? "";

  try {
    // Handle case where folder data is not found
    const vector = await getVectorDB({ tenantId });
    return <VectorDBSettingsPage vector={vector?.settings} />;
  } catch (error) {
    console.error("Error loading folder:", error);
  }
}
