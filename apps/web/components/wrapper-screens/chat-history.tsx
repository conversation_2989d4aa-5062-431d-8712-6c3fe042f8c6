"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { formatDistanceToNow } from "date-fns";
import { FileText, Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { deleteChat, updateChat } from "@/services";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { getLocale } from "@/utils/date-locale";

interface Message {
  content: string;
  role: "user" | "assistant";
  createdAt: string;
  updatedAt: string;
}

interface Chat {
  id: string;
  title: string;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
}

interface ChatHistoryProps {
  chatHistory: Chat[];
}

export default function ChatHistory({ chatHistory }: ChatHistoryProps) {
  const router = useRouter();
  const { t, language } = useLanguage();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");

  const handleStartEditing = (chat: Chat) => {
    setEditingId(chat.id);
    setEditingTitle(chat.title);
  };

  const handleUpdateTitle = async (chatId: string) => {
    try {
      const response = await updateChat({
        id: chatId,
        title: editingTitle,
      });
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chatHistory.renameSuccess"));
        router.refresh();
      }
    } catch (error) {
      toast.error(t("chatHistory.renameFailed"));
    } finally {
      setEditingId(null);
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    try {
      const response = await deleteChat(chatId);
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chatHistory.deleteSuccess"));
        router.refresh();
      }
    } catch (error) {
      toast.error(t("chatHistory.deleteFailed"));
    }
  };

  if (!chatHistory || chatHistory.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">{t("chatHistory.title")}</h1>
        <div className="text-center py-12">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-4">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <h2 className="text-lg font-semibold mb-2">
            {t("chatHistory.noHistoryYet")}
          </h2>
          <p className="text-muted-foreground mb-4">
            {t("chatHistory.startNewChatPrompt")}
          </p>
          <Button asChild>
            <Link href="/ask-ai">{t("chatHistory.startNewChat")}</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="text-2xl font-bold mb-6">{t("chatHistory.title")}</h1>
      <div className="grid gap-4">
        {chatHistory.map((chat) => (
          <div key={chat.id}>
            {editingId === chat.id ? (
              <Card className="p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start gap-4">
                  <div className="flex-1">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={editingTitle}
                        onChange={(e) => setEditingTitle(e.target.value)}
                        className="flex-1 px-2 py-1 border rounded"
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            handleUpdateTitle(chat.id);
                          }
                        }}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdateTitle(chat.id)}
                      >
                        {t("common.save")}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingId(null)}
                      >
                        {t("common.cancel")}
                      </Button>
                    </div>
                    <p className="text-xs w-[400px] mt-2 truncate">
                      {chat?.messages?.[0]?.content}
                    </p>
                    <p className="text-xs text-gray-400 mt-2">
                      {formatDistanceToNow(
                        new Date(
                          chat?.messages?.[0]?.updatedAt || chat?.updatedAt
                        ),
                        {
                          addSuffix: true,
                          locale: getLocale(language),
                        }
                      )}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteChat(chat.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ) : (
              <Link
                href={`/ask-ai/${chat.id}`}
                className="block hover:text-blue-600"
              >
                <Card className="p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start gap-4">
                    <div className="flex-1">
                      <h2 className="text-lg font-semibold mb-1">
                        {chat.title}
                      </h2>
                      <p className="text-xs w-[400px] mt-2 truncate">
                        {chat?.messages?.[0]?.content}
                      </p>
                      <p className="text-xs text-gray-400 mt-2">
                        {formatDistanceToNow(
                          new Date(
                            chat?.messages?.[0]?.updatedAt || chat?.updatedAt
                          ),
                          {
                            addSuffix: true,
                            locale: getLocale(language),
                          }
                        )}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleStartEditing(chat);
                        }}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleDeleteChat(chat.id);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </Link>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
