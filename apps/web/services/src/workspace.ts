import { apiUrl, fetchJson } from "..";

export const createWorkspace = async (data, tenantId, userId) => {
  try {
    const response = await fetch(`${apiUrl}/workspaces`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    if (!response.ok) {
      throw new Error(responseData?.error || "Network response was not ok");
    }
    return responseData;
  } catch (error) {
    console.log({ "Create workspace api calling error": error });
    return { error: error?.message || "Error Creating The Workspace" };
  }
};
export const getWorkspace = async (tenantId, userId, workspaceSlug = "") => {
  if (workspaceSlug) {
    const response = await fetchJson(
      `${apiUrl}/workspaces?slug=${workspaceSlug}`,
      userId,
      tenantId
    );
    return response;
  }
  const response = await fetchJson(`${apiUrl}/workspaces`, userId, tenantId);
  return response;
};

export const getWorkspaceMember = async (tenantId, userId, workspaceSlug) => {
  const response = await fetchJson(
    `${apiUrl}/workspaces/members?workspaceSlug=${workspaceSlug}`,
    userId,
    tenantId
  );
  return response;
};

export const inviteWorkspaceMember = async (data, tenantId, userId) => {
  try {
    const url = `${apiUrl}/workspaces/members`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Invite user api error": error });
    return { error: error.message || "Error inviting user" };
  }
};

export const removeWorkspaceMember = async (data, tenantId, userId) => {
  try {
    const url = `${apiUrl}/workspaces/members?workspaceSlug=${data.slug}&memberId=${data.memberId}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Remove workspace member api error": error });
    return { error: error.message || "Error removing workspace member" };
  }
};

export const updateWorkspace = async (id, data, tenantId, userId) => {
  try {
    const url = `${apiUrl}/workspaces?id=${id}`;
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Update workspace api error": error });
    return { error: error.message || "Error updating workspace" };
  }
};

export const deleteWorkspace = async (data, tenantId, userId) => {
  try {
    const url = `${apiUrl}/workspaces?slug=${data.slug}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Delete workspace api error": error });
    return { error: error.message || "Error deleting workspace" };
  }
};
