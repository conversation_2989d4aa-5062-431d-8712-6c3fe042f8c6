"use client";

import React, { useState, useEffect } from "react";
import { Share2, Co<PERSON>, CheckCircle, Globe, Lock, Calendar, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/lib/language-context";
import toast from "react-hot-toast";

interface ShareInfo {
  shared: boolean;
  shareToken?: string;
  shareUrl?: string;
  isPublic?: boolean;
  expiresAt?: string;
  createdBy?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt?: string;
}

interface ShareButtonProps {
  chatId: string;
  chatTitle?: string;
}

export const ShareButton: React.FC<ShareButtonProps> = ({
  chatId,
  chatTitle,
}) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [shareInfo, setShareInfo] = useState<ShareInfo>({ shared: false });
  const [isPublic, setIsPublic] = useState(false);
  const [expiresAt, setExpiresAt] = useState("");
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  // Load existing share info when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadShareInfo();
    }
  }, [isOpen, chatId]);

  const loadShareInfo = async () => {
    try {
      const response = await fetch(`/api/chat/${chatId}/share`);
      if (response.ok) {
        const data = await response.json();
        setShareInfo(data);
        setIsPublic(data.isPublic || false);
        setExpiresAt(data.expiresAt ? new Date(data.expiresAt).toISOString().slice(0, 16) : "");
      }
    } catch (error) {
      console.error("Error loading share info:", error);
    }
  };

  const handleCreateShare = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/chat/${chatId}/share`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isPublic,
          expiresAt: expiresAt ? new Date(expiresAt).toISOString() : null,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setShareInfo({
          shared: true,
          shareToken: data.shareToken,
          shareUrl: data.shareUrl,
          isPublic: data.isPublic,
          expiresAt: data.expiresAt,
        });
        toast.success("Thread shared successfully");
      } else {
        toast.error("Failed to share thread");
      }
    } catch (error) {
      console.error("Error creating share:", error);
      toast.error("Failed to share thread");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateShare = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/chat/${chatId}/share`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isPublic,
          expiresAt: expiresAt ? new Date(expiresAt).toISOString() : null,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setShareInfo({
          ...shareInfo,
          isPublic: data.isPublic,
          expiresAt: data.expiresAt,
        });
        toast.success("Share settings updated");
      } else {
        toast.error("Failed to update share settings");
      }
    } catch (error) {
      console.error("Error updating share:", error);
      toast.error("Failed to update share settings");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteShare = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/chat/${chatId}/share`, {
        method: "DELETE",
      });

      if (response.ok) {
        setShareInfo({ shared: false });
        setIsPublic(false);
        setExpiresAt("");
        toast.success("Share link removed");
      } else {
        toast.error("Failed to remove share link");
      }
    } catch (error) {
      console.error("Error deleting share:", error);
      toast.error("Failed to remove share link");
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = async () => {
    if (shareInfo.shareUrl) {
      try {
        await navigator.clipboard.writeText(shareInfo.shareUrl);
        setCopied(true);
        toast.success("Link copied to clipboard");
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        toast.error("Failed to copy link");
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Share2 className="h-4 w-4" />
          Share
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share Thread
          </DialogTitle>
          <DialogDescription>
            {chatTitle ? `Share "${chatTitle}"` : "Share this conversation"} with others
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {shareInfo.shared ? (
            <>
              {/* Existing Share */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Share Link</Label>
                  <Badge variant={shareInfo.isPublic ? "default" : "secondary"}>
                    {shareInfo.isPublic ? (
                      <>
                        <Globe className="h-3 w-3 mr-1" />
                        Public
                      </>
                    ) : (
                      <>
                        <Lock className="h-3 w-3 mr-1" />
                        Private
                      </>
                    )}
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <Input
                    value={shareInfo.shareUrl || ""}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyLink}
                    className="flex items-center gap-1"
                  >
                    {copied ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {shareInfo.expiresAt && (
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    Expires: {formatDate(shareInfo.expiresAt)}
                  </div>
                )}
              </div>

              {/* Share Settings */}
              <div className="space-y-3 pt-3 border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="public-toggle" className="text-sm font-medium">
                      Public Access
                    </Label>
                    <p className="text-xs text-gray-500">
                      Anyone with the link can view
                    </p>
                  </div>
                  <Switch
                    id="public-toggle"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                </div>

                <div>
                  <Label htmlFor="expires-at" className="text-sm font-medium">
                    Expiration (optional)
                  </Label>
                  <Input
                    id="expires-at"
                    type="datetime-local"
                    value={expiresAt}
                    onChange={(e) => setExpiresAt(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-3 border-t">
                <Button
                  onClick={handleUpdateShare}
                  disabled={loading}
                  className="flex-1"
                >
                  Update Settings
                </Button>
                <Button
                  variant="outline"
                  onClick={handleDeleteShare}
                  disabled={loading}
                  className="flex items-center gap-1"
                >
                  <Trash2 className="h-4 w-4" />
                  Remove
                </Button>
              </div>
            </>
          ) : (
            <>
              {/* Create New Share */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="public-toggle" className="text-sm font-medium">
                      Public Access
                    </Label>
                    <p className="text-xs text-gray-500">
                      Anyone with the link can view
                    </p>
                  </div>
                  <Switch
                    id="public-toggle"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                </div>

                <div>
                  <Label htmlFor="expires-at" className="text-sm font-medium">
                    Expiration (optional)
                  </Label>
                  <Input
                    id="expires-at"
                    type="datetime-local"
                    value={expiresAt}
                    onChange={(e) => setExpiresAt(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>

              <Button
                onClick={handleCreateShare}
                disabled={loading}
                className="w-full"
              >
                Create Share Link
              </Button>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
