from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.services.workspace_rag import WorkspaceRAGManager
from src.api.deps import get_db
from starlette.responses import StreamingResponse
import json
import datetime
from src.services.usage_tracker import Usage<PERSON>racker
from bson import ObjectId
from src.agents.rag_agent import Custom<PERSON><PERSON>NEncoder
import logging
from urllib.parse import unquote
import aiohttp
import base64
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize the workspace RAG manager
workspace_rag_manager = WorkspaceRAGManager()

class ImageAttachment(BaseModel):
    id: str
    url: str
    name: str
    type: str
    size: int
    preview: Optional[str] = None

async def process_images_for_context(images: List[ImageAttachment]) -> str:
    """
    Process images and generate descriptions using vision model.
    """
    image_descriptions = []

    for i, image in enumerate(images):
        try:
            # Download image from URL
            async with aiohttp.ClientSession() as session:
                async with session.get(image.url) as response:
                    if response.status == 200:
                        image_data = await response.read()

                        # Convert to base64
                        img_base64 = base64.b64encode(image_data).decode()

                        # Determine image format
                        img_format = image.type.split('/')[-1] if '/' in image.type else 'jpeg'

                        # Call vision model
                        description = await analyze_image_with_vision_model(img_base64, img_format, image.name)

                        image_descriptions.append(f"Image {i+1} ({image.name}): {description}")
                    else:
                        logger.error(f"Failed to download image {image.name}: HTTP {response.status}")
                        image_descriptions.append(f"Image {i+1} ({image.name}): Failed to process image")

        except Exception as e:
            logger.error(f"Error processing image {image.name}: {str(e)}")
            image_descriptions.append(f"Image {i+1} ({image.name}): Error processing image - {str(e)}")

    return "\n".join(image_descriptions)

async def analyze_image_with_vision_model(img_base64: str, img_format: str, filename: str) -> str:
    """
    Analyze image using vision model API.
    """
    try:
        endpoint = f"{os.getenv('AZURE_VISION_API_ENDPOINT')}/openai/deployments/{os.getenv('AZURE_VISION_MODEL')}/chat/completions?api-version=2024-02-15-preview"
        headers = {
            "Content-Type": "application/json",
            "api-key": os.getenv("DEEPSEEK_API_KEY")
        }

        messages = [
            {
                "role": "system",
                "content": [
                    {"type": "text", "text": "You are an AI assistant that provides detailed descriptions of images. Focus on all visible elements, text, objects, people, scenes, and any relevant context that would help answer questions about the image."}
                ]
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": f"Analyze this image ({filename}) and provide a comprehensive description. Include all visible text, objects, people, scenes, colors, and any other relevant details."},
                    {"type": "image_url", "image_url": {"url": f"data:image/{img_format};base64,{img_base64}"}}
                ]
            }
        ]

        data = {
            "messages": messages,
            "model": os.getenv('AZURE_VISION_MODEL', "mistral-medium-2505"),
            "temperature": 0.7,
            "max_tokens": 1000
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    logger.error(f"Vision API error: HTTP {response.status}")
                    return f"Unable to analyze image {filename} - API error"

    except Exception as e:
        logger.error(f"Error calling vision model: {str(e)}")
        return f"Unable to analyze image {filename} - {str(e)}"

class ChatMessage(BaseModel):
    role: str
    content: str
    id: Optional[str] = None
    createdAt: Optional[str] = None

class ChatQuery(BaseModel):
    question: Optional[str] = None
    stream: Optional[bool] = False
    config: Optional[Dict[str, Any]] = None
    previous_message: Optional[str] = None
    images: Optional[List[ImageAttachment]] = None
    messages: Optional[List[ChatMessage]] = None

class DocumentIndex(BaseModel):
    workspaceSlug: str
    document_path: str
    document_type: Optional[str] = "auto"
    metadata: Optional[Dict[str, Any]] = None

class WorkspaceAccess(BaseModel):
    user_id: str
    workspace_ids: List[str]

# Custom JSON encoder to handle non-serializable objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        # Add more custom type handling as needed
        return super().default(obj)

@router.post("/chat", response_model=Dict[str, Any], status_code=200)
async def chat_with_workspace_documents(
    query: ChatQuery,
    current_user: str,
    user_name: str,
    tenant_id: str,
    db = Depends(get_db)
):
    """
    Chat with documents across all accessible workspaces or a specific workspace.
    Returns a structured JSON response with title and answer.
    """
    # Set the database client
    workspace_rag_manager.db_client = db
    stream = query.stream if hasattr(query, 'stream') else False

    # Handle both AI SDK format (messages array) and legacy format (question + previous_message)
    if query.messages and len(query.messages) > 0:
        # AI SDK format - extract question from last user message and build conversation history
        user_messages = [msg for msg in query.messages if msg.role == "user"]
        if user_messages:
            question = user_messages[-1].content
        else:
            question = query.question or ""

        # Build conversation history from messages (excluding the current question)
        conversation_messages = query.messages[:-1] if len(query.messages) > 1 else []
        if conversation_messages:
            # Format conversation history as structured JSON
            conversation_history = {
                "conversationHistory": [
                    {
                        "role": msg.role,
                        "content": msg.content.strip(),
                        "turn": idx + 1,
                        "timestamp": msg.createdAt or "",
                        "id": msg.id or ""
                    }
                    for idx, msg in enumerate(conversation_messages)
                ],
                "totalTurns": len(conversation_messages),
                "contextNote": "This is the conversation history. The current question is provided separately."
            }
            previous_message = json.dumps(conversation_history, indent=2)
            logger.info(f"AI SDK format: Built conversation history with {len(conversation_messages)} messages for question: '{question[:50]}...'")
        else:
            previous_message = None
            logger.info(f"AI SDK format: No conversation history for question: '{question[:50]}...'")
    else:
        # Legacy format
        question = query.question
        previous_message = query.previous_message
        logger.info(f"Legacy format: Using question: '{question[:50] if question else 'None'}...' with previous_message: {'Yes' if previous_message else 'No'}")

    # Override query object properties for consistent processing
    query.question = question
    query.previous_message = previous_message

    if not stream:
        # Extract web search config
        include_web_results = False
        if query.config and "includeWebResults" in query.config:
            include_web_results = query.config.get("includeWebResults", False) is True
            logger.info(f"Web search enabled: {include_web_results} for query: '{query.question}'")
        else:
            logger.info(f"Web search disabled for query: '{query.question}'")

        # Process images if provided
        image_context = ""
        if query.images and len(query.images) > 0:
            logger.info(f"Processing {len(query.images)} images for query: '{query.question}'")
            image_context = await process_images_for_context(query.images)

        # Combine question with image context
        enhanced_question = query.question
        if image_context:
            enhanced_question = f"{query.question}\n\nImage Analysis:\n{image_context}"

        # Process the query with access control
        result = await workspace_rag_manager.query_workspace(
            user_id=current_user,
            question=enhanced_question,
            tenant_id=tenant_id,
            user_name=user_name,
            previous_message=query.previous_message,
            include_web_results=include_web_results
        )

        if result.get("status") == 403:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have access to this workspace"
            )
        elif result.get("status") == 500:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "An error occurred processing your request")
            )

        # Get sources and serialize them to handle ObjectId
        sources = result.get("sources", [])
        serializable_sources = json.loads(json.dumps(sources, cls=CustomJSONEncoder))

        # Return the structured JSON response with title, answer, and sources
        return {
            "answer": result.get("answer", ""),
            "title": result.get("title", ""),
            "sources": serializable_sources,  # Include serialized sources in the response
            "status": result.get("status", 200)
        }
    else:
        # Extract web search config for streaming
        include_web_results = False
        if query.config and "includeWebResults" in query.config:
            include_web_results = query.config.get("includeWebResults", False) is True
            logger.info(f"Web search enabled: {include_web_results} for streaming query: '{query.question}'")
        else:
            logger.info(f"Web search disabled for streaming query: '{query.question}'")

        # Process images if provided for streaming
        image_context = ""
        if query.images and len(query.images) > 0:
            logger.info(f"Processing {len(query.images)} images for streaming query: '{query.question}'")
            image_context = await process_images_for_context(query.images)
            logger.info(f"Image context: {image_context}")

        # Combine question with image context for streaming
        enhanced_question = query.question
        if image_context:
            enhanced_question = f"{query.question}\n\nImage Analysis:\n{image_context}"
        logger.info(f"Streaming query: '{enhanced_question}...'")

        # Streaming response
        stream_generator = await workspace_rag_manager.query_workspace(
            user_id=current_user,
            question=enhanced_question,
            tenant_id=tenant_id,
            user_name=user_name,
            previous_message=query.previous_message,
            stream=True,
            include_web_results=include_web_results
        )
        async def text_stream_generator():
            # Store the title for later use when creating the chat
            title = None
            # Flag to track if we've sent the sources in the first chunk
            sources_sent = False

            async for chunk in stream_generator:
                try:
                    # Check if this is an error chunk
                    if "error" in chunk:
                        # Stream the error message directly to the client
                        error_message = chunk.get("error", "Unknown error occurred")
                        yield f"ERROR: {error_message}"
                        # Stop streaming after sending the error
                        break

                    # Extract the answer and sources from the chunk
                    answer = chunk.get("answer", "")
                    sources = chunk.get("sources", [])

                    # Store the title from the first chunk that has one
                    if title is None and chunk.get("title"):
                        title = chunk.get("title")

                    # For the first chunk, send the sources as JSON
                    if not sources_sent and sources:
                        # Send the sources as a JSON object in the first chunk
                        # Use CustomJSONEncoder to handle ObjectId serialization
                        # Include the answer in the same JSON object to ensure proper parsing
                        first_chunk = {
                            "sources": sources,
                            "answer": answer,  # Include the answer in the first chunk
                            "is_first_chunk": True  # Add a marker to identify this as the first chunk
                        }
                        sources_json = json.dumps(first_chunk, cls=CustomJSONEncoder)
                        yield sources_json
                        sources_sent = True
                        # We've already included the answer in the JSON, so no need to yield it separately
                    else:
                        # For subsequent chunks, only stream the answer text
                        yield answer
                except TypeError as e:
                    # For text streaming, just yield the error message directly
                    error_message = f"Error processing response: {str(e)}"
                    yield f"ERROR: {error_message}"
                    break
                except Exception as e:
                    # Handle any other exceptions that might occur
                    error_message = f"Unexpected error: {str(e)}"
                    yield f"ERROR: {error_message}"
                    break

        return StreamingResponse(
            content=text_stream_generator(),
            media_type="text/plain"  # Plain text format for streaming
        )
@router.post("/index", response_model=Dict[str, Any], status_code=200)
async def index_workspace_document(
    index_request: DocumentIndex,
    current_user: str,
    tenant_id: str,
    file_id: str,
    db = Depends(get_db)
):
    # Initialize usage tracker
    start_time = datetime.datetime.now(datetime.timezone.utc)
    usage_tracker = UsageTracker(db)
    """
    Index a document in a workspace, with access control.
    Downloads the document from the provided path if it's a URL.
    """
    # Set the database client
    workspace_rag_manager.db_client = db
    await db.File.update_one(
        {"_id": ObjectId(file_id)},
        {"$set": {"vectorizationStatus": "PROCESSING"}}
    )

    # Check if document_path is a URL and download if needed
    document_path = index_request.document_path
    if document_path.startswith(('http://', 'https://')):
        try:
            import aiohttp
            import os
            from urllib.parse import urlparse

            # Create downloads directory if it doesn't exist
            download_dir = "./files"
            os.makedirs(download_dir, exist_ok=True)

            # Extract filename from URL or generate one
            parsed_url = urlparse(document_path)
            filename = os.path.basename(parsed_url.path)
            if not filename:
                # Generate filename if not present in URL
                import uuid
                ext = ".pdf"  # Default extension
                if "." in document_path:
                    ext = "." + document_path.split(".")[-1]
                filename = f"{uuid.uuid4()}{ext}"

            # Full path for downloaded file
            local_path = os.path.join(download_dir, filename)

            # Download the file
            async with aiohttp.ClientSession() as session:
                async with session.get(document_path) as response:
                    if response.status != 200:
                        return {
                            "error": f"Failed to download document: HTTP {response.status}",
                            "status": 500
                        }

                    content = await response.read()
                    with open(local_path, "wb") as f:
                        f.write(content)

            # Update document path to local path
            document_path = local_path

            # Store the path to delete after indexing
            file_to_delete = local_path

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error downloading document: {str(e)}"
            )
    else:
        file_to_delete = None
    slug=unquote(index_request.workspaceSlug)
    workspace=await db.Workspace.find_one(
        {
            "slug":slug,
        }
    )
    # Process the indexing with access control
    result = await workspace_rag_manager.index_document_with_access_check(
        user_id=current_user,
        workspace_id=workspace["_id"],
        document_path=document_path,
        document_type=index_request.document_type,
        metadata=index_request.metadata,
        tenant_id=tenant_id,
        file_id=file_id,
        slug=slug
    )

    # Log token usage for document indexing
    try:
        # Read file content based on file type
        import magic
        import PyPDF2

        mime = magic.Magic(mime=True)
        file_type = mime.from_file(document_path)

        if file_type.startswith('application/pdf'):
            # Handle PDF files
            with open(document_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                document_content = '\n'.join([page.extract_text() for page in pdf_reader.pages])
        elif file_type.startswith('text/'):
            # Handle text files
            with open(document_path, 'r', encoding='utf-8', errors='ignore') as file:
                document_content = file.read()
        else:
            # Skip token logging for unsupported file types
            logger.warning(f"Skipping token logging for unsupported file type: {file_type}")
            document_content = ''

        metadata_str = json.dumps(index_request.metadata) if index_request.metadata else ""

        # Log token usage for document content and metadata
        await usage_tracker.log_token_usage(
            tenant_id=tenant_id,
            input_text=document_content + metadata_str,
            output_text="",  # No output for indexing
            request_type="document_indexing",
            model_used="embed-v-4-0"  # Standard embedding model
        )
        await usage_tracker.log_api_request(
           user_id=current_user,
            tenant_id=tenant_id,
            endpoint="/index_document",
            method="POST",
            status_code=200,
            duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000)
        )
    except Exception as e:
        logger.error(f"Error logging token usage for document indexing: {e}")

    # Delete the downloaded file after indexing if it was downloaded
    if file_to_delete and os.path.exists(file_to_delete):
        try:
            os.remove(file_to_delete)
            result["file_cleanup"] = "Temporary file deleted successfully"
        except Exception as e:
            result["file_cleanup_error"] = f"Failed to delete temporary file: {str(e)}"

    if result.get("status") == 403:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have access to this workspace"
        )
    elif result.get("status") == 500:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result.get("error", "An error occurred indexing your document")
        )

    return result

@router.get("/check-access", response_model=WorkspaceAccess, status_code=200)
async def check_workspace_access(
    current_user: str,
    db = Depends(get_db)
):
    """
    Get list of workspaces the current user has access to.
    """
    # Set the database client
    workspace_rag_manager.db_client = db

    # Get workspaces the user has access to
    workspace_ids = await workspace_rag_manager.get_user_workspaces(current_user)

    return {
        "user_id": current_user,
        "workspace_ids": workspace_ids
    }
@router.get("/generate-title", status_code=200)
async def generate_title(
    query: str,
    tenant_id:str,
    db = Depends(get_db)
):
    """
    Get list of workspaces the current user has access to.
    """

        # Set the database client
    workspace_rag_manager.db_client = db
    workspace=await db.Workspace.find_one(
        {
            "tenantId":ObjectId(tenant_id),
        }
    )
    # Get workspaces the user has access to
    content = await workspace_rag_manager.generate_title_from_question(query,tenant_id,workspace["_id"])

    return content

@router.get("/delete-file", status_code=200)
async def delete_file(
    file_id: str,
    workspaceSlug: str,
    tenant_id: str,
    db = Depends(get_db)
):
    workspace_rag_manager.db_client = db
    slug=unquote(workspaceSlug)
    workspace=await db.Workspace.find_one(
    {
        "slug":slug,
    }
    )
    content = await workspace_rag_manager.delete_file(file_id=file_id,workspace_id=workspace["_id"],tenant_id=tenant_id)

    return content
