import { BlobServiceClient, ContainerClient } from "@azure/storage-blob";

const containerName =
  process.env.NEXT_PUBLIC_AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString =
  process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING || "";

export class AzureStorageService {
  private blobServiceClient: BlobServiceClient;
  private containerClient: ContainerClient;

  constructor() {
    this.blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    this.containerClient =
      this.blobServiceClient.getContainerClient(containerName);
  }

  async uploadFile(file: File): Promise<string> {
    try {
      // Create container if it doesn't exist
      await this.containerClient.createIfNotExists({
        access: "container",
      });

      // Generate unique blob name
      const blobName = `${Date.now()}-${file.name}`;
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

      // Upload file
      await blockBlobClient.uploadData(await file.arrayBuffer(), {
        blobHTTPHeaders: { blobContentType: file.type },
      });

      // Return the blob URL
      return blockBlobClient.url;
    } catch (error) {
      console.error("Error uploading file:", error);
      throw new Error("Failed to upload file to Azure Blob Storage");
    }
  }

  async deleteFile(blobUrl: string): Promise<void> {
    try {
      const blobName = blobUrl.split("/").pop();
      if (!blobName) throw new Error("Invalid blob URL");

      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);
      await blockBlobClient.delete();
    } catch (error) {
      console.error("Error deleting file:", error);
      throw new Error("Failed to delete file from Azure Blob Storage");
    }
  }
}

// Export singleton instance
export const azureStorage = new AzureStorageService();
