# Global Search Implementation

This document describes the comprehensive global search system implemented for the Swiss Knowledge Hub with role-based access control.

## Overview

The global search system allows users to search across all accessible content based on their role permissions:

- **Admin/Owner**: Can search files, pages, folders, workspaces, and their AI conversations
- **Custom**: Can search files, pages, folders, workspaces they have access to, and their AI conversations  
- **Member**: Can only search their AI conversations (messages and chat titles)

## Architecture

### Backend (FastAPI)
- **Location**: `apps/api/src/api/api_v1/endpoints/global_search.py`
- **Endpoint**: `/api/v1/global-search/search`
- **Features**:
  - Role-based access control
  - Hybrid keyword/semantic search
  - MongoDB aggregation pipelines
  - Tenant isolation
  - Performance optimized queries

### Frontend (Next.js)
- **API Route**: `apps/web/app/api/global-search/route.ts`
- **Service**: `apps/web/services/src/global-search.ts`
- **Components**: `apps/web/components/global-search/`

## Components

### GlobalSearchProvider
Provides global search context and keyboard shortcuts (Cmd/Ctrl+K).

```tsx
import { GlobalSearchProvider } from "@/components/global-search";

<GlobalSearchProvider tenantId={tenantId}>
  {children}
</GlobalSearchProvider>
```

### GlobalSearchButton
Search button with keyboard shortcut indicator.

```tsx
import { GlobalSearchButton } from "@/components/global-search";

<GlobalSearchButton size="sm" />
```

### GlobalSearchDialog
Main search interface with real-time results and categorization.

## Role-Based Access Control

### Admin/Owner Permissions
```typescript
// Can search all content types
const searchTypes = ["file", "page", "folder", "workspace", "chat", "message"];
// Access to all workspaces in tenant
const accessibleWorkspaces = await getAllTenantWorkspaces(tenantId);
```

### Custom Role Permissions
```typescript
// Can search content they have access to
const searchTypes = ["file", "page", "folder", "workspace", "chat", "message"];
// Only workspaces they're members of
const accessibleWorkspaces = await getUserWorkspaces(userId, tenantId);
```

### Member Permissions
```typescript
// Can only search their own conversations
const searchTypes = ["chat", "message"];
// No workspace access
const accessibleWorkspaces = [];
```

## Search Implementation

### Backend Search Functions

#### Files Search
```python
async def search_files(db, query, accessible_workspaces, limit, skip):
    search_filter = {
        "workspaceId": {"$in": [ObjectId(ws_id) for ws_id in accessible_workspaces]},
        "$or": [
            {"name": {"$regex": query, "$options": "i"}},
            {"content": {"$regex": query, "$options": "i"}}
        ]
    }
```

#### Chat/Message Search
```python
async def search_messages(db, query, user_id, tenant_id, limit, skip):
    # Only search user's own chats
    user_chats = await db.Chat.find({
        "userId": ObjectId(user_id),
        "tenantId": ObjectId(tenant_id)
    })
```

### Frontend Service

```typescript
export const performGlobalSearch = async (params: GlobalSearchParams): Promise<GlobalSearchResponse> => {
  const searchParams = new URLSearchParams({
    query: params.query,
    tenantId: params.tenantId,
    limit: (params.limit || 20).toString(),
    skip: (params.skip || 0).toString(),
  });

  if (params.types && params.types.length > 0) {
    searchParams.append("types", params.types.join(","));
  }

  return await fetchJson(`/api/global-search?${searchParams.toString()}`);
};
```

## Usage Examples

### Basic Integration
```tsx
// In your layout
import { GlobalSearchProvider } from "@/components/global-search";

export default function Layout({ children }) {
  return (
    <GlobalSearchProvider tenantId={tenantId}>
      {children}
    </GlobalSearchProvider>
  );
}
```

### Adding Search Button
```tsx
// In your header
import { GlobalSearchButton } from "@/components/global-search";

export function Header() {
  return (
    <header>
      <GlobalSearchButton size="sm" />
    </header>
  );
}
```

### Programmatic Search
```tsx
import { useGlobalSearch } from "@/components/global-search";

function MyComponent() {
  const { openSearch } = useGlobalSearch();
  
  return (
    <button onClick={openSearch}>
      Open Search
    </button>
  );
}
```

## Keyboard Shortcuts

- **Cmd/Ctrl + K**: Open global search
- **Escape**: Close search dialog
- **Enter**: Navigate to selected result

## Search Result Types

### File Results
- File name and content search
- Workspace context
- File metadata (type, size, extension)

### Page Results  
- Page name and content search
- Workspace context
- Creation/modification dates

### Folder Results
- Folder name search
- Workspace context
- Hierarchical structure

### Workspace Results
- Workspace name and description search
- Direct workspace access

### Chat Results
- Chat title search
- Message count metadata
- User's own chats only

### Message Results
- Message content search
- Chat context
- Snippet highlighting
- User's own messages only

## Performance Considerations

### Database Indexes
Ensure proper indexes are created for search performance:

```javascript
// MongoDB indexes for search
db.File.createIndex({ "name": "text", "content": "text" });
db.Page.createIndex({ "name": "text", "content": "text" });
db.Chat.createIndex({ "title": "text", "userId": 1, "tenantId": 1 });
db.Message.createIndex({ "content": "text", "chatId": 1 });
```

### Caching
- Search results are not cached to ensure real-time accuracy
- Workspace permissions are cached per request
- Database connections are pooled

### Pagination
- Default limit: 20 results
- Configurable skip/limit parameters
- Category-based result distribution

## Security

### Tenant Isolation
All searches are isolated by tenant ID to prevent cross-tenant data access.

### Permission Validation
User permissions are validated on every search request based on:
- User's role in the tenant
- Workspace membership
- Custom role permissions

### Input Sanitization
Search queries are sanitized to prevent injection attacks.

## Error Handling

### Backend Errors
- Invalid permissions return 403 Forbidden
- Missing parameters return 400 Bad Request
- Database errors return 500 Internal Server Error

### Frontend Errors
- Network errors show user-friendly messages
- Empty results show helpful suggestions
- Loading states prevent multiple requests

## Future Enhancements

### Vector Search
- Implement semantic search using MongoDB Atlas Vector Search
- Add embedding generation for documents
- Hybrid keyword + semantic ranking

### Advanced Filtering
- Date range filters
- File type filters
- Workspace-specific search
- Advanced query syntax

### Search Analytics
- Track search queries and results
- Popular search terms
- Search performance metrics
- User search patterns
