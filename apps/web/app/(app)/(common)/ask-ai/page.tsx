import { ChatForm } from "@/components/wrapper-screens/chat";
import { getTenantSubscription, getUserAccount } from "@/services";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

export default async function HomePage() {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;
  const userId = cookies().get("userId")?.value ?? session?.userId;
  if (!userId || !tenantId) {
    return redirect("/sign-in");
  }
  const [user, subscription] = await Promise.all([
    getUserAccount(userId),
    getTenantSubscription(tenantId, session.userId as string),
  ]);
  if (!subscription?.isActive) {
    return redirect("/dashboard");
  }
  return (
    <ChatForm
      userName={user?.name}
      userId={userId}
      tenantId={tenantId}
      chats={null}
    />
  );
}
