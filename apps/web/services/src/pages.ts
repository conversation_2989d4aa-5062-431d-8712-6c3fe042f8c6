import { apiUrl, fetchJson } from "..";

export const createPage = async (data, tenantId, userId) => {
  try {
    const response = await fetch(`${apiUrl}/pages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create page api calling error": error });
    return { error: "Error Creating The Page" };
  }
};

export const updatePage = async (data, tenantId, userId) => {
  try {
    // Use the new API endpoint for removing sync
    if (data.gDriveFolderId === null || data.oneDriveFolderId === null) {
      const response = await fetch(`${apiUrl}/pages/update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userId,
          "x-tenant-id": tenantId,
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Network response was not ok");
      }
      return await response.json();
    }

    // Use the existing endpoint for other updates
    const response = await fetch(`${apiUrl}/pages`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Update page api calling error": error });
    return { error: "Error Update The Page" };
  }
};

export const deletePage = async (pageId, tenantId, userId) => {
  try {
    const url = `${apiUrl}/pages?id=${pageId}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete page api error": error });
    return { error: error.message || "Error deleting page" };
  }
};

export const getPage = async ({
  workspaceSlug,
  tenantId,
  userId,
  pageId = null,
}) => {
  if (pageId) {
    const response = await fetchJson(
      `${apiUrl}/pages?workspaceSlug=${workspaceSlug}&pageId=${pageId}`,
      userId,
      tenantId
    );
    return response;
  }
  const response = await fetchJson(
    `${apiUrl}/pages?workspaceSlug=${workspaceSlug}`,
    userId,
    tenantId
  );
  return response;
};
