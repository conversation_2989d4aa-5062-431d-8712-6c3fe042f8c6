import { NextResponse } from "next/server";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const userId = url.searchParams.get("userId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "TenantId is required" },
        { status: 400 }
      );
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: userId,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Fetch all pending invitations for the tenant
    const invitations = await db.invitation.findMany({
      where: {
        tenantId,
        accepted: false,
        expires: { gt: new Date() },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ data: invitations });
  } catch (error) {
    console.error("Get invitations error:", error);
    return NextResponse.json(
      { error: "Failed to fetch invitations" },
      { status: 500 }
    );
  }
}
