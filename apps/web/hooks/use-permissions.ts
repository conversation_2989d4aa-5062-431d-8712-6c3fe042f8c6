"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { getCookie } from "@/utils/cookies";

interface Permission {
  action: string;
  resource: string;
}

/**
 * Hook to check if the current user has a specific permission
 * @returns An object with functions to check permissions
 */
export function usePermissions() {
  const { data: session }: { data: any } = useSession();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!session?.user) {
        setLoading(false);
        return;
      }

      try {
        const tenantId = getCookie("currentOrganizationId");
        if (!tenantId) {
          setError("No organization selected");
          setLoading(false);
          return;
        }

        // Include userId in the request for server-side compatibility
        const userId = session.userId || session.user?.id;
        const response = await fetch(
          `/api/permissions/user?tenantId=${tenantId}&userId=${userId}`
        );
        if (!response.ok) {
          throw new Error("Failed to fetch permissions");
        }

        const data = await response.json();
        setPermissions(data.permissions || []);
      } catch (err) {
        console.error("Error fetching permissions:", err);
        setError(err.message || "Failed to fetch permissions");
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [session]);

  /**
   * Check if the user has a specific permission
   * @param action - The action to check (CREATE, READ, UPDATE, DELETE)
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user has the permission
   */
  const hasPermission = (action: string, resource: string): boolean => {
    if (loading || error) return false;

    // Check if the user is an owner or admin (from session)
    const userRole = session?.user?.role;
    if (userRole === "OWNER" || userRole === "ADMIN") {
      // Owners have all permissions
      // Admins have all permissions except DELETE WORKSPACE
      if (userRole === "OWNER") {
        return true;
      }

      // Admin restrictions
      if (action === "DELETE" && resource === "WORKSPACE") {
        return false;
      }

      return true;
    }

    // Check if the user has the specific permission
    return permissions.some(
      (p) => p.action === action && p.resource === resource
    );
  };

  /**
   * Check if the user can create a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can create the resource
   */
  const canCreate = (resource: string): boolean => {
    return hasPermission("CREATE", resource);
  };

  /**
   * Check if the user can read a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can read the resource
   */
  const canRead = (resource: string): boolean => {
    return hasPermission("READ", resource);
  };

  /**
   * Check if the user can update a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can update the resource
   */
  const canUpdate = (resource: string): boolean => {
    return hasPermission("UPDATE", resource);
  };

  /**
   * Check if the user can delete a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can delete the resource
   */
  const canDelete = (resource: string): boolean => {
    return hasPermission("DELETE", resource);
  };

  return {
    loading,
    error,
    permissions,
    hasPermission,
    canCreate,
    canRead,
    canUpdate,
    canDelete,
  };
}
