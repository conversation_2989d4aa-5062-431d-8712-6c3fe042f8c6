import { useState } from "react";
import { toast } from "react-hot-toast";
import { Message } from "../types";
import { useLanguage } from "@/lib/language-context";
import { createMessage, updateMessage } from "@/services/src/message";
import {
  createTemporaryMessage,
  addTemporaryMessage,
  updateStreamingMessage,
} from "../utils/message-utils";

export const useMessageRegeneration = (
  messages: Message[],
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  chatId: string | null,
  userId: string,
  tenantId: string,
  userName: string,
  headers: Record<string, string>,
  updateDisplayIndex: (
    messageId: string,
    newIndex: number,
    isManuallySet: boolean
  ) => void
) => {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const API_BASE_URL =
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
  const API_VERSION = "/api/v1";

  const handleRegenerate = async () => {
    if (!chatId || messages.length < 2) return;

    try {
      // Find the last user message
      let lastUserMessageIndex = messages.length - 1;
      while (
        lastUserMessageIndex >= 0 &&
        messages[lastUserMessageIndex].role !== "user"
      ) {
        lastUserMessageIndex--;
      }

      if (lastUserMessageIndex < 0) return;

      // Get the last user message content
      const lastUserMessage = messages[lastUserMessageIndex].content;

      // Find the last assistant message
      let lastAssistantIndex = messages.length - 1;
      if (messages[lastAssistantIndex].role !== "assistant") {
        // If the last message is not from assistant, find the last assistant message
        while (
          lastAssistantIndex >= 0 &&
          messages[lastAssistantIndex].role !== "assistant"
        ) {
          lastAssistantIndex--;
        }
      }

      if (lastAssistantIndex < 0) return;

      // Get the assistant message that will be regenerated
      const assistantMessage = messages[lastAssistantIndex];

      // Show toast notification
      setIsLoading(true);
      toast.loading(t("chat.regenerating"));

      // Make a direct API call to get a new response
      try {
        const apiUrl = `${API_BASE_URL}${API_VERSION}/workspace-chat/chat?current_user=${userId}&tenant_id=${tenantId}&user_name=${userName}`;
        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...headers,
          },
          body: JSON.stringify({
            question: lastUserMessage,
            stream: true,
            config: {},
            previous_message: null, // Don't include previous context for regeneration
          }),
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.statusText}`);
        }

        // Set up streaming response handling
        const reader = response.body?.getReader();
        let streamedContent = "";
        let regeneratedSources: any[] = [];
        toast.remove();

        // Create a temporary message for streaming
        const tempMessage = createTemporaryMessage(assistantMessage);

        // Add the temporary message to the UI as a nested regenerated message
        setMessages((prevMessages) =>
          addTemporaryMessage(prevMessages, assistantMessage, tempMessage)
        );

        // Update the display index to show the streaming message
        if (assistantMessage.id) {
          // Count existing regenerated messages and add 1 for the new temporary message
          const regeneratedCount =
            (assistantMessage.regeneratedMessages?.length || 0) + 1;

          // Use our combined update function to update the display index
          updateDisplayIndex(
            assistantMessage.id,
            regeneratedCount,
            false // Not manually set
          );
        }

        // Try to parse sources from the first chunk
        const parseSourcesFromRegeneratedResponse = async (
          reader: ReadableStreamDefaultReader<Uint8Array>
        ) => {
          try {
            const { value } = await reader.read();
            const firstChunk = new TextDecoder().decode(value);

            try {
              // Try to parse the first chunk as JSON to extract sources
              const parsedChunk = JSON.parse(firstChunk);

              // Check if this is the first chunk with the marker
              if (parsedChunk.is_first_chunk && parsedChunk.sources) {
                regeneratedSources = parsedChunk.sources;
                // Return the answer part for further processing
                return {
                  initialContent: parsedChunk.answer || "",
                  sources: regeneratedSources,
                  reader,
                };
              }

              // Check for sources in the standard format as a fallback
              if (parsedChunk.sources) {
                regeneratedSources = parsedChunk.sources;
                // Return only the answer part for further processing
                return {
                  initialContent: parsedChunk.answer || "",
                  sources: regeneratedSources,
                  reader,
                };
              }
            } catch (e) {
              // If not JSON, log the error and use the chunk as plain text
              console.error("First regenerated chunk is not valid JSON:", e);
              console.log("First regenerated chunk content:", firstChunk);
              console.log("Using regenerated chunk as plain text");
            }

            return {
              initialContent: firstChunk,
              sources: [],
              reader,
            };
          } catch (e) {
            console.error(
              "Error parsing sources from regenerated response:",
              e
            );
            return {
              initialContent: "",
              sources: [],
              reader,
            };
          }
        };

        if (reader) {
          try {
            // Try to parse sources from the first chunk
            const {
              initialContent,
              sources: parsedSources,
              reader: updatedReader,
            } = await parseSourcesFromRegeneratedResponse(reader);
            streamedContent = initialContent;
            regeneratedSources = parsedSources;

            // Update the temporary message with the streaming content
            setMessages((prevMessages) =>
              updateStreamingMessage(
                prevMessages,
                assistantMessage,
                streamedContent,
                regeneratedSources
              )
            );

            // Continue reading the stream
            // eslint-disable-next-line no-constant-condition
            while (true) {
              const { done, value } = await updatedReader.read();
              if (done) break;

              // Convert the chunk to text and accumulate
              const chunk = new TextDecoder().decode(value);

              // Try to parse the chunk as JSON to extract sources
              try {
                const parsedChunk = JSON.parse(chunk);
                if (parsedChunk.sources) {
                  // If this is a sources chunk, store the sources but don't add to message content
                  regeneratedSources = parsedChunk.sources;
                  // Skip adding this chunk to the message content
                  continue;
                }
              } catch (e) {
                // Not JSON, treat as normal text
              }

              streamedContent += chunk;

              // Clean up the message content
              let cleanedContent = streamedContent;
              if (cleanedContent.startsWith("Answer:")) {
                cleanedContent = cleanedContent.replace("Answer:", "").trim();
              }

              // Update the temporary message with the streaming content
              setMessages((prevMessages) =>
                updateStreamingMessage(
                  prevMessages,
                  assistantMessage,
                  cleanedContent,
                  regeneratedSources
                )
              );
            }
          } catch (error) {
            console.error("Error reading stream:", error);
          } finally {
            reader.releaseLock();

            // Clean up the final content
            let finalContent = streamedContent;
            if (finalContent.startsWith("Answer:")) {
              finalContent = finalContent.replace("Answer:", "").trim();
            }

            if (finalContent.startsWith("ERROR:")) {
              finalContent =
                "Error: " + finalContent.replace("ERROR:", "").trim();
            }

            // Create a new message in the database for the regenerated response
            if (chatId && assistantMessage.id) {
              try {
                // Create a new message with a reference to the original
                const newMessage = await createMessage({
                  chatId,
                  userId,
                  content: finalContent,
                  role: "assistant",
                  metadata: {
                    regeneratedResponse: true,
                    isSelected: true,
                  },
                  originalMessageId: assistantMessage.id,
                  sources: regeneratedSources,
                });

                if (newMessage && !newMessage.error) {
                  // Update the original message to mark it as having regenerated versions
                  await updateMessage({
                    chatId,
                    messageId: assistantMessage.id,
                    metadata: {
                      ...assistantMessage.metadata,
                      originalResponse: true,
                    },
                  });

                  // Update the UI with the final regenerated message
                  setMessages((prevMessages) => {
                    return prevMessages.map((msg) => {
                      // Find the original message
                      if (
                        msg.id === assistantMessage.id &&
                        msg.regeneratedMessages
                      ) {
                        // Find and update the temporary message in the regeneratedMessages array
                        const updatedRegeneratedMessages =
                          msg.regeneratedMessages.map((regMsg) => {
                            // Find the temporary message by checking for missing ID
                            if (
                              !regMsg.id &&
                              regMsg.metadata?.regeneratedResponse
                            ) {
                              // Update the temporary message with the final ID and content
                              return {
                                ...regMsg,
                                id: newMessage.id,
                                content: finalContent,
                                sources: regeneratedSources,
                              };
                            }
                            return regMsg;
                          });

                        // Return the updated original message with streaming flag removed
                        return {
                          ...msg,
                          metadata: {
                            ...msg.metadata,
                            isStreaming: false, // Remove streaming flag
                          },
                          regeneratedMessages: updatedRegeneratedMessages,
                        };
                      }
                      return msg;
                    });
                  });

                  // Update the display index to show the newest regenerated message
                  if (assistantMessage.id) {
                    // Count how many regenerated messages this message has now
                    const regeneratedCount =
                      messages.filter(
                        (msg) => msg.originalMessageId === assistantMessage.id
                      ).length + 1; // +1 for the new message we just added

                    // Use our combined update function to update the display index
                    updateDisplayIndex(
                      assistantMessage.id,
                      regeneratedCount,
                      false // Not manually set
                    );
                  }
                }
              } catch (error) {
                console.error("Error saving regenerated message:", error);
              }
            }
          }
        }
        setIsLoading(false);
      } catch (apiError) {
        console.error("API error during regeneration:", apiError);
        toast.error(t("chat.regenerateError"));

        // Remove the temporary message from the regeneratedMessages array if there was an error
        setMessages((prevMessages) => {
          return prevMessages.map((msg) => {
            if (msg.id === assistantMessage.id && msg.regeneratedMessages) {
              // Filter out the temporary message from regeneratedMessages
              const filteredRegeneratedMessages =
                msg.regeneratedMessages.filter(
                  (regMsg) =>
                    !(regMsg.metadata?.regeneratedResponse && !regMsg.id)
                );

              // Return the updated original message with streaming flag removed
              return {
                ...msg,
                metadata: {
                  ...msg.metadata,
                  isStreaming: false, // Remove streaming flag
                },
                regeneratedMessages: filteredRegeneratedMessages,
              };
            }
            return msg;
          });
        });
      }
    } catch (error) {
      console.error("Error regenerating response:", error);
      toast.error(t("chat.regenerateError"));
    }
  };

  return {
    handleRegenerate,
    isLoading,
  };
};
