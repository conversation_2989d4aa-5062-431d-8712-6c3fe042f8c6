import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth";

// GET /api/embedded-settings
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get("tenantId");
    const id = searchParams.get("id");
    const rawSlug = searchParams.get("slug") ?? "";
    const slug = decodeURI(rawSlug);

    if (!tenantId) {
      return NextResponse.json(
        { error: "Missing tenantId parameter" },
        { status: 400 }
      );
    }

    if (id) {
      const settings = await db.embeddedSettings.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      return NextResponse.json({ settings }, { status: 200 });
    }

    if (slug) {
      const workspace = await db.workspace.findFirst({
        where: {
          slug,
          tenantId,
        },
      });
      const settings = await db.embeddedSettings.findFirst({
        where: {
          workspaceId: workspace?.id,
          tenantId,
        },
      });

      return NextResponse.json({ settings }, { status: 200 });
    }

    const settings = await db.embeddedSettings.findMany({
      where: {
        tenantId: tenantId,
      },
    });

    return NextResponse.json({ settings }, { status: 200 });
  } catch (error) {
    console.error("Error fetching embedded settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// POST /api/embedded-settings
export async function POST(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const data = await req.json();
    const settings = await db.embeddedSettings.create({
      data: {
        ...data,
      },
    });

    return NextResponse.json({ settings }, { status: 201 });
  } catch (error) {
    console.error("Error creating embedded settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// PUT /api/embedded-settings
export async function PUT(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const data = await req.json();
    const settings = await db.embeddedSettings.update({
      where: {
        id: data.id,
      },
      data,
    });

    return NextResponse.json({ settings }, { status: 200 });
  } catch (error) {
    console.error("Error updating embedded settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// DELETE /api/embedded-settings
export async function DELETE(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Missing id parameter" },
        { status: 400 }
      );
    }

    await db.embeddedSettings.delete({
      where: {
        id,
      },
    });

    return NextResponse.json(
      { message: "Settings deleted successfully" },
      { status: 204 }
    );
  } catch (error) {
    console.error("Error deleting embedded settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
