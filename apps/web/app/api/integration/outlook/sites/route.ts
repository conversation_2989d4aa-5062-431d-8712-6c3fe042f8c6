import { NextResponse } from "next/server";
import { getAuthenticatedClient } from "@/lib/microsoft-graph";

// List all available SharePoint sites
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const userId = url.searchParams.get("userId") ?? "";

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const client = await getAuthenticatedClient(tenantId, userId);

    // Get all sites the user has access to
    const response = await client
      .api("/sites?search=*")
      .select("id,name,displayName,webUrl,siteCollection")
      .get();

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error("Error listing SharePoint sites:", error);
    return NextResponse.json(
      { error: "Failed to list SharePoint sites" },
      { status: 500 }
    );
  }
}
