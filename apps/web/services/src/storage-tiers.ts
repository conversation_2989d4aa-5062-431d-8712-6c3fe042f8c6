import axios from "axios";
import { apiUrl } from "..";

export interface StorageTier {
  id: string;
  name: string; // e.g., "10GB", "50GB", "100GB"
  sizeGB: number; // Size in GB
  price: number; // Price for display purposes
  stripePriceId: string; // Monthly price ID
  stripeYearlyPriceId: string; // Yearly price ID
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StorageTiersResponse {
  storageTiers: StorageTier[];
}

export interface StorageTierResponse {
  storageTier: StorageTier;
}

// Get all active storage tiers
export const getStorageTiers = async (): Promise<StorageTier[]> => {
  try {
    const response = await axios.get<StorageTiersResponse>(`${apiUrl}/storage-tiers`);
    return response.data.storageTiers;
  } catch (error) {
    console.error("Error fetching storage tiers:", error);
    throw error;
  }
};

// Get a specific storage tier by ID
export const getStorageTier = async (tierId: string): Promise<StorageTier> => {
  try {
    const response = await axios.get<StorageTierResponse>(`${apiUrl}/storage-tiers/${tierId}`);
    return response.data.storageTier;
  } catch (error) {
    console.error(`Error fetching storage tier ${tierId}:`, error);
    throw error;
  }
};

// Get a storage tier by size
export const getStorageTierBySize = async (sizeGB: number): Promise<StorageTier | null> => {
  try {
    const tiers = await getStorageTiers();
    
    // Find the appropriate tier based on size
    if (sizeGB <= 10) {
      return tiers.find(tier => tier.name === "10GB") || null;
    } else if (sizeGB <= 50) {
      return tiers.find(tier => tier.name === "50GB") || null;
    } else {
      return tiers.find(tier => tier.name === "100GB") || null;
    }
  } catch (error) {
    console.error(`Error finding storage tier for size ${sizeGB}GB:`, error);
    throw error;
  }
};
