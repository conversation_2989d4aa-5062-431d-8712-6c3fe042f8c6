"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { outlookDriveService } from "@/services/src/integration/microsoft";
import { Folder, ChevronRight, Loader2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { getCookie } from "@/utils/cookies";

interface SharePointFolderSelectorProps {
  tenantId: string;
  siteId: string;
  driveId?: string;
  selectedFolderName?: string;
  onFolderSelect: (
    folderId: string,
    folderName: string,
    driveId: string
  ) => void;
  selectedFolderId?: string;
}

interface FolderItem {
  id: string;
  name: string;
  folder: any;
  parentReference?: {
    driveId?: string;
  };
}

export function SharePointFolderSelector({
  tenantId,
  siteId,
  driveId,
  onFolderSelect,
  selectedFolderId,
  selectedFolderName,
}: SharePointFolderSelectorProps) {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string | undefined>(
    undefined
  );
  const [currentDriveId, setCurrentDriveId] = useState<string | undefined>(
    driveId
  );
  const [folderPath, setFolderPath] = useState<
    { id: string; name: string; driveId?: string }[]
  >([
    {
      id: "root",
      name: t("integration.root") || "Root",
      driveId: currentDriveId,
    },
  ]);
  const [selectedFolder, setSelectedFolder] = useState<{
    id: string;
    name: string;
    driveId?: string;
  } | null>(null);
  const userId = getCookie("userId") ?? "";

  // Load folders when dialog opens or folder changes
  useEffect(() => {
    if (isOpen && siteId) {
      loadFolders(currentFolderId);
    }
  }, [isOpen, currentFolderId, siteId]);

  // Get default drive ID if not provided
  useEffect(() => {
    if (siteId && !driveId && !currentDriveId) {
      getDefaultDriveId();
    }
  }, [siteId, driveId]);

  // Reset state when siteId changes
  useEffect(() => {
    // Reset folder path and current folder ID when site changes
    setCurrentFolderId(undefined);
    setFolders([]);
    setSelectedFolder(null);
    setFolderPath([
      {
        id: "root",
        name: t("integration.root") || "Root",
        driveId: undefined,
      },
    ]);
    setCurrentDriveId(driveId);

    // If dialog is open, load folders for the new site
    if (isOpen && siteId) {
      getDefaultDriveId();
    }
  }, [siteId, driveId, isOpen, t]);

  const getDefaultDriveId = async () => {
    setIsLoading(true);
    try {
      const result = await outlookDriveService.listSiteFolders({
        tenantId,
        siteId,
        folderId: "root",
        userId,
      });

      if (result.parentReference?.driveId) {
        setCurrentDriveId(result.parentReference.driveId);
        setFolderPath([
          {
            id: "root",
            name: t("integration.root") || "Root",
            driveId: result.parentReference.driveId,
          },
        ]);

        // Load root folders with the new drive ID
        await loadFolders(undefined);
      }
    } catch (error) {
      console.error("Error getting default drive ID:", error);
      setFolders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadFolders = async (folderId?: string) => {
    setIsLoading(true);
    try {
      const result = await outlookDriveService.listSiteFolders({
        tenantId,
        siteId,
        driveId: currentDriveId,
        folderId,
        userId,
      });

      if (result.value) {
        // Filter to only include folders
        const folderItems = result.value.filter(
          (item: FolderItem) => item.folder
        );
        setFolders(folderItems);

        // Update current drive ID if it's in the response
        if (folderItems.length > 0 && folderItems[0].parentReference?.driveId) {
          setCurrentDriveId(folderItems[0].parentReference.driveId);
        }
      } else {
        setFolders([]);
      }
    } catch (error) {
      console.error("Error loading folders:", error);
      setFolders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFolderClick = (folder: FolderItem) => {
    setCurrentFolderId(folder.id);
    setFolderPath([
      ...folderPath,
      {
        id: folder.id,
        name: folder.name,
        driveId: folder.parentReference?.driveId || currentDriveId,
      },
    ]);
  };

  const handleBreadcrumbClick = (index: number) => {
    const newPath = folderPath.slice(0, index + 1);
    setFolderPath(newPath);
    setCurrentFolderId(newPath[newPath.length - 1].id);
    setCurrentDriveId(newPath[newPath.length - 1].driveId);
  };

  const handleSelectFolder = () => {
    if (currentFolderId && currentDriveId) {
      const currentFolder = folderPath[folderPath.length - 1];
      setSelectedFolder({
        id: currentFolder.id,
        name: currentFolder.name,
        driveId: currentDriveId,
      });
      onFolderSelect(currentFolder.id, currentFolder.name, currentDriveId);
      setIsOpen(false);
    }
  };

  return (
    <div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-full justify-start gap-2">
            <Folder className="h-4 w-4" />
            {selectedFolder?.name || selectedFolderId || selectedFolderName ? (
              <div className="truncate">
                <span className="font-medium">
                  {selectedFolder?.name ||
                    selectedFolderName ||
                    t("integration.selectedFolder") ||
                    "Selected Folder"}
                </span>
              </div>
            ) : (
              t("integration.selectFolder") || "Select SharePoint Folder"
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>
              {t("integration.selectSharePointFolder") ||
                "Select SharePoint Folder"}
            </DialogTitle>
          </DialogHeader>

          {/* Breadcrumb navigation */}
          <div className="flex flex-wrap items-center gap-1 text-sm">
            {folderPath.map((folder, index) => (
              <div key={folder.id} className="flex items-center">
                {index > 0 && <ChevronRight className="h-3 w-3 mx-1" />}
                <button
                  className="hover:underline"
                  onClick={() => handleBreadcrumbClick(index)}
                >
                  {folder.name}
                </button>
              </div>
            ))}
          </div>

          {/* Folder list */}
          <div className="max-h-[300px] overflow-y-auto border rounded-md">
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-5 w-5 animate-spin" />
              </div>
            ) : folders.length === 0 ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                {t("integration.noFoldersFound") || "No folders found"}
              </div>
            ) : (
              <div className="divide-y">
                {folders.map((folder) => (
                  <button
                    key={folder.id}
                    className="flex w-full items-center gap-2 p-2 hover:bg-muted text-left"
                    onClick={() => handleFolderClick(folder)}
                  >
                    <Folder className="h-4 w-4" />
                    <span>{folder.name}</span>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              {t("common.cancel") || "Cancel"}
            </Button>
            <Button onClick={handleSelectFolder} disabled={!currentFolderId}>
              {t("common.select") || "Select"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
