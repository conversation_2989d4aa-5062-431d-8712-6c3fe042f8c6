"use client";

import { useState, useEffect } from "react";
import { Loader2, Users, Database, Plus, Minus, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { updateSubscriptionAddons } from "@/services";
import { SubscriptionWithPlan } from "@/services/src/subscriptions";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { SubscriptionUpdateConfirmationDialog } from "./subscription-update-confirmation-dialog";

// Storage addon type is defined inline with the constants

interface ManageSubscriptionAddonsProps {
  subscription: SubscriptionWithPlan;
  tenantId: string;
  stripePrices: any;
  usageSummary?: {
    currentUsage: number;
    totalUsage: number;
    limit: number;
  };
}

export function ManageSubscriptionAddons({
  subscription,
  tenantId,
  usageSummary,
  stripePrices,
}: ManageSubscriptionAddonsProps) {
  // Define the storage addon type
  type StorageAddon = {
    id: string;
    size: number;
    price: number;
    stripePriceId?: string;
    stripeYearlyPriceId?: string;
  };

  // Define storage add-on options based on the pricing model
  const MONTHLY_STORAGE_ADDONS: StorageAddon[] = [
    {
      id: "10gb",
      size: 10,
      price:
        stripePrices?.storageTiers?.find(
          (tier: any) => tier.tierName === "10GB"
        )?.monthlyPrice?.unitAmount / 100,
      stripePriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "10GB"
      )?.monthlyPrice?.id,
      stripeYearlyPriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "10GB"
      )?.yearlyPrice?.id,
    },
    {
      id: "50gb",
      size: 50,
      price:
        stripePrices?.storageTiers?.find(
          (tier: any) => tier.tierName === "50GB"
        )?.monthlyPrice?.unitAmount / 100,
      stripePriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "50GB"
      )?.monthlyPrice?.id,
      stripeYearlyPriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "50GB"
      )?.yearlyPrice?.id,
    },
    {
      id: "100gb",
      size: 100,
      price:
        stripePrices?.storageTiers?.find(
          (tier: any) => tier.tierName === "100GB"
        )?.monthlyPrice?.unitAmount / 100,
      stripePriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "100GB"
      )?.monthlyPrice?.id,
      stripeYearlyPriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "100GB"
      )?.yearlyPrice?.id,
    },
  ];

  // Define yearly storage add-on options
  const YEARLY_STORAGE_ADDONS: StorageAddon[] = [
    {
      id: "10gb",
      size: 10,
      price:
        stripePrices?.storageTiers?.find(
          (tier: any) => tier.tierName === "10GB"
        )?.yearlyPrice?.unitAmount / 100,
      stripePriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "10GB"
      )?.monthlyPrice?.id,
      stripeYearlyPriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "10GB"
      )?.yearlyPrice?.id,
    },
    {
      id: "50gb",
      size: 50,
      price:
        stripePrices?.storageTiers?.find(
          (tier: any) => tier.tierName === "50GB"
        )?.yearlyPrice?.unitAmount / 100,
      stripePriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "50GB"
      )?.monthlyPrice?.id,
      stripeYearlyPriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "50GB"
      )?.yearlyPrice?.id,
    },
    {
      id: "100gb",
      size: 100,
      price:
        stripePrices?.storageTiers?.find(
          (tier: any) => tier.tierName === "100GB"
        )?.yearlyPrice?.unitAmount / 100,
      stripePriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "100GB"
      )?.monthlyPrice?.id,
      stripeYearlyPriceId: stripePrices?.storageTiers?.find(
        (tier: any) => tier.tierName === "100GB"
      )?.yearlyPrice?.id,
    },
  ];

  // User management state
  const [additionalUsers, setAdditionalUsers] = useState(
    subscription.additionalUsers
  );

  const additionalStorage =
    subscription?.storageTierItems?.reduce(
      (total, item) => total + item.size * item.quantity,
      0
    ) || 0;

  // Storage management state
  const [additionalStorageGB, setAdditionalStorageGB] = useState(
    additionalStorage || 0
  );

  const [loading, setLoading] = useState(false);
  const plan = subscription.plan;
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState("users");

  // Confirmation dialog state
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogType, setConfirmDialogType] = useState<
    "users" | "storage"
  >("users");

  // Determine if the subscription is yearly or monthly
  const isYearlyBilling: boolean =
    subscription.billingInterval === "year" ||
    (subscription.stripeSubscriptionId &&
      subscription.stripeSubscriptionId.includes("year")) ||
    false;

  // Select the appropriate storage add-ons based on billing interval
  const STORAGE_ADDONS = isYearlyBilling
    ? YEARLY_STORAGE_ADDONS
    : MONTHLY_STORAGE_ADDONS;

  // isYearlyBilling determines which pricing to use

  // User management functions
  const incrementUsers = () => {
    setAdditionalUsers((prev) => prev + 1);
  };

  const decrementUsers = () => {
    setAdditionalUsers((prev) => (prev > 0 ? prev - 1 : 0));
  };

  const handleUserInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setAdditionalUsers(value);
    } else if (e.target.value === "") {
      setAdditionalUsers(0);
    }
  };

  // Define a type for storage tiers
  type StorageTier = {
    id: string;
    size: number;
    quantity: number;
    price: number;
  };

  // Track the storage tiers that the user has added
  const [storageTiers, setStorageTiers] = useState<StorageTier[]>([]);

  // Initialize storage tiers from the subscription
  useEffect(() => {
    // If we have storageTierItems in the subscription, use them
    if (
      subscription.storageTierItems &&
      subscription.storageTierItems.length > 0
    ) {
      // Convert the subscription storage tiers to our local format
      const initialTiers: StorageTier[] = subscription.storageTierItems.map(
        (item) => ({
          id: item.id,
          size: item.size,
          quantity: item.quantity,
          price: item.price,
        })
      );
      setStorageTiers(initialTiers);
    } else {
      // Fallback to calculating from additionalStorageGB for backward compatibility
      let remainingStorage = additionalStorage || 0;
      const initialTiers: StorageTier[] = [];

      // First try to add 100GB tiers
      const tier100 = STORAGE_ADDONS.find((addon) => addon.size === 100);
      if (tier100 && remainingStorage >= 100) {
        const count = Math.floor(remainingStorage / 100);
        initialTiers.push({
          id: tier100.id,
          size: tier100.size,
          quantity: count,
          price: tier100.price,
        });
        remainingStorage -= count * 100;
      }

      // Then try to add 50GB tiers
      const tier50 = STORAGE_ADDONS.find((addon) => addon.size === 50);
      if (tier50 && remainingStorage >= 50) {
        const count = Math.floor(remainingStorage / 50);
        initialTiers.push({
          id: tier50.id,
          size: tier50.size,
          quantity: count,
          price: tier50.price,
        });
        remainingStorage -= count * 50;
      }

      // Finally add 10GB tiers
      const tier10 = STORAGE_ADDONS.find((addon) => addon.size === 10);
      if (tier10 && remainingStorage >= 10) {
        const count = Math.floor(remainingStorage / 10);
        initialTiers.push({
          id: tier10.id,
          size: tier10.size,
          quantity: count,
          price: tier10.price,
        });
        remainingStorage -= count * 10;
      }

      // If there's still remaining storage, add one more 10GB tier
      if (remainingStorage > 0 && tier10) {
        initialTiers.push({
          id: tier10.id,
          size: tier10.size,
          quantity: 1,
          price: tier10.price,
        });
      }

      setStorageTiers(initialTiers);
    }
  }, [additionalStorage, subscription.storageTierItems]);

  // Calculate total additional storage from tiers
  const calculateTotalAdditionalStorage = () => {
    return storageTiers.reduce(
      (total, tier) => total + tier.size * tier.quantity,
      0
    );
  };

  // Update additionalStorageGB when storageTiers change
  useEffect(() => {
    setAdditionalStorageGB(calculateTotalAdditionalStorage());
  }, [storageTiers]);

  // Get the storage addon for a specific size
  const getStorageAddon = (size: number) => {
    const addon = STORAGE_ADDONS.find((addon) => addon.size === size);
    return addon || null;
  };

  // We already have calculateTotalAdditionalStorage defined above

  // Define the available storage increments
  const STORAGE_INCREMENTS = [10, 50, 100];

  // Track which storage increment is currently selected
  const [selectedStorageIncrement, setSelectedStorageIncrement] = useState(10);

  const incrementStorage = () => {
    // Find the storage addon for the selected increment
    const addon = getStorageAddon(selectedStorageIncrement);
    if (!addon) return;

    // Check if we already have this tier
    const existingTierIndex = storageTiers.findIndex(
      (tier) => tier.size === selectedStorageIncrement
    );

    if (existingTierIndex >= 0) {
      // If we already have this tier, increment its quantity
      const updatedTiers = [...storageTiers];
      updatedTiers[existingTierIndex].quantity += 1;
      setStorageTiers(updatedTiers);
    } else {
      // Otherwise, add a new tier
      setStorageTiers([
        ...storageTiers,
        {
          id: addon.id,
          size: addon.size,
          quantity: 1,
          price: addon.price,
        },
      ]);
    }
  };

  // Function to reset the additional storage to the current value
  const resetStorage = () => {
    // Reset to the initial state based on subscription
    let remainingStorage = additionalStorage || 0;
    const initialTiers: StorageTier[] = [];

    // First try to add 100GB tiers
    const tier100 = STORAGE_ADDONS.find((addon) => addon.size === 100);
    if (tier100 && remainingStorage >= 100) {
      const count = Math.floor(remainingStorage / 100);
      initialTiers.push({
        id: tier100.id,
        size: tier100.size,
        quantity: count,
        price: tier100.price,
      });
      remainingStorage -= count * 100;
    }

    // Then try to add 50GB tiers
    const tier50 = STORAGE_ADDONS.find((addon) => addon.size === 50);
    if (tier50 && remainingStorage >= 50) {
      const count = Math.floor(remainingStorage / 50);
      initialTiers.push({
        id: tier50.id,
        size: tier50.size,
        quantity: count,
        price: tier50.price,
      });
      remainingStorage -= count * 50;
    }

    // Finally add 10GB tiers
    const tier10 = STORAGE_ADDONS.find((addon) => addon.size === 10);
    if (tier10 && remainingStorage >= 10) {
      const count = Math.floor(remainingStorage / 10);
      initialTiers.push({
        id: tier10.id,
        size: tier10.size,
        quantity: count,
        price: tier10.price,
      });
      remainingStorage -= count * 10;
    }

    // If there's still remaining storage, add one more 10GB tier
    if (remainingStorage > 0 && tier10) {
      initialTiers.push({
        id: tier10.id,
        size: tier10.size,
        quantity: 1,
        price: tier10.price,
      });
    }

    setStorageTiers(initialTiers);
  };

  // Handle subscription update for users
  const handleUpdateUsers = () => {
    if (!tenantId) return;

    // Check if anything has changed
    const hasUserChanges = additionalUsers !== subscription.additionalUsers;

    if (!hasUserChanges) {
      toast.error(t("billing.noChangesToUpdate"));
      return;
    }

    // Show confirmation dialog
    setConfirmDialogType("users");
    setShowConfirmDialog(true);
  };

  // Execute the actual user update after confirmation
  const executeUserUpdate = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);

      // Use the direct update API with updateType=users
      const result = await updateSubscriptionAddons({
        tenantId,
        additionalUsers,
        updateType: "users",
        billingInterval: isYearlyBilling ? "year" : "month",
        stripeUserPriceId: isYearlyBilling
          ? stripePrices?.plans?.find((p: any) => p.planId === plan.id)
              ?.userYearlyPrice?.id ||
            plan.stripeUserYearlyPriceId ||
            undefined
          : stripePrices?.plans?.find((p: any) => p.planId === plan.id)
              ?.userMonthlyPrice?.id ||
            plan.stripeUserPriceId ||
            undefined,
      });

      // Users subscription updated successfully

      // Show success message
      toast.success(t("billing.usersUpdateSuccess"));

      // If we have a subscription in the result, update the local state
      if (result.subscription) {
        setAdditionalUsers(result.subscription.additionalUsers);
        // Reload the page after a short delay to show the updated subscription
        setTimeout(() => window.location.reload(), 1500);
      }

      // If we have a portal URL, redirect to Stripe customer portal
      // if (result.portalUrl) {
      //   window.location.href = result.portalUrl;
      // }
    } catch (error) {
      console.error("Error updating users subscription:", error);
      toast.error(t("billing.usersUpdateFailed"));
    } finally {
      setLoading(false);
      setShowConfirmDialog(false);
    }
  };

  // Handle subscription update for storage
  const handleUpdateStorage = () => {
    if (!tenantId) return;

    // Check if anything has changed
    const hasStorageChanges = additionalStorageGB !== additionalStorage;

    if (!hasStorageChanges) {
      toast.error(t("billing.noChangesToUpdate"));
      return;
    }

    // Make sure we have at least one storage tier
    if (storageTiers.length === 0 && additionalStorageGB > 0) {
      toast.error(t("billing.noStorageTiersSelected"));
      return;
    }

    // Show confirmation dialog
    setConfirmDialogType("storage");
    setShowConfirmDialog(true);
  };

  // Execute the actual storage update after confirmation
  const executeStorageUpdate = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);

      // When we send additionalStorageGB to the backend, it will be added to the existing storage
      // So we need to send only the difference, not the total
      const storageToAdd = additionalStorageGB - additionalStorage;

      // Use the direct update API with updateType=storage
      const result = await updateSubscriptionAddons({
        tenantId,
        additionalStorageGB: storageToAdd, // Send only the storage to add, not the total (for backward compatibility)
        updateType: "storage",
        billingInterval: isYearlyBilling ? "year" : "month",
        // Pass the storage tiers directly as an array of objects
        storageTierItems: storageTiers.map((tier) => ({
          id: tier.id,
          size: tier.size,
          quantity: tier.quantity,
          price: tier.price,
          // Add the appropriate Stripe price ID based on billing interval
          stripePriceId: isYearlyBilling
            ? STORAGE_ADDONS.find((addon) => addon.id === tier.id)
                ?.stripeYearlyPriceId
            : STORAGE_ADDONS.find((addon) => addon.id === tier.id)
                ?.stripePriceId,
        })),
        // Keep the JSON string for backward compatibility
        storageTiers: JSON.stringify(storageTiers),
      });

      // Storage subscription updated successfully

      // Show success message
      toast.success(t("billing.storageUpdateSuccess"));

      // If we have a subscription in the result, update the local state
      if (result.subscription) {
        setAdditionalStorageGB(result.subscription.additionalStorageGB || 0);

        // If we don't have a portal URL, the subscription was updated directly

        // Reload the page after a short delay to show the updated subscription
        setTimeout(() => window.location.reload(), 1500);
      }

      // If we have a portal URL, redirect to Stripe customer portal
      // if (result.portalUrl) {
      //   window.location.href = result.portalUrl;
      // }
    } catch (error) {
      console.error("Error updating storage subscription:", error);

      // Check if it's a Stripe error about duplicate price
      if (
        error.response?.data?.error &&
        error.response.data.error.includes(
          "can't be added to this Subscription because an existing Subscription Item"
        )
      ) {
        toast.error(t("billing.duplicateStorageTier"));
      } else {
        toast.error(t("billing.storageUpdateFailed"));
      }
    } finally {
      setLoading(false);
      setShowConfirmDialog(false);
    }
  };

  // Individual update handlers are used instead of a combined handler

  // Calculate costs for users
  const basePlanPrice = plan.price || 0;

  // Get the additional user price from Stripe prices if available
  const additionalUserFee = isYearlyBilling
    ? stripePrices?.plans?.find((p: any) => p.planId === plan.id)
        ?.userYearlyPrice?.unitAmount / 100 || plan.additionalUserFee * 10 // Fallback to 10x monthly price for yearly (16.7% discount)
    : stripePrices?.plans?.find((p: any) => p.planId === plan.id)
        ?.userMonthlyPrice?.unitAmount / 100 || plan.additionalUserFee;

  const additionalUserCost = additionalUsers * additionalUserFee;

  // Calculate costs for storage
  const basePlanStorage = plan.vectorStoreGB || 0;

  // Calculate the total cost of all storage tiers
  const additionalStorageCost = storageTiers.reduce((total, tier) => {
    const addon = getStorageAddon(tier.size);
    return total + (addon ? addon.price * tier.quantity : 0);
  }, 0);

  // Calculate total costs
  const totalCost = basePlanPrice + additionalUserCost + additionalStorageCost;

  // Billing period is handled directly in the JSX

  // Calculate differences for display
  const userDifference = additionalUsers - subscription.additionalUsers;
  // Only calculate cost difference for additional users being added
  const userCostDifference = Math.max(0, userDifference) * additionalUserFee;

  // Calculate the difference between the new total storage and the existing storage
  const storageDifference = Math.max(
    0,
    additionalStorageGB - additionalStorage
  );

  // Calculate the cost difference between the current storage tiers and the initial storage
  const initialStorageCost = (() => {
    // Calculate how many of each tier are needed to reach the initial additionalStorageGB
    let remainingStorage = additionalStorage;
    let cost = 0;

    // First try to add 100GB tiers
    const tier100 = STORAGE_ADDONS.find((addon) => addon.size === 100);
    if (tier100 && remainingStorage >= 100) {
      const count = Math.floor(remainingStorage / 100);
      cost += tier100.price * count;
      remainingStorage -= count * 100;
    }

    // Then try to add 50GB tiers
    const tier50 = STORAGE_ADDONS.find((addon) => addon.size === 50);
    if (tier50 && remainingStorage >= 50) {
      const count = Math.floor(remainingStorage / 50);
      cost += tier50.price * count;
      remainingStorage -= count * 50;
    }

    // Finally add 10GB tiers
    const tier10 = STORAGE_ADDONS.find((addon) => addon.size === 10);
    if (tier10 && remainingStorage >= 10) {
      const count = Math.floor(remainingStorage / 10);
      cost += tier10.price * count;
      remainingStorage -= count * 10;
    }

    // If there's still remaining storage, add one more 10GB tier
    if (remainingStorage > 0 && tier10) {
      cost += tier10.price;
    }

    return cost;
  })();

  const storageCostDifference = Math.max(
    0,
    additionalStorageCost - initialStorageCost
  );
  // Calculate total storage
  const totalStorage = basePlanStorage + additionalStorageGB;

  // Changes are checked in the individual update handlers

  // Handle dialog confirmation
  const handleConfirmUpdate = () => {
    if (confirmDialogType === "users") {
      executeUserUpdate();
    } else {
      executeStorageUpdate();
    }
  };

  // Handle dialog cancellation
  const handleCancelUpdate = () => {
    setShowConfirmDialog(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("billing.manageSubscriptionAddons")}</CardTitle>
        <CardDescription>
          {t("billing.adjustSubscriptionAddons")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="users"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="users">
              <Users className="mr-2 h-4 w-4" />
              {t("billing.users")}
            </TabsTrigger>
            <TabsTrigger value="storage">
              <Database className="mr-2 h-4 w-4" />
              {t("billing.storage")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="additionalUsers">
                {t("billing.additionalUsers")}
              </Label>
              <div className="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={decrementUsers}
                  disabled={additionalUsers === 0}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <Input
                  id="additionalUsers"
                  type="number"
                  min="0"
                  value={additionalUsers}
                  onChange={handleUserInputChange}
                  className="w-20 text-center"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={incrementUsers}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="rounded-md border p-4 space-y-2">
              <div className="flex justify-between">
                <span>
                  {t("billing.basePlan", { users: plan.includedUsers })}
                </span>
                <span>
                  {t("billing.priceCHF", { price: basePlanPrice.toFixed(2) })}
                </span>
              </div>
              <div className="flex justify-between">
                <span>
                  {t("billing.additionalUsersCount", {
                    count: additionalUsers,
                  })}
                  <span className="ml-1 text-sm text-muted-foreground">
                    (
                    {t("billing.pricePerUserCHF", {
                      price: additionalUserFee.toFixed(2),
                    })}
                    {isYearlyBilling
                      ? ` ${t("billing.perYear")}`
                      : ` ${t("billing.perMonth")}`}
                    )
                  </span>
                </span>
                <span>
                  {t("billing.priceCHF", {
                    price: additionalUserCost.toFixed(2),
                  })}
                  {isYearlyBilling && (
                    <span className="ml-1 text-sm text-muted-foreground">
                      ({t("billing.perYear")})
                    </span>
                  )}
                  {!isYearlyBilling && (
                    <span className="ml-1 text-sm text-muted-foreground">
                      ({t("billing.perMonth")})
                    </span>
                  )}
                </span>
              </div>

              {userDifference > 0 && (
                <div className="text-sm mt-2 text-orange-500">
                  {t("billing.addingUsers", {
                    count: userDifference,
                    price: userCostDifference.toFixed(2),
                  })}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="storage" className="space-y-4 mt-4">
            {usageSummary && (
              <div className="text-sm mb-4">
                <p>
                  {t("billing.currentUsage")}:{" "}
                  {usageSummary.totalUsage.toFixed(2)} GB
                  {usageSummary.totalUsage > basePlanStorage && (
                    <span className="text-orange-500 ml-1">
                      ({t("billing.exceededBaseStorage")})
                    </span>
                  )}
                </p>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <Label htmlFor="additionalStorage">
                  {t("billing.currentAdditionalStorage")}
                </Label>
                <div className="mt-2 text-lg font-medium">
                  {additionalStorageGB} GB
                </div>

                {/* Display current storage tiers */}
                {storageTiers.length > 0 && (
                  <div className="mt-2 text-sm text-muted-foreground">
                    {t("billing.currentStorageTiers")}:
                    <ul className="list-disc list-inside mt-1">
                      {storageTiers.map((tier, index) => (
                        <li key={index}>
                          {tier.quantity} x {tier.size} GB
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="ml-2 h-6 w-6 p-0"
                            onClick={() => {
                              // Remove one quantity of this tier
                              const updatedTiers = [...storageTiers];
                              if (updatedTiers[index].quantity > 1) {
                                updatedTiers[index].quantity -= 1;
                                setStorageTiers(updatedTiers);
                              } else {
                                // Remove the tier completely
                                setStorageTiers(
                                  updatedTiers.filter((_, i) => i !== index)
                                );
                              }
                            }}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="storageIncrement">
                  {t("billing.selectStorageIncrement")}
                </Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {STORAGE_INCREMENTS.map((increment) => (
                    <Button
                      key={increment}
                      type="button"
                      variant={
                        selectedStorageIncrement === increment
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => setSelectedStorageIncrement(increment)}
                    >
                      {increment} GB
                    </Button>
                  ))}
                </div>
              </div>

              <div className="flex flex-col gap-2">
                {(additionalStorage || 0) > 0 && (
                  <div className="text-sm text-muted-foreground">
                    {t("billing.addingStorageToExisting", {
                      existing: additionalStorage || 0,
                    })}
                  </div>
                )}
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={incrementStorage}
                    className="flex-1"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t("billing.addSelectedStorage", {
                      size: selectedStorageIncrement,
                    })}
                  </Button>

                  {additionalStorageGB > (additionalStorage || 0) && (
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={resetStorage}
                      className="flex-none"
                    >
                      <X className="h-4 w-4 mr-2" />
                      {t("billing.resetStorage")}
                    </Button>
                  )}
                </div>
              </div>
            </div>

            <div className="rounded-md border p-4 space-y-2">
              <div className="flex justify-between">
                <span>
                  {t("billing.basePlanStorage", { storage: basePlanStorage })}
                </span>
                <span>{t("billing.included")}</span>
              </div>
              {/* Show existing storage if any */}
              {(additionalStorage || 0) > 0 && (
                <div className="flex justify-between">
                  <span>
                    {t("billing.existingAdditionalStorage", {
                      count: additionalStorage || 0,
                    })}
                  </span>
                  <span>{t("billing.included")}</span>
                </div>
              )}

              {/* Show new storage being added */}
              {additionalStorageGB > (additionalStorage || 0) && (
                <div className="flex justify-between">
                  <span>
                    {t("billing.newStorageBeingAdded", {
                      count: additionalStorageGB - (additionalStorage || 0),
                    })}
                  </span>
                  <span>
                    {t("billing.priceCHF", {
                      price: storageCostDifference.toFixed(2),
                    })}
                    {isYearlyBilling && (
                      <span className="ml-1 text-sm text-muted-foreground">
                        ({t("billing.perYear")})
                      </span>
                    )}
                    {!isYearlyBilling && (
                      <span className="ml-1 text-sm text-muted-foreground">
                        ({t("billing.perMonth")})
                      </span>
                    )}
                  </span>
                </div>
              )}

              {/* Show total additional storage */}
              <div className="flex justify-between">
                <span>
                  {additionalStorageGB > 0
                    ? t("billing.totalAdditionalStorage", {
                        count: additionalStorageGB,
                      })
                    : t("billing.noAdditionalStorage")}
                </span>
                <span>
                  {additionalStorageGB > 0 ? (
                    <>
                      {t("billing.priceCHF", {
                        price: additionalStorageCost.toFixed(2),
                      })}
                      {isYearlyBilling && (
                        <span className="ml-1 text-sm text-muted-foreground">
                          ({t("billing.perYear")})
                        </span>
                      )}
                      {!isYearlyBilling && (
                        <span className="ml-1 text-sm text-muted-foreground">
                          ({t("billing.perMonth")})
                        </span>
                      )}
                    </>
                  ) : (
                    t("billing.free")
                  )}
                </span>
              </div>

              <div className="border-t pt-2 mt-2 flex justify-between font-medium">
                <span>{t("billing.totalStorage")}</span>
                <span>{totalStorage} GB</span>
              </div>

              {storageDifference > 0 && (
                <div className="text-sm mt-2 text-orange-500">
                  {t("billing.addingStorage", {
                    count: storageDifference,
                    price: storageCostDifference.toFixed(2),
                  })}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <Separator className="my-4" />

        <div className="rounded-md border p-4 space-y-2">
          <div className="flex justify-between font-medium">
            <span>{t("billing.totalSubscriptionCost")}</span>
            <span>
              {isYearlyBilling
                ? t("billing.pricePerYear", { price: totalCost.toFixed(2) })
                : t("billing.pricePerMonth", { price: totalCost.toFixed(2) })}
            </span>
          </div>

          {(userDifference > 0 || storageDifference > 0) && (
            <div className={`text-sm mt-2 text-orange-500`}>
              {t("billing.increasingSubscription", {
                price: (userCostDifference + storageCostDifference).toFixed(2),
              })}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-4">
        {activeTab === "users" && (
          <Button
            onClick={handleUpdateUsers}
            disabled={
              loading || additionalUsers <= subscription.additionalUsers
            }
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("billing.processing")}
              </>
            ) : (
              <>
                <Users className="mr-2 h-4 w-4" />
                {t("billing.updateUsers")}
              </>
            )}
          </Button>
        )}

        {activeTab === "storage" && (
          <Button
            onClick={handleUpdateStorage}
            disabled={
              loading || additionalStorageGB === (additionalStorage || 0)
            }
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("billing.processing")}
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                {t("billing.addStorageAction")}
              </>
            )}
          </Button>
        )}
      </CardFooter>

      {/* Confirmation Dialog */}
      <SubscriptionUpdateConfirmationDialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        onConfirm={handleConfirmUpdate}
        onCancel={handleCancelUpdate}
        updateType={confirmDialogType}
        currentValue={
          confirmDialogType === "users"
            ? subscription.additionalUsers
            : additionalStorage
        }
        newValue={
          confirmDialogType === "users" ? additionalUsers : additionalStorageGB
        }
        loading={loading}
        priceChange={
          confirmDialogType === "users"
            ? userCostDifference
            : storageCostDifference
        }
        isYearlyBilling={isYearlyBilling}
      />
    </Card>
  );
}
