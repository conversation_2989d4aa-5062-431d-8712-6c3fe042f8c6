"use client";

// Cache the token to avoid making too many requests
let cachedToken: { value: string | null; expiry: number } = {
  value: null,
  expiry: 0,
};

/**
 * Get a JWT token for API authentication
 *
 * This function fetches a JWT token from the server based on the current user's session.
 * The token is cached for 50 minutes to avoid making too many requests.
 *
 * @returns The JWT token as a string, or null if not authenticated
 */
export const getAuthToken = async (): Promise<string | null> => {
  try {
    // Check if we have a cached token that's still valid
    const now = Date.now();
    if (cachedToken.value && cachedToken.expiry > now) {
      return cachedToken.value;
    }

    // Fetch a new token from the server
    const response = await fetch("/api/auth/token", {
      method: "GET",
      credentials: "include",
    });

    if (!response.ok) {
      console.error("Failed to fetch auth token:", await response.text());
      return null;
    }

    const data = await response.json();

    if (data.token) {
      // Cache the token for 50 minutes (tokens expire after 1 hour)
      cachedToken = {
        value: data.token,
        expiry: now + 50 * 60 * 1000, // 50 minutes in milliseconds
      };

      return data.token;
    }

    return null;
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
};

/**
 * Add authorization headers to a fetch request
 *
 * @param headers - The headers object to add authorization to
 * @returns The headers object with authorization added if available
 */
export const addAuthHeaders = async (
  headers: HeadersInit = {}
): Promise<HeadersInit> => {
  const token = await getAuthToken();

  if (token) {
    return {
      ...headers,
      Authorization: `Bearer ${token}`,
    };
  }

  return headers;
};

/**
 * Create a fetch function that includes authentication
 *
 * @returns A fetch function that includes authentication
 */
export const createAuthenticatedFetch = () => {
  return async (url: string, options: RequestInit = {}) => {
    const headers = await addAuthHeaders(options.headers || {});

    return fetch(url, {
      ...options,
      headers,
      // This is important for cookies to be sent with the request
      credentials: "include",
    });
  };
};
