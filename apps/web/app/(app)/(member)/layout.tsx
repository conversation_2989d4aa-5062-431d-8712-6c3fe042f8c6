import { redirect } from "next/navigation";
import { SidebarInset, SidebarProvider } from "@/components/layouts/sidebar";
import { MemberAppSidebar } from "@/components/layouts/member-sidebar";
import { SiteHeader } from "@/components/layouts/site-header";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";
import { QuickAskWrapper } from "@/components/quick-ask";
import { GlobalSearchProvider } from "@/components/global-search";

export default async function HomePage({ children }: any) {
  const session: any = await getServerSession(authOptions);

  if (!session) {
    return redirect("/sign-in");
  } else if (!session?.memberships || session?.memberships?.length === 0) {
    return redirect("/onboarding");
  }
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;
  const chatHistory = [
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Nextjs",
      url: "#",
    },
    {
      name: "Status: InProgress. Time: 60.8694658(s)",
      url: "#",
    },
    {
      name: "Accounting search information",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Nextjs",
      url: "#",
    },
    {
      name: "Status: InProgress. Time: 60.8694658(s)",
      url: "#",
    },
    {
      name: "Accounting search information",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Nextjs",
      url: "#",
    },
    {
      name: "Status: InProgress. Time: 60.8694658(s)",
      url: "#",
    },
    {
      name: "Accounting search information",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
  ];

  return (
    <GlobalSearchProvider tenantId={tenantId}>
      <QuickAskWrapper>
        <div className="[--header-height:calc(theme(spacing.14))]">
          <SidebarProvider className="flex flex-col">
            <SiteHeader />
            <div className="flex flex-1">
              <MemberAppSidebar chatHistory={chatHistory} session={session} />
              <SidebarInset className="p-4"> {children}</SidebarInset>
            </div>
          </SidebarProvider>
        </div>
      </QuickAskWrapper>
    </GlobalSearchProvider>
  );
}
