import db from "@/lib/shared-db";

/**
 * Checks if a tenant has an active subscription
 * @param tenantId The ID of the tenant to check
 * @returns Object containing whether the tenant has an active subscription and the subscription details
 */
export async function checkActiveSubscription(tenantId: string) {
  if (!tenantId) {
    return { hasActiveSubscription: false, subscription: null };
  }

  // Get active subscription for the tenant
  const subscription = await db.subscription.findFirst({
    where: {
      tenantId,
      isActive: true,
    },
    include: {
      plan: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return {
    hasActiveSubscription: !!subscription,
    subscription,
  };
}

/**
 * Checks if a tenant has reached its user limit based on the subscription plan
 * @param tenantId The ID of the tenant to check
 * @returns Object containing whether the tenant has reached its user limit and related details
 */
export async function checkUserLimit(tenantId: string) {
  if (!tenantId) {
    return {
      hasReachedLimit: true,
      currentCount: 0,
      limit: 0,
      subscription: null,
    };
  }

  // Get active subscription with plan details
  const { subscription } = await checkActiveSubscription(tenantId);

  if (!subscription) {
    return {
      hasReachedLimit: true,
      currentCount: 0,
      limit: 0,
      subscription: null,
    };
  }

  // Count current members in the tenant
  const memberCount = await db.membership.count({
    where: { tenantId },
  });

  // Calculate the total user limit based on the plan's included users plus any additional users
  const totalUserLimit =
    subscription.plan.includedUsers + (subscription.additionalUsers || 0);

  return {
    hasReachedLimit: memberCount >= totalUserLimit,
    currentCount: memberCount,
    limit: totalUserLimit,
    subscription,
  };
}
