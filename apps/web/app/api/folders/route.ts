import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";

//Get folders for a workspace (with nested structure)
const getFolders = async (request: Request) => {
  try {
    const url = new URL(request.url);
    const rawWorkspaceSlug = url.searchParams.get("workspaceSlug");
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;
    const folderId = url.searchParams.get("folderId");

    if (!workspaceSlug) {
      return NextResponse.json(
        { error: "Workspace ID is required" },
        { status: 400 }
      );
    }

    if (folderId) {
      const folder = await db.folder.findUnique({
        where: { id: folderId },
        include: {
          parentRelations: {
            include: { child: true }, // Fetch parent folders
          },
          childRelations: {
            include: { child: true, parent: true }, // Fetch child folders
          },
          files: true,
        },
      });

      if (!folder) {
        return NextResponse.json(
          { error: "Folder not found" },
          { status: 404 }
        );
      }
      const hierarchy = await db.folderHierarchy.findMany({
        where: {
          pageId: folder?.pageId,
        },
        include: {
          parent: true,
          child: true,
        },
      });

      folder.hierarchy = hierarchy;

      return NextResponse.json({ folder }, { status: 200 });
    }

    // Fetch all root folders in the workspace (top-level folders with no parent)
    const folders = await db.folder.findMany({
      where: {
        workspace: {
          slug: workspaceSlug,
        },
        parentRelations: { none: {} },
      },
      include: { childRelations: { include: { child: true } }, files: true },
    });

    return NextResponse.json({ folders }, { status: 200 });
  } catch (error) {
    console.error("Error fetching folders:", error);
    return NextResponse.json(
      { error: "Failed to fetch folders" },
      { status: 500 }
    );
  }
};

// ✅ Create a new folder
const createFolder = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const {
      name,
      workspaceSlug: slug,
      pageId,
      parentIds = [],
    } = await request.json();
    const workspaceSlug = decodeURI(slug);
    if (!name || !workspaceSlug) {
      return NextResponse.json(
        { error: "Name and workspace ID are required" },
        { status: 400 }
      );
    }
    const workspace = await db.workspace.findFirst({
      where: {
        slug: workspaceSlug,
      },
    });
    // Create folder
    const folder = await db.folder.create({
      data: { name, workspaceId: workspace?.id, pageId },
    });

    // Handle multiple parent-child relationships
    if (parentIds.length > 0) {
      await db.folderHierarchy.createMany({
        data: parentIds.map((parentId: string) => ({
          parentId,
          pageId,
          childId: folder.id,
        })),
      });
    }

    return NextResponse.json(
      { message: "Folder created successfully", folder },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating folder:", error);
    return NextResponse.json(
      { error: "Failed to create folder" },
      { status: 500 }
    );
  }
};

// ✅ Update a folder
const updateFolder = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const { id, name } = await request.json();
    if (!id || !name) {
      return NextResponse.json(
        { error: "Folder ID and name are required" },
        { status: 400 }
      );
    }

    const folder = await db.folder.update({ where: { id }, data: { name } });
    return NextResponse.json({
      message: "Folder updated successfully",
      folder,
    });
  } catch (error) {
    console.error("Error updating folder:", error);
    return NextResponse.json(
      { error: "Failed to update folder" },
      { status: 500 }
    );
  }
};

// ✅ Delete a folder (with cascading delete for relationships)
const deleteFolder = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const folderId = url.searchParams.get("id");
    const force = url.searchParams.get("force") === "true";

    if (!folderId)
      return NextResponse.json(
        { error: "Folder ID is required" },
        { status: 400 }
      );

    const folder = await db.folder.findUnique({
      where: { id: folderId },
      include: { parentRelations: true, childRelations: true, files: true },
    });

    if (!folder)
      return NextResponse.json({ error: "Folder not found" }, { status: 404 });

    if (
      (folder.parentRelations.length > 0 || folder.files.length > 0) &&
      !force
    ) {
      return NextResponse.json(
        { error: "Folder is not empty." },
        { status: 400 }
      );
    }

    await db.folder.delete({ where: { id: folderId } });

    return NextResponse.json({ message: "Folder deleted successfully" });
  } catch (error) {
    console.error("Error deleting folder:", error);
    return NextResponse.json(
      { error: "Failed to delete folder" },
      { status: 500 }
    );
  }
};

// Export the handlers with permission checks
export const GET = withPermission(getFolders, "READ", "FOLDER");
export const POST = withPermission(createFolder, "CREATE", "FOLDER");
export const PUT = withPermission(updateFolder, "UPDATE", "FOLDER");
export const DELETE = withPermission(deleteFolder, "DELETE", "FOLDER");
