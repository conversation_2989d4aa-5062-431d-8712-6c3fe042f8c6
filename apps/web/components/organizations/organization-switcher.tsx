"use client";

import { useState, useEffect, useMemo } from "react";
import { useSession } from "next-auth/react";
import {
  Building,
  Check,
  ChevronsUpDown,
  PlusCircle,
  Settings,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { setCurrentOrganization, createOrganization } from "@/services";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

import { getCookie, setCookie } from "@/utils/cookies";

// Define Organization interface
interface Organization {
  id: string;
  name: string;
  slug: string;
  role?: string;
  image?: string;
}

// Create schema with translations
const createFormSchema = (t: any) =>
  z.object({
    name: z.string().min(3, {
      message: t("organization.nameMinLength"),
    }),
    description: z.string().optional(),
    url: z
      .string()
      .url({
        message: t("organization.validUrl"),
      })
      .optional()
      .or(z.literal("")),
  });

export function OrganizationSwitcher({ isAdmin }) {
  const { data: session, update } = useSession({
    required: true,
  });
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [switcherOpen, setSwitcherOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const organizationId = getCookie("currentOrganizationId");
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<
    string | null
  >(null);
  const { t } = useLanguage();

  // Create schema with translations
  const formSchema = createFormSchema(t);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      url: "",
    },
  });

  // Map organizations from session
  const organizations: Organization[] =
    (session as any)?.memberships?.map((membership: any) => ({
      id: membership.tenant.id,
      name: membership.tenant.name,
      slug: membership.tenant.slug,
      role: membership.role,
      image: membership.tenant.image,
    })) || [];

  // Get current organization from session or from state
  const sessionCurrentOrg: Organization | undefined = (session as any)
    ?.currentOrganization;

  // Initialize from localStorage and update when needed
  useEffect(() => {
    // Only run on client side
    if (typeof window === "undefined") return;

    // Try to get from localStorage first
    const savedOrgId = getCookie("currentOrganizationId");

    if (savedOrgId) {
      // Make sure the org exists in the user's organizations
      const orgExists = organizations.some((org) => org.id === savedOrgId);
      if (orgExists) {
        setSelectedOrganizationId(savedOrgId);
      } else if (organizations.length > 0) {
        // If saved org doesn't exist, use first available
        setSelectedOrganizationId(organizations[0].id);
        setCookie("currentOrganizationId", organizations[0].id);
        setCookie("userId", (session as any)?.userId);
      }
    } else if (sessionCurrentOrg?.id) {
      // If nothing in localStorage but session has a value, use that
      setSelectedOrganizationId(sessionCurrentOrg.id);
      setCookie("currentOrganizationId", sessionCurrentOrg.id);
      setCookie("userId", (session as any)?.userId);
    } else if (organizations.length > 0) {
      // Fallback to first org
      setSelectedOrganizationId(organizations[0].id);
      setCookie("currentOrganizationId", organizations[0].id);
      setCookie("userId", (session as any)?.userId);
    }
  }, [organizations, sessionCurrentOrg]);

  // Get current organization from local state or session
  const currentOrganization: Organization | undefined = useMemo(() => {
    // If we have a selected ID, find that organization
    if (organizationId) {
      const selected = organizations.find(
        (org: Organization) => org.id === organizationId
      );

      if (selected) return selected;
    }

    // Otherwise use the one from session
    return sessionCurrentOrg;
  }, [sessionCurrentOrg, organizationId, organizations]);

  // Handle organization switching
  const handleOrganizationSelect = async (organizationId: string) => {
    // Return early if the org is already selected
    if (selectedOrganizationId === organizationId) {
      // Close the dropdown
      setSwitcherOpen(false);
      return;
    }

    // Update local state immediately for better UI responsiveness
    setSelectedOrganizationId(organizationId);

    // Save to localStorage for persistence
    if (typeof window !== "undefined") {
      setCookie("currentOrganizationId", organizationId);
      setCookie("userId", (session as any)?.userId);
    }

    // Close the dropdown
    setSwitcherOpen(false);

    setLoading(true);
    try {
      toast.loading(t("organization.switching"));

      // Find the selected organization from existing memberships
      const selectedOrg = organizations.find(
        (org) => org.id === organizationId
      );
      if (!selectedOrg) {
        toast.dismiss();
        toast.error(t("organization.notFound"));
        return;
      }

      // Set it as the current organization in the session
      await update({
        currentOrganization: selectedOrg,
      });

      // Make API call to server-side
      const response = await setCurrentOrganization(organizationId);

      if (response.error) {
        toast.dismiss();
        toast.error(response.error);
        return;
      }

      // Hard refresh the page to ensure all components re-render with the new org
      toast.dismiss();
      toast.success(t("organization.switchSuccess"));

      // Special handling for organization settings page - needs to show settings for the new org
      if (window.location.pathname === "/settings") {
        // Just reload the page to show settings for the new organization
        setTimeout(() => {
          window.location.reload();
        }, 500);
      } else {
        // For other pages, maintain the current path
        setTimeout(() => {
          window.location.href = window.location.pathname;
        }, 500);
      }
    } catch (error) {
      console.error("Error switching organization:", error);
      toast.dismiss();
      toast.error(t("organization.switchFailed"));
    } finally {
      setLoading(false);
    }
  };

  // Handle organization creation
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    try {
      toast.loading(t("organization.creating"));

      // Generate slug from name
      const slug = values.name.toLowerCase().replace(/\s+/g, "-");

      const response = await createOrganization({
        name: values.name,
        slug,
        description: values.description,
        url: values.url,
      });

      if (response.error) {
        toast.dismiss();
        toast.error(response.error);
        return;
      }

      // Update local state immediately
      setSelectedOrganizationId(response.data.id);

      // Save to localStorage for persistence
      if (typeof window !== "undefined") {
        setCookie("currentOrganizationId", response.data.id);
        setCookie("userId", (session as any)?.userId);
      }

      // Update session with new organization
      const newMembership = {
        tenant: response.data,
        role: "OWNER", // User will be owner of new organization
      };

      await update({
        memberships: [...((session as any)?.memberships || []), newMembership],
        currentOrganization: response.data,
      });

      // Reset form and close dialog
      form.reset();
      setCreateDialogOpen(false);

      // Refresh the page
      toast.dismiss();
      toast.success(t("organization.createSuccess"));

      // Hard refresh for consistency
      setTimeout(() => {
        window.location.href = window.location.pathname;
      }, 500);
    } catch (error) {
      console.error("Error creating organization:", error);
      toast.dismiss();
      toast.error(t("organization.createFailed"));
    } finally {
      setLoading(false);
    }
  };

  // Define filteredOrganizations outside of conditional returns
  const filteredOrganizations = useMemo(() => {
    return organizations.filter((org: Organization) =>
      org.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [organizations, searchTerm]);

  // Effect to update current organization if needed
  useEffect(() => {
    if (!currentOrganization && organizations.length > 0) {
      update({
        currentOrganization: organizations[0],
      });
    }
  }, [currentOrganization, organizations, update]);

  if (!session) {
    return null;
  }

  // If there's no current organization but we have memberships, return early
  // The useEffect above will handle updating the current organization
  if (!currentOrganization && organizations.length > 0) {
    return null;
  }

  if (!currentOrganization) {
    return null;
  }

  return (
    <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
      <Popover open={switcherOpen} onOpenChange={setSwitcherOpen}>
        <PopoverTrigger asChild>
          <div className="flex items-center w-full rounded-lg p-3 text-sm cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors">
            <Avatar className="h-8 w-8 rounded-lg">
              <AvatarImage
                src={currentOrganization.image}
                alt={currentOrganization.name}
              />
              <AvatarFallback className="rounded-lg bg-primary/10">
                <Building className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight ml-3">
              <span className="truncate font-semibold">
                {currentOrganization.name}
              </span>
              <span className="truncate text-xs">
                {currentOrganization.role || "Owner"}
              </span>
            </div>
            <ChevronsUpDown className="ml-auto size-4" />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="overflow-hidden rounded-md border border-muted bg-popover text-popover-foreground">
            <div className="flex flex-col">
              <div className="border-b p-2">
                <input
                  className="w-full bg-transparent py-2 px-2 text-sm outline-none placeholder:text-muted-foreground"
                  placeholder={t("organization.searchPlaceholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="p-2">
                <div className="text-xs font-medium text-muted-foreground py-1.5 px-2">
                  {t("common.organizations")}
                </div>

                {filteredOrganizations.length === 0 ? (
                  <div className="py-6 text-center text-sm">
                    {t("organization.noOrganizationsFound")}
                  </div>
                ) : (
                  <div>
                    {filteredOrganizations.map((org: Organization) => (
                      <button
                        key={org.id}
                        onClick={() => {
                          handleOrganizationSelect(org.id);
                          setSwitcherOpen(false);
                        }}
                        className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                      >
                        <Avatar className="mr-2 h-5 w-5">
                          <AvatarImage src={org.image} alt={org.name} />
                          <AvatarFallback className="bg-primary/10">
                            <Building className="h-3 w-3" />
                          </AvatarFallback>
                        </Avatar>
                        <span className="truncate">{org.name}</span>
                        {selectedOrganizationId === org.id && (
                          <Check className="ml-auto h-4 w-4" />
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              <div className="border-t p-2">
                {currentOrganization && isAdmin && (
                  <a
                    href={`/settings`}
                    className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                    onClick={() => setSwitcherOpen(false)}
                  >
                    <Settings className="mr-2 h-5 w-5" />
                    {t("common.organizationSettings")}
                  </a>
                )}
                <button
                  onClick={() => {
                    setSwitcherOpen(false);
                    setCreateDialogOpen(true);
                  }}
                  className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground mt-1"
                >
                  <PlusCircle className="mr-2 h-5 w-5" />
                  {t("organization.createOrganization")}
                </button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Create Organization Dialog */}
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("organization.createOrganization")}</DialogTitle>
          <DialogDescription>
            {t("organization.createDescription")}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("common.name")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("organization.namePlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("organization.descriptionOptional")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("organization.descriptionPlaceholder")}
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("organization.urlOptional")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("organization.urlPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setCreateDialogOpen(false)}
                disabled={loading}
              >
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? t("organization.creating") : t("common.create")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
