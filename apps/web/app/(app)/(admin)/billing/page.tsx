import BillingScreen from "@/components/pricing/pricing";
import {
  getPlans,
  getTenantSubscription,
  getVectorStoreUsageSummary,
} from "@/services";
import { cookies } from "next/headers";

export default async function BillingPage() {
  const tenantId = (await cookies().get("currentOrganizationId")?.value) ?? "";
  const userId = (await cookies().get("userId")?.value) ?? "";
  const [plansData, subscriptionData, usageData] = await Promise.all([
    getPlans(),
    getTenantSubscription(tenantId, userId),
    getVectorStoreUsageSummary(tenantId, userId),
  ]);

  return (
    <BillingScreen
      plans={plansData}
      subscription={subscriptionData}
      usageSummary={usageData}
    />
  );
}
