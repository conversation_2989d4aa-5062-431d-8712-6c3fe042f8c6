import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import db from "@/lib/shared-db";
import { withPermission } from "@/lib/permission-middleware";
import { fetchUrlContent } from "./content-extractor";

/**
 * Fetches and parses a sitemap to extract URLs
 */
async function fetchSitemap(url: string) {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch sitemap: ${response.statusText}` },
        { status: response.status }
      );
    }

    const xml = await response.text();

    // Extract URLs from sitemap
    const urlMatches = xml.matchAll(/<loc>(.*?)<\/loc>/gi);
    const urls: string[] = [];

    for (const match of urlMatches) {
      urls.push(match[1].trim());
    }

    return NextResponse.json({
      sitemapUrl: url,
      urls,
    });
  } catch (error) {
    console.error("Error fetching sitemap:", error);
    return NextResponse.json(
      { error: `Failed to process sitemap: ${error.message}` },
      { status: 500 }
    );
  }
}

/**
 * Checks if a URL has already been imported
 */
async function checkDuplicateUrl(url: string, workspaceId: string) {
  // For MongoDB, we need to use a different approach to query JSON fields
  const existingFile = await db.file.findFirst({
    where: {
      workspaceId,
      // Use a raw filter for MongoDB to query inside the JSON
      metadata: {
        // This syntax works with MongoDB to check if the sourceUrl field exists and equals the URL
        sourceUrl: url,
      },
    },
  });

  return existingFile !== null;
}

/**
 * Handler for URL import requests
 */
async function handleUrlImport(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request data
    const { url, action, workspaceId, options } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Handle different actions
    if (action === "fetch") {
      const result = await fetchUrlContent(url, options);
      if (!result.success) {
        return NextResponse.json(
          { error: result.error || "Failed to fetch URL content" },
          { status: 500 }
        );
      }
      return NextResponse.json(result);
    } else if (action === "sitemap") {
      return fetchSitemap(url);
    } else if (action === "check-duplicate" && workspaceId) {
      const isDuplicate = await checkDuplicateUrl(url, workspaceId);
      return NextResponse.json({ isDuplicate });
    } else if (action === "batch-import" && workspaceId) {
      // This would be implemented to handle batch import of multiple URLs
      return NextResponse.json(
        {
          error: "Batch import is not yet implemented",
        },
        { status: 501 }
      );
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Error handling URL import:", error);
    return NextResponse.json(
      { error: `Failed to process request: ${error.message}` },
      { status: 500 }
    );
  }
}

// Export the handler with permission check
export const POST = withPermission(handleUrlImport, "CREATE", "FILE");
