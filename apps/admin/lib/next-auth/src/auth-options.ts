import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { type NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare } from "bcrypt";
import db from "@/lib/shared-db";
import crypto from "crypto";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  pages: {
    signIn: "/sign-in",
  },
  secret: process.env.NEXTAUTH_SECRET,
  session: { strategy: "jwt" },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Username", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Username and password are required.");
        }

        try {
          // Find user by email
          const emailHash = crypto
            .createHash("sha256")
            .update(credentials.email)
            .digest("hex");
          const user = await db.adminUser.findUnique({
            where: { email: credentials.email },
          });
          console.log("useruser", user);

          if (!user) {
            throw new Error("User not found. Please check your email.");
          }

          const passwordCorrect = await compare(
            credentials.password,
            user.hashedPassword
          );

          if (!passwordCorrect) {
            throw new Error("Invalid password. Please try again.");
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
          }; // Success
        } catch (error) {
          console.error("Authorization Error:", error);
          throw new Error(error.message || "Internal Server Error");
        }
      },
    }),
  ],
  logger: {
    error(code, metadata) {
      console.error(code, metadata);
    },
    warn(code) {
      console.warn(code);
    },
    debug(code, metadata) {
      console.debug(code, metadata);
    },
  },
  cookies: {
    sessionToken: {
      name: `${
        process.env.NODE_ENV === "production" ? "__Secure-" : ""
      }next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user?.email && token?.email) {
        const emailHash = crypto
          .createHash("sha256")
          .update(session?.user?.email)
          .digest("hex");
        const user = await db.adminUser.findUnique({
          where: {
            email: session?.user?.email,
          },
        });
      }

      return { ...session, ...token };
    },

    async signIn({ user }) {
      // Only allow sign in if the user exists and has at least one organization
      if (!user) return false;

      const emailHash = crypto
        .createHash("sha256")
        .update(user.email ?? "")
        .digest("hex");
      const dbUser = await db.user.findUnique({
        where: {
          emailHash,
        },
        include: {
          membership: true,
        },
      });

      // The user must have at least one organization to sign in
      return !!(dbUser?.membership && dbUser.membership.length > 0);
    },

    async jwt({ token }) {
      if (token?.email) {
        const emailHash = crypto
          .createHash("sha256")
          .update(token?.email)
          .digest("hex");
        const user = await db.user.findUnique({
          where: {
            emailHash,
          },
          select: {
            id: true,
          },
        });

        if (user?.id) {
          token.userId = user.id;
        }
      }
      return token;
    },
  },
};
