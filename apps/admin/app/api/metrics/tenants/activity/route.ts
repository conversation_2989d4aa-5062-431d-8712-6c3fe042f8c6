import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get("from")
      ? new Date(searchParams.get("from") as string)
      : new Date(new Date().setDate(new Date().getDate() - 30));
    const toDate = searchParams.get("to")
      ? new Date(searchParams.get("to") as string)
      : new Date();

    // Get total tenants
    const totalTenants = await db.tenant.count({
      where: {
        createdAt: {
          lte: toDate,
        },
      },
    });

    // Get onboarded tenants
    const onboardedTenants = await db.tenant.count({
      where: {
        isOnboarded: true,
        createdAt: {
          lte: toDate,
        },
      },
    });

    // Calculate onboarding rate
    const onboardingRate =
      totalTenants > 0
        ? Math.round((onboardedTenants / totalTenants) * 100)
        : 0;

    // Get active tenants (those with active subscriptions)
    const activeTenants = await db.tenant.count({
      where: {
        Subscription: {
          some: {
            isActive: true,
          },
        },
        createdAt: {
          lte: toDate,
        },
      },
    });

    // Calculate inactive tenants
    const inactiveTenants = totalTenants - activeTenants;

    // Get tenants with recent activity (API requests in the selected date range)
    const tenantsWithActivity = await db.aPIRequest.groupBy({
      by: ["tenantId"],
      where: {
        timestamp: {
          gte: fromDate,
          lte: toDate,
        },
      },
      _count: {
        _all: true,
      },
    });

    const activeTenantsCount = tenantsWithActivity.length;
    const activityRate =
      totalTenants > 0
        ? Math.round((activeTenantsCount / totalTenants) * 100)
        : 0;

    // Calculate churn rate - tenants who canceled or didn't renew in the date range
    // Get count of subscriptions that ended in the date range
    const canceledSubscriptions = await db.subscription.count({
      where: {
        isActive: false,
        endDate: {
          gte: fromDate,
          lte: toDate,
        },
      },
    });

    // Calculate churn rate as percentage of total active tenants
    const churnRate =
      activeTenants > 0
        ? Math.round((canceledSubscriptions / activeTenants) * 100 * 10) / 10
        : 0;

    // Get tenant health score based on activity, support tickets, etc.
    // This is a simplified version - in a real implementation, you would have more factors
    const tenantHealthScore =
      totalTenants > 0
        ? Math.round(
            ((activeTenantsCount / totalTenants) * 0.6 +
              (onboardingRate / 100) * 0.4) *
              100
          )
        : 0;

    return NextResponse.json({
      totalTenants,
      activeTenants,
      inactiveTenants,
      onboardedTenants,
      onboardingRate,
      activeTenantsCount,
      activityRate,
      churnRate,
      tenantHealthScore,
      canceledSubscriptions,
    });
  } catch (error) {
    console.error("Error fetching tenant activity metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch tenant activity metrics" },
      { status: 500 }
    );
  }
}
