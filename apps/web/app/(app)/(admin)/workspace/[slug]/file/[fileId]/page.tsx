import FilePage from "@/components/wrapper-screens/workspace/file-wrapper";
import { getFile, getVectorStoreUsageSummary } from "@/services";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { hasPermission } from "@/lib/permissions";

export default async function Page({ params }) {
  const { fileId, slug } = params;

  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;

  if (!tenantId || !userId) {
    return redirect("/sign-in");
  }

  try {
    // Check if user has permission to read this file
    const hasReadPermission = await hasPermission(
      userId,
      tenantId,
      "READ",
      "FILE",
      fileId
    );

    // If user doesn't have permission, redirect to workspace page
    if (!hasReadPermission) {
      console.log(
        `User ${userId} doesn't have permission to access file ${fileId}`
      );
      return redirect(`/workspace/${slug}`);
    }

    const [fileData, usageSummary] = await Promise.all([
      getFile(tenantId, userId, fileId),
      getVectorStoreUsageSummary(tenantId, userId),
    ]);

    // Handle case where file data is not found
    if (!fileData || !fileData.file) {
      return redirect(`/workspace/${slug}`);
    }

    return (
      <FilePage
        workspaceSlug={slug}
        tenantId={tenantId}
        fileId={fileId}
        file={fileData.file}
        usageSummary={usageSummary}
      />
    );
  } catch (error) {
    console.error("Error loading folder:", error);
    return redirect(`/workspace/${slug}`);
  }
}
