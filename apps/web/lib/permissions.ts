import { PermissionAction, PermissionResource } from "@prisma/client";
import db from "@/lib/shared-db";

/**
 * Check if a user has a specific permission for a resource
 * @param userId - The ID of the user
 * @param tenantId - The ID of the tenant
 * @param action - The action to check (CREATE, READ, UPDATE, DELETE)
 * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
 * @param resourceId - Optional ID of the specific resource
 * @returns Promise<boolean> - Whether the user has the permission
 */
export async function hasPermission(
  userId: string,
  tenantId: string,
  action: PermissionAction,
  resource: PermissionResource,
  resourceId?: string
): Promise<boolean> {
  try {
    // First, check if the user is a member of the tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        customRole: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    if (!membership) {
      return false;
    }

    // If the user is an OWNER, they have all permissions
    if (membership.role === "OWNER") {
      return true;
    }

    // If the user is an ADMIN, they have all permissions except for certain owner-only actions
    if (membership.role === "ADMIN") {
      // Define any owner-only permissions here
      const ownerOnlyPermissions = [
        // Example: Only owners can delete workspaces
        {
          action: "DELETE" as PermissionAction,
          resource: "WORKSPACE" as PermissionResource,
        },
      ];

      // Check if the requested permission is owner-only
      const isOwnerOnly = ownerOnlyPermissions.some(
        (p) => p.action === action && p.resource === resource
      );

      if (!isOwnerOnly) {
        return true;
      }
    }

    // If the user has a custom role, check if it has the required permission
    if (membership.customRole) {
      const hasRequiredPermission = membership.customRole.permissions.some(
        (p) =>
          p.permission.action === action && p.permission.resource === resource
      );

      if (hasRequiredPermission) {
        return true;
      }
    }

    // For workspace-specific permissions, check WorkspaceMember role
    if (resource === "WORKSPACE" && resourceId) {
      // First, check direct workspace membership
      const workspaceMember = await db.workspaceMember.findFirst({
        where: {
          userId,
          workspaceId: resourceId,
        },
        include: {
          customRole: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      if (workspaceMember) {
        // Workspace owners and admins have all permissions for their workspace
        if (
          workspaceMember.role === "OWNER" ||
          workspaceMember.role === "ADMIN"
        ) {
          return true;
        }

        // Check custom role permissions for the workspace
        if (workspaceMember.customRole) {
          const hasRequiredPermission =
            workspaceMember.customRole.permissions.some(
              (p) =>
                p.permission.action === action &&
                p.permission.resource === resource
            );

          if (hasRequiredPermission) {
            return true;
          }
        }
      }

      // If no direct membership, check for group-based access
      // Find all groups the user is a member of
      const userGroups = await db.groupMember.findMany({
        where: {
          userId,
        },
        select: {
          groupId: true,
        },
      });

      const userGroupIds = userGroups.map((group) => group.groupId);

      if (userGroupIds.length > 0) {
        // Check if any of these groups have access to the workspace
        const groupWorkspace = await db.groupWorkspace.findFirst({
          where: {
            workspaceId: resourceId,
            groupId: {
              in: userGroupIds,
            },
          },
        });

        // If a matching group-workspace association is found, grant access
        if (groupWorkspace) {
          return true;
        }
      }
    }

    // Default to no permission
    return false;
  } catch (error) {
    console.error("Error checking permission:", error);
    return false;
  }
}

/**
 * Get all permissions for a user in a tenant
 * @param userId - The ID of the user
 * @param tenantId - The ID of the tenant
 * @returns Promise<Array<{action: PermissionAction, resource: PermissionResource}>> - List of permissions
 */
export async function getUserPermissions(
  userId: string,
  tenantId: string
): Promise<Array<{ action: PermissionAction; resource: PermissionResource }>> {
  try {
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        customRole: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    if (!membership) {
      return [];
    }

    // If the user is an OWNER, they have all permissions
    if (membership.role === "OWNER") {
      // Return all possible permissions
      const allPermissions = await db.permission.findMany();
      return allPermissions.map((p) => ({
        action: p.action,
        resource: p.resource,
      }));
    }

    // If the user is an ADMIN, they have all permissions except owner-only ones
    if (membership.role === "ADMIN") {
      // Define owner-only permissions
      const ownerOnlyPermissions = [
        {
          action: "DELETE" as PermissionAction,
          resource: "WORKSPACE" as PermissionResource,
        },
      ];

      // Get all permissions
      const allPermissions = await db.permission.findMany();

      // Filter out owner-only permissions
      return allPermissions
        .filter(
          (p) =>
            !ownerOnlyPermissions.some(
              (op) => op.action === p.action && op.resource === p.resource
            )
        )
        .map((p) => ({ action: p.action, resource: p.resource }));
    }

    // For custom roles, return the specific permissions
    if (membership.customRole) {
      return membership.customRole.permissions.map((p) => ({
        action: p.permission.action,
        resource: p.permission.resource,
      }));
    }

    // Default member role has limited permissions
    return [
      {
        action: "READ" as PermissionAction,
        resource: "WORKSPACE" as PermissionResource,
      },
      {
        action: "READ" as PermissionAction,
        resource: "PAGE" as PermissionResource,
      },
      {
        action: "READ" as PermissionAction,
        resource: "FOLDER" as PermissionResource,
      },
      {
        action: "READ" as PermissionAction,
        resource: "FILE" as PermissionResource,
      },
      {
        action: "READ" as PermissionAction,
        resource: "MEMBER" as PermissionResource,
      },
    ];
  } catch (error) {
    console.error("Error getting user permissions:", error);
    return [];
  }
}
