"use client";

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatNumber, formatDate } from "@/lib/utils";
import { useEffect, useState } from "react";
import {
  useMetrics,
  dateRangeToParams,
} from "@/components/metrics/metrics-context";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

export default function TenantMetricsPage() {
  // State for tenant metrics
  const [tenantMetrics, setTenantMetrics] = useState({
    totalActiveTenants: 0,
    totalTenants: 0,
    newTenants: 0,
    growthRate: 0,
  });

  // State for tenant activity
  const [tenantActivity, setTenantActivity] = useState({
    activeTenants: 0,
    inactiveTenants: 0,
    onboardedTenants: 0,
    onboardingRate: 0,
    churnRate: 0,
    tenantHealthScore: 0,
    activeTenantsCount: 0,
    activityRate: 0,
    canceledSubscriptions: 0,
  });

  // State for tenant distribution by plan
  const [planDistribution, setPlanDistribution] = useState([]);

  // State for tenant distribution by tier
  const [tierDistribution, setTierDistribution] = useState([]);

  // State for trial subscriptions
  const [trialSubscriptions, setTrialSubscriptions] = useState(0);

  // State for new tenants over time
  const [newTenantsOverTime, setNewTenantsOverTime] = useState([]);

  // State for loading
  const [loading, setLoading] = useState(true);

  // Get metrics context
  const { dateRange, setIsLoading } = useMetrics();

  useEffect(() => {
    async function fetchTenantMetrics() {
      try {
        setIsLoading(true);
        setLoading(true);

        // Create date range params
        const dateParams = dateRangeToParams(dateRange);

        // Fetch basic tenant metrics
        const metricsResponse = await fetch(
          `/api/metrics/tenants/count?${dateParams}`
        );
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          setTenantMetrics(metricsData);
        }

        // Fetch tenant activity metrics
        const activityResponse = await fetch(
          `/api/metrics/tenants/activity?${dateParams}`
        );
        if (activityResponse.ok) {
          const activityData = await activityResponse.json();
          setTenantActivity(activityData);
        }

        // Fetch plan distribution
        const plansResponse = await fetch(
          `/api/metrics/tenants/plans?${dateParams}`
        );
        if (plansResponse.ok) {
          const plansData = await plansResponse.json();
          setPlanDistribution(plansData.planDistribution || []);
          setTierDistribution(plansData.tierDistribution || []);
          setTrialSubscriptions(plansData.trialSubscriptions || 0);
        }

        // Fetch new tenants over time
        const timeResponse = await fetch(
          `/api/metrics/tenants/new?period=6months&${dateParams}`
        );
        if (timeResponse.ok) {
          const timeData = await timeResponse.json();
          setNewTenantsOverTime(timeData.monthlyData || []);
        }
      } catch (error) {
        console.error("Error fetching tenant metrics:", error);
      } finally {
        setLoading(false);
        setIsLoading(false);
      }
    }

    fetchTenantMetrics();
  }, [dateRange, setIsLoading]);

  // Colors for the pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  return (
    <div className="space-y-6">
      {/* First row of metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading
                ? "Loading..."
                : formatNumber(tenantMetrics.totalTenants)}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantMetrics.growthRate > 0 ? "+" : ""}
              {tenantMetrics.growthRate}% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Active Tenants
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading
                ? "Loading..."
                : formatNumber(tenantMetrics.totalActiveTenants)}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantMetrics.totalTenants > 0
                ? `${Math.round(
                    (tenantMetrics.totalActiveTenants /
                      tenantMetrics.totalTenants) *
                      100
                  )}% of total`
                : "0% of total"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              New Tenants (30d)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : formatNumber(tenantMetrics.newTenants)}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantMetrics.totalTenants > 0
                ? `${Math.round(
                    (tenantMetrics.newTenants / tenantMetrics.totalTenants) *
                      100
                  )}% growth rate`
                : "0% growth rate"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : `${tenantActivity.churnRate}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {loading
                ? "Loading..."
                : `${tenantActivity.canceledSubscriptions} cancellations in period`}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Second row of metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Tenant Health Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : `${tenantActivity.tenantHealthScore}`}
            </div>
            <p className="text-xs text-muted-foreground">
              Based on activity and onboarding
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Active vs Inactive
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading
                ? "Loading..."
                : `${tenantActivity.activeTenants}/${tenantActivity.inactiveTenants}`}
            </div>
            <p className="text-xs text-muted-foreground">
              Active/Inactive tenants
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Onboarding Completion
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : `${tenantActivity.onboardingRate}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {loading
                ? "Loading..."
                : `${tenantActivity.onboardedTenants} of ${tenantMetrics.totalTenants} tenants`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Activity Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "Loading..." : `${tenantActivity.activityRate}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {loading
                ? "Loading..."
                : `${tenantActivity.activeTenantsCount} active in period`}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts - First Row */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>New Tenants Over Time</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={newTenantsOverTime}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [`${value} tenants`, "New Tenants"]}
                  />
                  <Bar dataKey="count" fill="#8884d8" name="New Tenants" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tenants by Plan</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={planDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {planDistribution.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value} tenants`, "Count"]}
                  />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Charts - Second Row */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Subscription Tier Breakdown</CardTitle>
            <p className="text-sm text-muted-foreground">
              Distribution across free, basic, premium plans
            </p>
          </CardHeader>
          <CardContent className="h-[300px]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <p>Loading chart data...</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={tierDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {tierDistribution.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value} tenants`, "Count"]}
                  />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Trial Subscriptions</CardTitle>
            <p className="text-sm text-muted-foreground">
              Tenants currently on trial period
            </p>
          </CardHeader>
          <CardContent className="h-[300px] flex flex-col items-center justify-center">
            <div className="text-5xl font-bold mb-4">
              {loading ? "Loading..." : formatNumber(trialSubscriptions)}
            </div>
            <p className="text-lg text-muted-foreground">
              {trialSubscriptions === 1 ? "Active Trial" : "Active Trials"}
            </p>
            {tenantMetrics.totalTenants > 0 && trialSubscriptions > 0 && (
              <p className="text-sm text-muted-foreground mt-2">
                {Math.round(
                  (trialSubscriptions / tenantMetrics.totalTenants) * 100
                )}
                % of total tenants
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
