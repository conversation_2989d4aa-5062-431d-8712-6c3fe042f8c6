/**
 * This script initializes the default permissions in the database.
 * Run it with: node scripts/init-permissions.js
 */
import { PrismaClient } from "@prisma/client";

// Cast PrismaClient to our custom interface
const prisma = new PrismaClient() as unknown as PrismaClient;

async function main() {
  console.log("Initializing permissions...");

  // Define all possible permission combinations
  const permissionCombinations: any = [];

  // Define all actions and resources
  const actions = ["CREATE", "READ", "UPDATE", "DELETE"];
  const resources = ["WORKSPACE", "PAGE", "FOLDER", "FILE", "MEMBER"];

  // Generate all combinations
  for (const action of actions) {
    for (const resource of resources) {
      permissionCombinations.push({
        action,
        resource,
        description: `${action.toLowerCase()} ${resource.toLowerCase()}`,
      });
    }
  }

  // Create permissions if they don't exist
  const createdPermissions: any = [];

  for (const perm of permissionCombinations) {
    const existingPermission = await prisma.permission.findFirst({
      where: {
        action: perm.action,
        resource: perm.resource,
      },
    });

    if (!existingPermission) {
      const newPermission = await prisma.permission.create({
        data: {
          action: perm.action?.toUpperCase(),
          resource: perm.resource?.toUpperCase(),
          description: perm.description,
        },
      });
      createdPermissions.push(newPermission);
      console.log(`Created permission: ${perm.action} ${perm.resource}`);
    } else {
      console.log(`Permission already exists: ${perm.action} ${perm.resource}`);
    }
  }

  console.log(`Created ${createdPermissions.length} new permissions`);

  // Create default roles
  const defaultRoles = [
    {
      name: "Content Editor",
      description: "Can create and edit content but cannot manage members",
      permissions: [
        { action: "CREATE", resource: "PAGE" },
        { action: "READ", resource: "PAGE" },
        { action: "UPDATE", resource: "PAGE" },
        { action: "DELETE", resource: "PAGE" },
        { action: "CREATE", resource: "FOLDER" },
        { action: "READ", resource: "FOLDER" },
        { action: "UPDATE", resource: "FOLDER" },
        { action: "DELETE", resource: "FOLDER" },
        { action: "CREATE", resource: "FILE" },
        { action: "READ", resource: "FILE" },
        { action: "UPDATE", resource: "FILE" },
        { action: "DELETE", resource: "FILE" },
        { action: "READ", resource: "WORKSPACE" },
        { action: "READ", resource: "MEMBER" },
      ],
    },
    {
      name: "Viewer",
      description: "Can only view content, cannot edit or create",
      permissions: [
        { action: "READ", resource: "PAGE" },
        { action: "READ", resource: "FOLDER" },
        { action: "READ", resource: "FILE" },
        { action: "READ", resource: "WORKSPACE" },
        { action: "READ", resource: "MEMBER" },
      ],
    },
  ];

  // Get all tenants
  const tenants = await prisma.tenant.findMany();

  // For each tenant, create the default roles if they don't exist
  for (const tenant of tenants) {
    console.log(`Processing tenant: ${tenant.name} (${tenant.id})`);

    for (const roleTemplate of defaultRoles) {
      // Check if role already exists
      const existingRole = await prisma.customRole.findFirst({
        where: {
          name: roleTemplate.name,
          tenantId: tenant.id,
        },
      });

      if (!existingRole) {
        // Create the role
        const newRole = await prisma.customRole.create({
          data: {
            name: roleTemplate.name,
            description: roleTemplate.description,
            tenantId: tenant.id,
            isDefault: true,
          },
        });

        // Get permission IDs
        const permissionIds: any = [];
        for (const perm of roleTemplate.permissions) {
          const permission = await prisma.permission.findFirst({
            where: {
              action: perm.action as any,
              resource: perm.resource as any,
            },
          });

          if (permission) {
            permissionIds.push(permission.id);
          }
        }

        // Create permission connections
        if (permissionIds.length > 0) {
          const permissionConnections = permissionIds.map((permissionId) => ({
            permissionId,
            customRoleId: newRole.id,
          }));

          await prisma.customRolePermission.createMany({
            data: permissionConnections,
          });
        }

        console.log(
          `Created role "${roleTemplate.name}" for tenant ${tenant.name}`
        );
      } else {
        console.log(
          `Role "${roleTemplate.name}" already exists for tenant ${tenant.name}`
        );
      }
    }
  }

  console.log("Initialization complete!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
