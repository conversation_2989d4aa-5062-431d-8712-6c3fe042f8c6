import { apiUrl, fetchJson } from "..";

export const createMessage = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/chat/${data?.chatId}/messages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create Message api calling error": error });
    return { error: "Error Creating The Message" };
  }
};

export const updateMessage = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/chat/${data?.chatId}/messages`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update messages api calling error": error });
    return { error: "Error Update The Message" };
  }
};

export const getMessage = async ({
  tenantId,
  chatId = "",
  id = "",
  page = 1,
  limit = 20,
}) => {
  try {
    if (id) {
      const response = await fetchJson(
        `${apiUrl}/chat/${chatId}/messages?id=${id}&tenantId=${tenantId}`
      );
      return response;
    }
    if (chatId) {
      const response = await fetchJson(
        `${apiUrl}/messages?chatId=${chatId}&tenantId=${tenantId}&page=${page}&limit=${limit}`
      );
      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/messages?tenantId=${tenantId}&page=${page}&limit=${limit}`
    );

    return response;
  } catch (error) {
    console.error("Error in getMessage service:", error);
    throw error;
  }
};

export const deleteMessage = async (id) => {
  try {
    const url = `${apiUrl}/messages?id=${id}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete Message api error": error });
    return { error: error.message || "Error deleting Message" };
  }
};
