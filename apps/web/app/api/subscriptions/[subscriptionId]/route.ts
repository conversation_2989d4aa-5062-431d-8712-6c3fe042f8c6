import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

// Get a specific subscription
export async function GET(
  request: Request,
  { params }: { params: { subscriptionId: string } }
) {
  try {
    const subscriptionId = params.subscriptionId;

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const subscription = await db.subscription.findUnique({
      where: {
        id: subscriptionId,
      },
      include: {
        plan: true,
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: subscription.tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this subscription" },
        { status: 403 }
      );
    }

    return NextResponse.json({ subscription });
  } catch (error) {
    console.error("Error fetching subscription:", error);
    return NextResponse.json(
      { error: "Failed to fetch subscription" },
      { status: 500 }
    );
  }
}

// Update a subscription
export async function PUT(
  request: Request,
  { params }: { params: { subscriptionId: string } }
) {
  try {
    const subscriptionId = params.subscriptionId;

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { planId, additionalUsers, additionalStorageGB, isActive } = body;

    // Get the subscription
    const subscription = await db.subscription.findUnique({
      where: {
        id: subscriptionId,
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: subscription.tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // If changing plan, check if new plan exists and is active
    if (planId && planId !== subscription.planId) {
      const plan = await db.plan.findFirst({
        where: {
          id: planId,
          isActive: true,
        },
      });

      if (!plan) {
        return NextResponse.json(
          { error: "Selected plan not found or inactive" },
          { status: 404 }
        );
      }
    }

    // Update the subscription
    const updateData: any = {};
    if (planId) updateData.planId = planId;
    if (additionalUsers !== undefined)
      updateData.additionalUsers = additionalUsers;
    if (additionalStorageGB !== undefined)
      updateData.additionalStorageGB = additionalStorageGB;
    if (isActive !== undefined) {
      updateData.isActive = isActive;
      if (isActive === false) {
        updateData.endDate = new Date();
      }
    }

    const updatedSubscription = await db.subscription.update({
      where: {
        id: subscriptionId,
      },
      data: updateData,
      include: {
        plan: true,
      },
    });

    return NextResponse.json({ subscription: updatedSubscription });
  } catch (error) {
    console.error("Error updating subscription:", error);
    return NextResponse.json(
      { error: "Failed to update subscription" },
      { status: 500 }
    );
  }
}

// Cancel a subscription
export async function DELETE(
  request: Request,
  { params }: { params: { subscriptionId: string } }
) {
  try {
    const subscriptionId = params.subscriptionId;

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the subscription
    const subscription = await db.subscription.findUnique({
      where: {
        id: subscriptionId,
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: subscription.tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Soft delete by setting isActive to false and setting end date
    const updatedSubscription = await db.subscription.update({
      where: {
        id: subscriptionId,
      },
      data: {
        isActive: false,
        endDate: new Date(),
      },
    });

    return NextResponse.json({
      message: "Subscription successfully cancelled",
      subscription: updatedSubscription,
    });
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    return NextResponse.json(
      { error: "Failed to cancel subscription" },
      { status: 500 }
    );
  }
}
