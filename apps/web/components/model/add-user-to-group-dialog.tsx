"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/lib/language-context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { addUserToGroup } from "@/services/src/group";

interface AddUserToGroupDialogProps {
  groupId: string;
  tenantId?: string;
  trigger: React.ReactNode;
}

export function AddUserToGroupDialog({
  groupId,
  tenantId,
  trigger,
}: AddUserToGroupDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const router = useRouter();
  const { t } = useLanguage();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenantId) {
      toast.error(t("common.error"));
      return;
    }

    try {
      setIsLoading(true);
      const result = await addUserToGroup({
        groupId,
        email,
        tenantId,
      });
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.userAddedToGroup"));
      setIsOpen(false);
      setEmail("");
      router.refresh();
    } catch (error) {
      toast.error(t("groups.failedToAddUserToGroup"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("groups.addUserToGroup")}</DialogTitle>
          <DialogDescription>
            {t("groups.addUserToGroupDescription")}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email">{t("common.email")}</Label>
              <Input
                id="email"
                type="email"
                placeholder={t("inviteMember.emailPlaceholder")}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t("common.adding") : t("common.add")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
