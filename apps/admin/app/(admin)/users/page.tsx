"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";
import { Eye } from "lucide-react";
import Link from "next/link";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

// Mock data - will be replaced with real API calls
const mockUsers = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    tenantName: "Acme Corp",
    tenantId: "1",
    role: "OWNER",
    lastActive: "2023-07-15T14:30:00Z",
    createdAt: "2023-01-15T12:00:00Z",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    tenantName: "Acme Corp",
    tenantId: "1",
    role: "ADMIN",
    lastActive: "2023-07-14T10:15:00Z",
    createdAt: "2023-01-16T09:30:00Z",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    tenantName: "Acme Corp",
    tenantId: "1",
    role: "MEMBER",
    lastActive: "2023-07-13T16:45:00Z",
    createdAt: "2023-01-18T14:20:00Z",
  },
  {
    id: "4",
    name: "Alice Williams",
    email: "<EMAIL>",
    tenantName: "Startup Inc",
    tenantId: "2",
    role: "OWNER",
    lastActive: "2023-07-15T11:30:00Z",
    createdAt: "2023-02-20T14:30:00Z",
  },
  {
    id: "5",
    name: "Charlie Brown",
    email: "<EMAIL>",
    tenantName: "Tech Solutions",
    tenantId: "3",
    role: "OWNER",
    lastActive: "2023-07-14T09:45:00Z",
    createdAt: "2023-03-10T09:15:00Z",
  },
];

const mockUsersByTenant = [
  { name: "Acme Corp", users: 25 },
  { name: "Startup Inc", users: 8 },
  { name: "Tech Solutions", users: 15 },
  { name: "Global Services", users: 3 },
  { name: "Digital Innovations", users: 12 },
];

const mockUsersByRole = [
  { name: "OWNER", users: 5 },
  { name: "ADMIN", users: 12 },
  { name: "MEMBER", users: 46 },
];

export default function UsersPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Users</h1>
        <p className="text-muted-foreground">
          Manage all users across all tenants
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Users by Tenant</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={mockUsersByTenant}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => `${value} users`} />
                <Bar dataKey="users" name="Number of Users" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Users by Role</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={mockUsersByRole}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => `${value} users`} />
                <Bar dataKey="users" name="Number of Users" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left font-medium">Name</th>
                  <th className="px-4 py-3 text-left font-medium">Email</th>
                  <th className="px-4 py-3 text-left font-medium">Tenant</th>
                  <th className="px-4 py-3 text-left font-medium">Role</th>
                  <th className="px-4 py-3 text-left font-medium">Created</th>
                  <th className="px-4 py-3 text-left font-medium">Last Active</th>
                  <th className="px-4 py-3 text-right font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {mockUsers.map((user) => (
                  <tr key={user.id} className="border-b">
                    <td className="px-4 py-3">{user.name}</td>
                    <td className="px-4 py-3">{user.email}</td>
                    <td className="px-4 py-3">{user.tenantName}</td>
                    <td className="px-4 py-3">{user.role}</td>
                    <td className="px-4 py-3">{formatDate(user.createdAt)}</td>
                    <td className="px-4 py-3">{formatDate(user.lastActive)}</td>
                    <td className="px-4 py-3 text-right">
                      <Link href={`/users/${user.id}`}>
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View</span>
                        </Button>
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
