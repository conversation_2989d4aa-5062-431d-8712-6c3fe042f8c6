"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { UsageChart } from "@/components/dashboard/usage-chart";
import { formatCurrency, formatNumber } from "@/lib/utils";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

// Mock data - will be replaced with real API calls
const mockMonthlyTokenUsage = [
  { name: "Jan", value: 4000 },
  { name: "Feb", value: 3000 },
  { name: "Mar", value: 2000 },
  { name: "Apr", value: 2780 },
  { name: "May", value: 1890 },
  { name: "Jun", value: 2390 },
  { name: "Jul", value: 3490 },
];

const mockModelUsage = [
  { name: "gpt-4", value: 45 },
  { name: "gpt-3.5-turbo", value: 30 },
  { name: "embed-v-4-0", value: 15 },
  { name: "deepseek-coder", value: 10 },
];

const mockTenantTokenUsage = [
  { name: "Acme Corp", input: 12000, output: 8000 },
  { name: "Startup Inc", input: 8000, output: 5000 },
  { name: "Tech Solutions", input: 10000, output: 7000 },
  { name: "Global Services", input: 5000, output: 3000 },
  { name: "Digital Innovations", input: 9000, output: 6000 },
];

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

export default function TokensPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Token Usage</h1>
        <p className="text-muted-foreground">
          Monitor token usage across all tenants
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <UsageChart
          title="Monthly Token Usage"
          data={mockMonthlyTokenUsage}
          dataKey="value"
          valueFormatter={(value) => formatNumber(value)}
        />

        <Card>
          <CardHeader>
            <CardTitle>Model Usage Distribution</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={mockModelUsage}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name}: ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockModelUsage.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value}%`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Token Usage by Tenant</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={mockTenantTokenUsage}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value) => formatNumber(Number(value))} />
              <Legend />
              <Bar dataKey="input" name="Input Tokens" fill="#8884d8" />
              <Bar dataKey="output" name="Output Tokens" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Cost Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left font-medium">Tenant</th>
                  <th className="px-4 py-3 text-left font-medium">
                    Input Tokens
                  </th>
                  <th className="px-4 py-3 text-left font-medium">
                    Output Tokens
                  </th>
                  <th className="px-4 py-3 text-left font-medium">
                    Total Tokens
                  </th>
                  <th className="px-4 py-3 text-left font-medium">
                    Estimated Cost
                  </th>
                </tr>
              </thead>
              <tbody>
                {mockTenantTokenUsage.map((tenant, index) => {
                  const totalTokens = tenant.input + tenant.output;
                  // Simplified cost calculation
                  const cost =
                    (tenant.input * 0.0015 + tenant.output * 0.002) / 1000;

                  return (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-3">{tenant.name}</td>
                      <td className="px-4 py-3">
                        {formatNumber(tenant.input)}
                      </td>
                      <td className="px-4 py-3">
                        {formatNumber(tenant.output)}
                      </td>
                      <td className="px-4 py-3">{formatNumber(totalTokens)}</td>
                      <td className="px-4 py-3">{formatCurrency(cost)}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
