"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { GlobalSearchDialog } from "./global-search-dialog";

interface GlobalSearchContextType {
  isOpen: boolean;
  openSearch: () => void;
  closeSearch: () => void;
  toggleSearch: () => void;
}

const GlobalSearchContext = createContext<GlobalSearchContextType | undefined>(undefined);

export const useGlobalSearch = () => {
  const context = useContext(GlobalSearchContext);
  if (!context) {
    throw new Error("useGlobalSearch must be used within a GlobalSearchProvider");
  }
  return context;
};

interface GlobalSearchProviderProps {
  children: React.ReactNode;
  tenantId: string;
}

export const GlobalSearchProvider: React.FC<GlobalSearchProviderProps> = ({
  children,
  tenantId,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const openSearch = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeSearch = useCallback(() => {
    setIsOpen(false);
  }, []);

  const toggleSearch = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // Keyboard shortcut handler
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Cmd+K or Ctrl+K to open search
      if ((event.metaKey || event.ctrlKey) && event.key === "k") {
        event.preventDefault();
        toggleSearch();
      }
      // Escape to close search
      if (event.key === "Escape" && isOpen) {
        closeSearch();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, toggleSearch, closeSearch]);

  const value: GlobalSearchContextType = {
    isOpen,
    openSearch,
    closeSearch,
    toggleSearch,
  };

  return (
    <GlobalSearchContext.Provider value={value}>
      {children}
      <GlobalSearchDialog
        open={isOpen}
        onOpenChange={setIsOpen}
        tenantId={tenantId}
      />
    </GlobalSearchContext.Provider>
  );
};
