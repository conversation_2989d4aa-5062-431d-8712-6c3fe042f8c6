---
title: "Administrator Guide"
description: "Comprehensive guide for Swiss Knowledge Hub administrators"
---

# Administrator Guide

This guide provides detailed information for administrators of Swiss Knowledge Hub, covering user management, billing, settings, and system configuration.

## Administrator Dashboard

As an administrator, you have access to additional features and settings:

1. **Admin Dashboard**: Access by clicking "Dashboard" in the main navigation
2. **Admin Controls**: Located in the "Settings" section
3. **System Metrics**: View usage statistics and performance metrics

## User Management

### User Roles and Permissions

Swiss Knowledge Hub has several user roles with different permissions:

| Role | Scope | Capabilities |
|------|-------|-------------|
| **System Admin** | Entire system | Full access to all features and settings |
| **Workspace Admin** | Specific workspace | Manage workspace settings and members |
| **Editor** | Specific workspace | Upload and manage documents, create chats |
| **Viewer** | Specific workspace | View documents and participate in chats |

### Managing Users

To manage users in your organization:

1. Navigate to "Settings" > "User Management"
2. View all users in your organization
3. Filter users by status, role, or workspace
4. Perform actions:
   - Edit user details
   - Change user roles
   - Disable/enable user accounts
   - Delete users (use with caution)

### User Onboarding

Streamline the onboarding process:

1. Create email templates for new users
2. Set default roles and permissions
3. Configure automatic workspace assignments
4. Enable or disable email verification requirements

## Subscription and Billing

### Subscription Management

Manage your organization's subscription:

1. Go to "Settings" > "Billing"
2. View current subscription details
3. Upgrade or downgrade your plan
4. Update payment information
5. View billing history and download invoices

### User Allocation

Manage seats for your subscription:

1. View total available seats
2. See currently assigned seats
3. Add additional seats as needed
4. Remove unused seats to optimize costs

### Usage Monitoring

Track resource usage across your organization:

1. Document storage utilization
2. Vector database usage
3. API call volume
4. User activity metrics

## System Configuration

### AI Provider Settings

Configure AI integration settings:

1. Navigate to "Settings" > "AI Configuration"
2. Select your preferred AI provider:
   - OpenAI
   - Azure OpenAI
3. Enter API keys and configuration details
4. Set default models for different functions
5. Configure rate limits and usage alerts

### Vector Database Configuration

Set up and manage vector database settings:

1. Go to "Settings" > "Vector Database"
2. Choose your vector store provider:
   - ChromaDB
   - LanceDB
   - Pinecone
3. Configure connection details
4. Set indexing parameters
5. Monitor performance metrics

### Storage Configuration

Manage document storage settings:

1. Navigate to "Settings" > "Storage"
2. Configure blob storage connection
3. Set document processing parameters
4. Define retention policies
5. Configure backup settings

## Security Settings

### Authentication Settings

Configure authentication methods:

1. Password policies (complexity, expiration)
2. Multi-factor authentication requirements
3. Single sign-on (SSO) integration
4. Session timeout settings

### Access Control

Manage system access:

1. IP restrictions
2. Login attempt limitations
3. Automated account locking
4. Session management

### Audit Logging

Monitor system activity:

1. User login/logout events
2. Document access and modifications
3. Settings changes
4. Administrative actions
5. Export audit logs for compliance

## System Maintenance

### Performance Monitoring

Keep your system running smoothly:

1. Monitor system health metrics
2. Track response times
3. Identify bottlenecks
4. Set up alerts for performance issues

### Backup and Recovery

Protect your data:

1. Configure automated backups
2. Set backup frequency and retention
3. Test restoration procedures
4. Document disaster recovery plans

## Best Practices for Administrators

1. **Regular audits**: Review user accounts and permissions quarterly
2. **Usage monitoring**: Track resource utilization to optimize costs
3. **Security reviews**: Regularly update security settings and policies
4. **Documentation**: Maintain internal documentation for system configuration
5. **User training**: Provide training resources for workspace administrators
6. **Feedback collection**: Gather user feedback to improve system configuration
