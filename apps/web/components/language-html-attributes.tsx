'use client';

import { useEffect } from 'react';
import { useLanguage } from '@/lib/language-context';

/**
 * This component updates the HTML lang attribute based on the selected language
 */
export default function LanguageHtmlAttributes() {
  const { language } = useLanguage();
  
  useEffect(() => {
    // Update HTML lang attribute when language changes
    document.documentElement.lang = language;
  }, [language]);
  
  // This component doesn't render anything
  return null;
}