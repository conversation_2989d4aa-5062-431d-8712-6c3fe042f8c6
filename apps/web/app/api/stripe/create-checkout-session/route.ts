import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import Stripe from "stripe";
import db from "@/lib/shared-db";

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2023-10-16" as any,
});

export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const {
      planId,
      tenantId,
      userId,
      successUrl,
      cancelUrl,
      additionalUsers = 0,
      additionalStorageGB = 0,
      discountCode,
      billingInterval = "month",
      stripePriceId, // Direct Stripe price ID for the plan
      stripeUserPriceId, // Direct Stripe price ID for additional users
      stripeStoragePriceId, // Direct Stripe price ID for storage
    } = body;

    // Validate required fields
    if (!planId || !tenantId || !successUrl || !cancelUrl) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Get the plan details
    const plan = await db.plan.findUnique({
      where: {
        id: planId,
      },
    });

    if (!plan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Get or create Stripe customer for the tenant
    let stripeCustomerId: string;
    const tenant = await db.tenant.findUnique({
      where: {
        id: tenantId,
      },
      select: {
        id: true,
        name: true,
        stripeCustomerId: true,
      },
    });

    if (!tenant) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    if (tenant.stripeCustomerId) {
      stripeCustomerId = tenant.stripeCustomerId;
    } else {
      // Create a new Stripe customer
      const customer = await stripe.customers.create({
        name: tenant.name,
        metadata: {
          tenantId: tenant.id,
        },
      });

      // Update tenant with Stripe customer ID
      await db.tenant.update({
        where: {
          id: tenantId,
        },
        data: {
          stripeCustomerId: customer.id,
        },
      });

      stripeCustomerId = customer.id;
    }

    // Create line items array
    const lineItems: any[] = [];

    // Add base plan with its price
    if (plan.type !== "CUSTOM") {
      // If a direct Stripe price ID was provided, use it
      if (stripePriceId) {
        lineItems.push({
          price: stripePriceId,
          quantity: 1,
        });
      } else {
        // Otherwise, use the appropriate Stripe Price ID based on the billing interval
        const planPriceId =
          billingInterval === "year"
            ? plan.stripeYearlyPriceId
            : plan.stripePriceId;

        if (!planPriceId) {
          return NextResponse.json(
            {
              error: `No Stripe price ID found for ${plan.name} plan with ${billingInterval} billing interval`,
            },
            { status: 400 }
          );
        }

        lineItems.push({
          price: planPriceId,
          quantity: 1,
        });
      }
    } else {
      // For custom plans, redirect to contact page
      return NextResponse.json(
        { error: "Custom plans require contacting sales" },
        { status: 400 }
      );
    }

    // Add additional users if any
    if (additionalUsers > 0) {
      // If a direct Stripe price ID was provided for users, use it
      if (stripeUserPriceId) {
        lineItems.push({
          price: stripeUserPriceId,
          quantity: additionalUsers,
        });
      } else {
        // Otherwise, use the appropriate Stripe Price ID for additional users based on the billing interval
        const userPriceId =
          billingInterval === "year"
            ? plan.stripeUserYearlyPriceId
            : plan.stripeUserPriceId;

        if (!userPriceId) {
          return NextResponse.json(
            {
              error: `No Stripe price ID found for additional users with ${billingInterval} billing interval`,
            },
            { status: 400 }
          );
        }

        lineItems.push({
          price: userPriceId,
          quantity: additionalUsers,
        });
      }
    }

    // Add additional storage if any
    if (additionalStorageGB > 0) {
      // If a direct Stripe price ID was provided for storage, use it
      if (stripeStoragePriceId) {
        lineItems.push({
          price: stripeStoragePriceId,
          quantity: 1, // Always 1 for storage tiers
        });
      } else {
        // Find the appropriate storage tier
        let storageTier: any;

        if (additionalStorageGB <= 10) {
          storageTier = await db.storageTier.findFirst({
            where: { name: "10GB", isActive: true },
          });
        } else if (additionalStorageGB <= 50) {
          storageTier = await db.storageTier.findFirst({
            where: { name: "50GB", isActive: true },
          });
        } else {
          storageTier = await db.storageTier.findFirst({
            where: { name: "100GB", isActive: true },
          });
        }

        if (!storageTier) {
          return NextResponse.json(
            { error: "No storage tier found for the requested size" },
            { status: 400 }
          );
        }

        // Use the appropriate Stripe Price ID based on the billing interval
        const storagePriceId =
          billingInterval === "year"
            ? storageTier.stripeYearlyPriceId
            : storageTier.stripePriceId;

        if (!storagePriceId) {
          return NextResponse.json(
            {
              error: `No Stripe price ID found for storage tier ${storageTier.name} with ${billingInterval} billing interval`,
            },
            { status: 400 }
          );
        }

        lineItems.push({
          price: storagePriceId,
          quantity: 1, // Always 1 for storage tiers
        });
      }
    }
    const promotionCodes = await stripe.promotionCodes.list({
      active: true,
    });
    const discount = promotionCodes?.data?.find(
      (promotionCode) =>
        promotionCode?.metadata?.onlyForYearly === "true" ||
        promotionCode?.coupon?.metadata?.onlyForYearly === "true"
    );

    // Create checkout session options
    const sessionOptions: Stripe.Checkout.SessionCreateParams = {
      customer: stripeCustomerId,
      payment_method_types: ["card"],
      line_items: lineItems,
      mode: "subscription",
      success_url: successUrl,
      cancel_url: cancelUrl,
      ...(billingInterval === "year" ? {} : { allow_promotion_codes: true }), // Enable promotion code field in checkout
      subscription_data: {
        trial_period_days: 14,
        metadata: {
          tenantId,
          planId,
          additionalUsers: additionalUsers.toString(),
          additionalStorageGB: additionalStorageGB.toString(),
          userId,
          billingInterval,
          unified: "true", // Flag to indicate this is a unified subscription
        },
      },
      discounts:
        billingInterval === "year" && discount?.id
          ? [
              {
                promotion_code: discount?.id,
              },
            ]
          : [],
      metadata: {
        tenantId,
        planId,
        additionalUsers: additionalUsers.toString(),
        additionalStorageGB: additionalStorageGB.toString(),
        userId,
        billingInterval,
        unified: "true", // Flag to indicate this is a unified subscription
      },
    };
    // Create a Stripe checkout session
    const session = await stripe.checkout.sessions.create(sessionOptions);

    return NextResponse.json({ session });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    return NextResponse.json(
      { error: "Failed to create checkout session" },
      { status: 500 }
    );
  }
}
