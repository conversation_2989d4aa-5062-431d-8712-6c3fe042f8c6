import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { name, tenantId } = await req.json();
    if (!name) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    const chatGroup = await db.chatGroup.create({
      data: {
        name,
        userId: session?.userId,
        tenantId,
      },
    });

    return NextResponse.json(chatGroup);
  } catch (error) {
    console.error("[CHAT_GROUP_POST]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const tenantId = searchParams.get("tenantId");

    const chatGroups = await db.chatGroup.findMany({
      where: {
        userId,
        tenantId,
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        chats: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    return NextResponse.json(chatGroups);
  } catch (error) {
    console.error("[CHAT_GROUP_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
