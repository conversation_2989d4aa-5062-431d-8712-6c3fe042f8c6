"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";
import { addDays } from "date-fns";

interface DateRange {
  from: Date;
  to: Date;
}

interface MetricsContextType {
  dateRange: DateRange;
  setDateRange: (range: DateRange) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const MetricsContext = createContext<MetricsContextType | undefined>(undefined);

export function MetricsProvider({ children }: { children: ReactNode }) {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: addDays(new Date(), -30),
    to: new Date(),
  });
  const [isLoading, setIsLoading] = useState(false);

  return (
    <MetricsContext.Provider
      value={{
        dateRange,
        setDateRange,
        isLoading,
        setIsLoading,
      }}
    >
      {children}
    </MetricsContext.Provider>
  );
}

export function useMetrics() {
  const context = useContext(MetricsContext);
  if (context === undefined) {
    throw new Error("useMetrics must be used within a MetricsProvider");
  }
  return context;
}

// Helper function to convert date range to API parameters
export function dateRangeToParams(dateRange: DateRange): string {
  const { from, to } = dateRange;
  return `from=${from.toISOString()}&to=${to.toISOString()}`;
}

// Helper function to format date range for display
export function formatDateRange(dateRange: DateRange): string {
  const { from, to } = dateRange;
  return `${from.toLocaleDateString()} - ${to.toLocaleDateString()}`;
}
