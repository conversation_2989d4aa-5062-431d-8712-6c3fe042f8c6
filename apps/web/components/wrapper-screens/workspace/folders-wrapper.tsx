"use client";

import React from "react";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import {
  Folder,
  File,
  Upload,
  Plus,
  FolderPlus,
  ChevronLeft,
  Search,
  List,
  LayoutGrid,
  LayoutList,
  Globe,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { CreateFolderDialog } from "./page-wrapper";
import MoreButton from "./more-button";
import { CreateGuard } from "@/components/permission-guard";
import { useFolderOperations } from "@/hooks/use-folder-operations";
import { useFileOperations } from "@/hooks/use-file-operations";
import { useLanguage } from "@/lib/language-context";
import { fetchWorkspaceBySlugWithCache } from "@/utils/workspace";
import { UrlImportButton } from "@/components/url-import/url-import-button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface FolderItem {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  workspaceId: string;
  pageId?: string;
  files: FileItem[];
  parentRelations: {
    parent: FolderItem;
  }[];
  childRelations: {
    child: FolderItem;
  }[];
}

interface FileItem {
  id: string;
  name: string;
  type: string;
  extension?: string;
  size?: number;
  url?: string;
  content?: string;
  vectorizationStatus?: string;
  vectorizedAt?: string;
  createdAt: string;
  updatedAt: string;
  workspaceId: string;
  pageId?: string;
  folderId?: string;
}

interface FolderPageProps {
  currentFolder: any;
  folderId: string;
  workspaceSlug: string;
  tenantId: string;
  pageId: string;
  usageSummary: any;
}

export default function FolderPage({
  currentFolder,
  folderId,
  workspaceSlug,
  tenantId,
  pageId,
  usageSummary,
}: FolderPageProps) {
  const router = useRouter();
  const { t } = useLanguage();
  const [workspaceTitle, setWorkspaceTitle] = useState("");
  const [pageTitle, setPageTitle] = useState("");

  // Fetch the workspace name when component mounts
  useEffect(() => {
    const getWorkspaceName = async () => {
      try {
        const workspace = await fetchWorkspaceBySlugWithCache(workspaceSlug);
        if (workspace && workspace.name) {
          setWorkspaceTitle(workspace.name);
          setPageTitle(
            workspace?.pages?.find((p: any) => p.id === pageId)?.name
          );
        } else {
          // Fallback to formatting the slug if workspace data is not available
          const formattedTitle = workspaceSlug
            .split("-")
            .slice(0, -1)
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
          setWorkspaceTitle(formattedTitle);
        }
      } catch (error) {
        console.error("Error fetching workspace name:", error);
        // Fallback to formatting the slug
        const formattedTitle = workspaceSlug
          .split("-")
          .slice(0, -1)
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        setWorkspaceTitle(formattedTitle);
      }
    };

    getWorkspaceName();
  }, [workspaceSlug]);

  const [newFolderName, setNewFolderName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState<string | null>(
    null
  );
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [updateData, setUpdateData] = useState<any>({});
  const [viewMode, setViewMode] = useState<"tiles" | "grid" | "list">("tiles");

  // Data states
  const [folderPath, setFolderPath] = useState<any[]>([]);
  const [childFolders, setChildFolders] = useState<any[]>([]);
  const [folderFiles, setFolderFiles] = useState<any[]>([]);
  const isSyncedWithCloud =
    currentFolder?.gDriveFolderId || currentFolder?.oneDriveFolderId;
  const syncProvider =
    currentFolder?.gDriveFolderId && currentFolder?.oneDriveFolderId
      ? "both Google Drive and OneDrive"
      : currentFolder?.gDriveFolderId
        ? "Google Drive"
        : currentFolder?.oneDriveFolderId
          ? "OneDrive"
          : null;

  // Process folder data when component mounts or currentFolder changes
  useEffect(() => {
    if (!currentFolder) {
      return;
    }

    try {
      // Extract child folders from childRelations
      let folders = [];
      if (
        currentFolder.parentRelations &&
        Array.isArray(currentFolder.parentRelations)
      ) {
        folders = currentFolder.parentRelations
          .filter((relation: any) => relation && relation.child)
          .map((relation: any) => relation.child);
      } else {
        console.log("No child relations found or not in expected format");
      }
      setChildFolders(folders);

      // Extract files
      const files = Array.isArray(currentFolder.files)
        ? currentFolder.files
        : [];
      setFolderFiles(files);

      // Build folder path (breadcrumbs)
      // For this we need to process parent relations
      const getCurrentFolderPath = (
        hierarchy: any,
        currentFolderId: string
      ) => {
        const folderMap = new Map();
        hierarchy.forEach((item: any) => {
          folderMap.set(item.child.id, {
            id: item.child.id,
            name: item.child.name,
            parentId: item.parentId,
          });
          if (!folderMap.has(item.parentId)) {
            folderMap.set(item.parentId, {
              id: item.parentId,
              name: item.parent?.name || "",
              parentId: null,
            });
          }
        });

        const path: any = [];
        let currentId = currentFolderId;
        while (currentId && folderMap.has(currentId)) {
          const folder = folderMap.get(currentId);
          path.unshift({ id: folder.id, name: folder.name });
          currentId = folder.parentId;
        }

        return path;
      };

      setFolderPath(getCurrentFolderPath(currentFolder?.hierarchy, folderId));
    } catch (error) {
      console.error("Error processing folder data:", error);
    }
  }, [currentFolder]);

  const { handleCreateFolder, handleRenameFolder, handleDeleteFolder } =
    useFolderOperations({
      workspaceSlug,
      tenantId,
      pageId: pageId as string,
      setIsLoading,
    });

  const { handleFileUpload, handleRenameFile, handleDeleteFile } =
    useFileOperations({
      workspaceSlug,
      tenantId,
      pageId: pageId as string,
      setIsLoading,
      usageSummary,
    });

  // Create a new folder
  const handleCreateFolderClick = async () => {
    // Create folder as a child of the current folder
    if (await handleCreateFolder(newFolderName, folderId)) {
      setIsCreateFolderOpen(null);
      setNewFolderName("");
    }
  };

  const handleRename = async () => {
    if (updateData?.type === "folder") {
      if (await handleRenameFolder(updateData.id, newFolderName)) {
        setNewFolderName("");
        setUpdateData(null);
        setIsCreateFolderOpen(null);
      }
    } else {
      if (await handleRenameFile(updateData?.id, newFolderName)) {
        setNewFolderName("");
        setUpdateData(null);
        setIsCreateFolderOpen(null);
      }
    }
  };

  // Handle file upload
  const handleFileUploadChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileUpload(e, folderId, folderFiles, setFolderFiles, setIsUploadOpen);
  };

  // Delete an item
  const handleDelete = async (id: string, type: "folder" | "file") => {
    if (type === "folder") {
      await handleDeleteFolder(id);
    } else {
      await handleDeleteFile(id);
    }
  };

  // Filter items based on search term
  const filteredChildFolders = childFolders.filter((folder: any) =>
    folder.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredFiles = folderFiles.filter(
    (file: any) =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.extension?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get file icon based on extension
  const getFileIcon = (extension?: string) => {
    switch (extension?.toLowerCase()) {
      case "pdf":
        return <File className="h-6 w-6 text-red-500" />;
      case "doc":
      case "docx":
        return <File className="h-6 w-6 text-blue-500" />;
      case "xls":
      case "xlsx":
        return <File className="h-6 w-6 text-green-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <File className="h-6 w-6 text-purple-500" />;
      default:
        return <File className="h-6 w-6 text-gray-500" />;
    }
  };

  // Get vectorization status badge
  const getVectorizationStatusBadge = (status?: string) => {
    console.log("Status:", status);

    switch (status) {
      case "PENDING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Pending
          </span>
        );
      case "PROCESSING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            Processing
          </span>
        );
      case "COMPLETED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Indexed
          </span>
        );
      case "FAILED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Failed
          </span>
        );
      default:
        return null;
    }
  };

  if (!currentFolder) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">
            {t("workspace.loadingFolder")}
          </h2>
          <p className="text-muted-foreground">
            {t("workspace.folderLoadingError")}
          </p>
          <Button
            className="mt-4"
            onClick={() =>
              router.push(
                `/workspace/${workspaceSlug}${pageId ? `?page=${pageId}` : ""}`
              )
            }
          >
            {t("workspace.returnToWorkspace")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full px-1 py-2 md:px-4">
      {/* Header with title */}
      <div className="flex items-center mb-6 justify-between ">
        <div className="">
          <h1 className="text-2xl font-bold md:text-3xl tracking-tight">
            {currentFolder.name}
          </h1>
          <div className="flex items-center flex-wrap gap-1 text-sm text-muted-foreground mt-1">
            <Link
              href={`/workspace/${workspaceSlug}${
                pageId ? `?page=${pageId}` : ""
              }`}
              className="hover:text-foreground hover:underline transition-colors"
            >
              {workspaceTitle} {t("workspace.workspace")}
            </Link>
            <Link
              href={`/workspace/${workspaceSlug}/page/${pageId}${
                pageId ? `?page=${pageId}` : ""
              }`}
              className="hover:text-foreground hover:underline transition-colors"
            >
              {pageTitle && <span> &gt; {pageTitle}</span>}
            </Link>
            {folderPath.length > 0 && (
              <>
                {folderPath.map((pathItem: any) => (
                  <React.Fragment key={pathItem.id}>
                    <span>&gt;</span>
                    <Link
                      href={`/workspace/${workspaceSlug}/folder/${pathItem.id}?page=${pageId}`}
                      className="hover:text-foreground hover:underline transition-colors"
                    >
                      {pathItem.name}
                    </Link>
                  </React.Fragment>
                ))}
              </>
            )}
          </div>
        </div>
        <Button
          onClick={() => router.push(`/workspace/${workspaceSlug}/members`)}
        >
          <Plus className="h-4 w-4 mr-2" />
          {t("workspace.addRemoveMember")}
        </Button>
      </div>
      {/* Cloud sync notification banner */}
      {isSyncedWithCloud && (
        <div className="mb-6 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 p-4 rounded-md">
          <div className="flex items-start">
            <div className="flex-shrink-0 pt-0.5">
              <svg
                className="h-5 w-5 text-blue-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {t("workspace.syncedWithCloud", { provider: syncProvider })}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                <span className="font-semibold">
                  {t("integration.syncDisclaimer")}
                </span>
              </p>
              {currentFolder?.gDriveFolderName && (
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  <span className="font-semibold">Google Drive path:</span>{" "}
                  {currentFolder.gDriveFolderName}
                </p>
              )}
              {currentFolder?.oneDriveFolderName && (
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  <span className="font-semibold">SharePoint path:</span>{" "}
                  {currentFolder.oneDriveFolderName}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Command bar */}
      <div className="flex flex-col md:flex-row justify-between gap-3 mb-6 bg-muted/30 p-3 rounded-md">
        <div className="flex flex-wrap gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="gap-1"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4" />
            {t("common.back")}
          </Button>

          {/* + New button */}
          <CreateGuard resource="FOLDER">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  disabled={isSyncedWithCloud}
                  variant="default"
                  size="sm"
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {t("workspace.new")}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  disabled={isSyncedWithCloud}
                  onClick={() => setIsCreateFolderOpen("create")}
                >
                  <FolderPlus className="h-4 w-4 mr-2" />
                  {t("workspace.newFolder")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CreateGuard>

          {/* Upload button */}
          <CreateGuard resource="FILE">
            <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
              <DialogTrigger asChild>
                <Button
                  disabled={isSyncedWithCloud}
                  variant="outline"
                  size="sm"
                  className="gap-2"
                >
                  <Upload className="h-4 w-4" />
                  {t("workspace.upload")}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t("workspace.uploadFiles")}</DialogTitle>
                  <DialogDescription>
                    {t("workspace.uploadToFolder")}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <Input
                    type="file"
                    multiple
                    onChange={handleFileUploadChange}
                    className="cursor-pointer"
                  />
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsUploadOpen(false)}
                  >
                    {t("common.cancel")}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CreateGuard>

          {/* URL Import button - disabled when synced with cloud */}
          <UrlImportButton
            workspaceSlug={workspaceSlug}
            workspaceId={currentFolder.workspaceId}
            pageId={pageId}
            folderId={folderId}
            disabled={isSyncedWithCloud}
          />
        </div>

        <div className="flex gap-3 items-center">
          {/* Search */}
          <div className="relative">
            <Input
              type="search"
              placeholder={t("workspace.searchInFolder")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-9 max-w-64 min-w-40 pr-8"
            />
            <Search className="h-4 w-4 absolute right-3 top-2.5 text-muted-foreground" />
          </div>

          {/* View options */}
          <div className="flex gap-1 items-center">
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "list"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.listView")}
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "grid"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.gridView")}
              onClick={() => setViewMode("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "tiles"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.tileView")}
              onClick={() => setViewMode("tiles")}
            >
              <LayoutList className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* All items (folders and files) */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">{t("workspace.allItems")}</h2>
          {filteredChildFolders.length + filteredFiles.length > 0 && (
            <p className="text-sm text-muted-foreground">
              {filteredChildFolders.length === 1
                ? t("workspace.folderCount", {
                    count: filteredChildFolders.length,
                  })
                : t("workspace.foldersCount", {
                    count: filteredChildFolders.length,
                  })}
              ,{" "}
              {filteredFiles.length === 1
                ? t("workspace.fileCount", { count: filteredFiles.length })
                : t("workspace.filesCount", { count: filteredFiles.length })}
            </p>
          )}
        </div>

        {/* List View */}
        {viewMode === "list" && (
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="text-left px-4 py-2 font-medium text-sm">
                    {t("workspace.name")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm hidden md:table-cell">
                    {t("workspace.modified")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm hidden lg:table-cell">
                    {t("workspace.type")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm hidden md:table-cell">
                    {t("workspace.size")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm w-20">
                    {t("workspace.actions")}
                  </th>
                </tr>
              </thead>
              <tbody>
                {/* Folders first */}
                {filteredChildFolders.map((folder: any) => (
                  <tr key={folder.id} className="border-t hover:bg-muted/20">
                    <td className="px-4 py-2">
                      <Link
                        href={`/workspace/${workspaceSlug}/folder/${folder.id}?page=${pageId}`}
                        className="flex items-center gap-2 hover:underline"
                      >
                        <Folder className="h-5 w-5 text-blue-500 shrink-0" />
                        <span className="truncate font-medium">
                          {folder.name}
                        </span>
                      </Link>
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      {new Date(
                        folder.updatedAt || folder.createdAt
                      ).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden lg:table-cell">
                      {t("workspace.folder")}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      —
                    </td>
                    <td className="px-4 py-2 w-20">
                      <MoreButton
                        handleDelete={handleDelete}
                        file={folder}
                        setIsCreateFolderOpen={setIsCreateFolderOpen}
                        type="folder"
                        setNewFolderName={setNewFolderName}
                        setUpdateData={setUpdateData}
                        isSyncedWithCloud={isSyncedWithCloud}
                      />
                    </td>
                  </tr>
                ))}

                {/* Then files */}
                {filteredFiles.map((file: any) => (
                  <tr key={file.id} className="border-t hover:bg-muted/20">
                    <td className="px-4 py-2">
                      <Link
                        href={`/workspace/${workspaceSlug}/file/${file.id}?page=${pageId}`}
                        className="flex items-center gap-2 hover:underline"
                      >
                        {getFileIcon(file.extension)}
                        <div>
                          <span className="truncate font-medium">
                            {file.name}
                          </span>
                          <div className="mt-1">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                {getVectorizationStatusBadge(
                                  file?.vectorizationStatus
                                )}
                              </TooltipTrigger>
                              <TooltipContent>
                                {t("workspace.vectorizationStatusTooltip")}
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      {new Date(
                        file.updatedAt || file.createdAt
                      ).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden lg:table-cell">
                      {file.extension ? `.${file.extension}` : "File"}
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      {file.size}
                    </td>
                    <td className="px-4 py-2 w-20">
                      <MoreButton
                        handleDelete={handleDelete}
                        file={file}
                        setIsCreateFolderOpen={setIsCreateFolderOpen}
                        type="file"
                        setNewFolderName={setNewFolderName}
                        setUpdateData={setUpdateData}
                        isSyncedWithCloud={isSyncedWithCloud}
                      />
                    </td>
                  </tr>
                ))}

                {filteredChildFolders.length === 0 &&
                  filteredFiles.length === 0 && (
                    <tr>
                      <td
                        colSpan={5}
                        className="px-4 py-8 text-center text-muted-foreground"
                      >
                        {t("workspace.noItemsFound")}
                      </td>
                    </tr>
                  )}
              </tbody>
            </table>
          </div>
        )}

        {/* Grid View */}
        {viewMode === "grid" && (
          <div>
            {filteredChildFolders.length > 0 || filteredFiles.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                {/* Folders first */}
                {filteredChildFolders.map((folder: any) => (
                  <Link
                    key={folder.id}
                    href={`/workspace/${workspaceSlug}/folder/${folder.id}?page=${pageId}`}
                    className="group relative bg-card hover:bg-muted/20 rounded-md border p-3 flex flex-col items-center text-center hover:underline cursor-pointer"
                  >
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <MoreButton
                        handleDelete={handleDelete}
                        file={folder}
                        setIsCreateFolderOpen={setIsCreateFolderOpen}
                        type="folder"
                        setNewFolderName={setNewFolderName}
                        setUpdateData={setUpdateData}
                        isSyncedWithCloud={isSyncedWithCloud}
                      />
                    </div>
                    <Folder className="h-12 w-12 text-blue-500 mb-2" />
                    <span className="text-sm font-medium truncate max-w-full">
                      {folder.name}
                    </span>
                    <span className="text-xs text-muted-foreground mt-1">
                      {new Date(
                        folder.updatedAt || folder.createdAt
                      ).toLocaleDateString()}
                    </span>
                  </Link>
                ))}

                {/* Then files */}
                {filteredFiles.map((file: any) => (
                  <Link
                    key={file.id}
                    href={`/workspace/${workspaceSlug}/file/${file.id}?page=${pageId}`}
                    className="group relative bg-card hover:bg-muted/20 rounded-md border p-3 flex flex-col items-center text-center hover:underline cursor-pointer"
                  >
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <MoreButton
                        handleDelete={handleDelete}
                        file={file}
                        setIsCreateFolderOpen={setIsCreateFolderOpen}
                        type="file"
                        setNewFolderName={setNewFolderName}
                        setUpdateData={setUpdateData}
                        isSyncedWithCloud={isSyncedWithCloud}
                      />
                    </div>
                    <div className="h-12 w-12 flex items-center justify-center mb-2">
                      {React.cloneElement(getFileIcon(file.extension), {
                        className: "h-12 w-12",
                      })}
                    </div>
                    <span className="text-sm font-medium truncate max-w-full">
                      {file.name}
                    </span>
                    <span className="text-xs text-muted-foreground mt-1">
                      {file.extension ? `.${file.extension}` : "File"} ·{" "}
                      {file.size}
                    </span>
                    <div className="mt-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          {getVectorizationStatusBadge(
                            file?.vectorizationStatus
                          )}
                        </TooltipTrigger>
                        <TooltipContent>
                          {t("workspace.vectorizationStatusTooltip")}
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-8">
                {t("workspace.noItemsFound")}
              </p>
            )}
          </div>
        )}

        {/* Tiles View */}
        {viewMode === "tiles" && (
          <div>
            {/* Folders section */}
            {filteredChildFolders.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  {t("workspace.folders")}
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {filteredChildFolders.map((folder: any) => (
                    <Card
                      key={folder.id}
                      className="group hover:border-primary transition-colors"
                    >
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center flex-1 min-w-0 pr-4">
                            <Folder className="h-8 w-8 mr-2 text-blue-500 flex-shrink-0" />
                            <CardTitle className="text-base overflow-hidden text-ellipsis whitespace-nowrap w-full">
                              {folder.name}
                            </CardTitle>
                          </div>
                          <div className="flex-shrink-0">
                            <MoreButton
                              handleDelete={handleDelete}
                              file={folder}
                              setIsCreateFolderOpen={setIsCreateFolderOpen}
                              type="folder"
                              setNewFolderName={setNewFolderName}
                              setUpdateData={setUpdateData}
                              isSyncedWithCloud={isSyncedWithCloud}
                            />
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-2">
                        <p className="text-sm text-muted-foreground">
                          {t("workspace.modified")}{" "}
                          {new Date(
                            folder.updatedAt || folder.createdAt
                          ).toLocaleDateString()}
                        </p>
                      </CardContent>
                      <CardFooter className="p-4 pt-0">
                        <Button
                          variant="ghost"
                          asChild
                          className="w-full justify-start p-0 h-8 hover:underline cursor-pointer"
                        >
                          <Link
                            href={`/workspace/${workspaceSlug}/folder/${folder.id}?page=${pageId}`}
                          >
                            {t("workspace.openFolder")}
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Files section */}
            {filteredFiles.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  {t("workspace.files")}
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {filteredFiles.map((file: any) => (
                    <Card
                      key={file.id}
                      className="group hover:border-primary transition-colors"
                    >
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center flex-1 min-w-0 pr-4">
                            {getFileIcon(file.extension)}
                            <div className="ml-2 overflow-hidden w-full">
                              <div className="w-full overflow-hidden">
                                <CardTitle
                                  className="text-base overflow-hidden text-ellipsis whitespace-nowrap w-full"
                                  title={file.name}
                                >
                                  {file.name}
                                </CardTitle>
                              </div>
                              <p className="text-xs text-muted-foreground">
                                {file.extension ? `.${file.extension}` : ""} ·{" "}
                                {file.size}
                              </p>
                              <div className="mt-1">
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    {getVectorizationStatusBadge(
                                      file?.vectorizationStatus
                                    )}
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    {t("workspace.vectorizationStatusTooltip")}
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            <MoreButton
                              handleDelete={handleDelete}
                              file={file}
                              setIsCreateFolderOpen={setIsCreateFolderOpen}
                              type="file"
                              setNewFolderName={setNewFolderName}
                              setUpdateData={setUpdateData}
                              isSyncedWithCloud={isSyncedWithCloud}
                            />
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-2">
                        <p className="text-sm text-muted-foreground">
                          {t("workspace.modified")}{" "}
                          {new Date(
                            file.updatedAt || file.createdAt
                          ).toLocaleDateString()}
                        </p>
                      </CardContent>
                      <CardFooter className="p-4 pt-0">
                        <Button
                          variant="ghost"
                          asChild
                          className="w-full justify-start p-0 h-8 hover:underline cursor-pointer"
                        >
                          <Link
                            href={`/workspace/${workspaceSlug}/file/${file.id}?page=${pageId}`}
                          >
                            {t("workspace.viewFile")}
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {filteredChildFolders.length === 0 &&
              filteredFiles.length === 0 && (
                <p className="text-muted-foreground text-center py-8">
                  {t("workspace.noItemsFound")}
                </p>
              )}
          </div>
        )}
      </div>

      {/* Dialogs */}
      <CreateFolderDialog
        isLoading={isLoading}
        title={
          updateData?.type === "folder"
            ? t("workspace.renameFolder")
            : t("workspace.renameFile")
        }
        subTitle={
          updateData?.type === "folder"
            ? t("workspace.enterFolderName")
            : t("workspace.enterFileName")
        }
        isOpen={isCreateFolderOpen === "rename"}
        onClose={() => {
          setIsCreateFolderOpen(null);
          setNewFolderName("");
          setUpdateData(null);
        }}
        folderName={newFolderName}
        onFolderNameChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setNewFolderName(e.target.value)
        }
        onCreateFolder={handleRename}
        label={
          updateData?.type === "folder"
            ? t("workspace.folderName")
            : t("workspace.newFileName")
        }
        labelPlaceholder={
          updateData?.type === "folder"
            ? t("workspace.enterFolderNamePlaceholder")
            : t("workspace.enterFileNamePlaceholder")
        }
        buttonText={
          updateData?.type === "folder"
            ? t("workspace.renameFolder")
            : t("workspace.renameFile")
        }
      />
      <CreateFolderDialog
        isLoading={isLoading}
        title={t("workspace.createFolder")}
        subTitle={t("workspace.enterFolderName")}
        isOpen={isCreateFolderOpen === "create"}
        onClose={() => {
          setIsCreateFolderOpen(null);
          setNewFolderName("");
          setUpdateData(null);
        }}
        folderName={newFolderName}
        onFolderNameChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setNewFolderName(e.target.value)
        }
        onCreateFolder={handleCreateFolderClick}
        label={t("workspace.folderName")}
        labelPlaceholder={t("workspace.enterFolderNamePlaceholder")}
        buttonText={t("workspace.createFolder")}
      />
    </div>
  );
}
