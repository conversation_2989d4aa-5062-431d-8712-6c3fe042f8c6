"use client";

import React, { useState, useRef, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AtSign, Users, Search } from "lucide-react";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface SimpleMentionInputProps {
  value: string;
  onChange: (value: string, mentions: string[]) => void;
  placeholder?: string;
  className?: string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

export const SimpleMentionInput: React.FC<SimpleMentionInputProps> = ({
  value,
  onChange,
  placeholder,
  className,
  onKeyDown,
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [currentMention, setCurrentMention] = useState("");
  const [loading, setLoading] = useState(false);
  const [mentionMap, setMentionMap] = useState<Map<string, string>>(new Map()); // Maps @Name to userId
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Load all users when component mounts
  useEffect(() => {
    loadAllUsers();
  }, []);

  // Load all workspace users
  const loadAllUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/search?q=&limit=50`);
      if (response.ok) {
        const data = await response.json();
        setAllUsers(data.users || []);
      }
    } catch (error) {
      console.error("Error loading users:", error);
    } finally {
      setLoading(false);
    }
  };

  // Filter users based on query
  const filterUsers = (query: string) => {
    if (!query) {
      setFilteredUsers(allUsers);
      return;
    }

    const filtered = allUsers.filter(
      (user) =>
        user.name.toLowerCase().includes(query.toLowerCase()) ||
        user.email.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredUsers(filtered);
  };

  // Handle text change
  const handleTextChange = (newValue: string) => {
    // Check for @mentions at the end of the text
    const atIndex = newValue.lastIndexOf("@");
    if (atIndex !== -1) {
      const afterAt = newValue.slice(atIndex + 1);
      // Check if there's no space after @ (still typing a mention)
      if (!afterAt.includes(" ") && !afterAt.includes("\n")) {
        setCurrentMention(afterAt);
        setShowSuggestions(true);
        setSelectedIndex(0);
        filterUsers(afterAt);
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }

    // Extract mentions from the text using simple @Name format
    const mentionMatches = newValue.match(/@\w+/g) || [];
    const extractedMentions = mentionMatches
      .map((mention: string) => mentionMap.get(mention))
      .filter(Boolean) as string[];

    onChange(newValue, extractedMentions);
  };

  // Handle mention selection
  const selectMention = (user: User) => {
    const atIndex = value.lastIndexOf("@");
    if (atIndex === -1) return;

    const beforeAt = value.slice(0, atIndex);
    // Create a simple mention format: @Name (without spaces in name)
    const userName = user.name.replace(/\s+/g, "");
    const mentionText = `@${userName} `;
    const newValue = beforeAt + mentionText;

    // Update the mention map to track this mention
    const newMentionMap = new Map(mentionMap);
    newMentionMap.set(`@${userName}`, user.id);
    setMentionMap(newMentionMap);

    // Extract all current mentions from the new value
    const mentionMatches = newValue.match(/@\w+/g) || [];
    const extractedMentions = mentionMatches
      .map((mention: string) => newMentionMap.get(mention))
      .filter(Boolean) as string[];

    onChange(newValue, extractedMentions);
    setShowSuggestions(false);
    setCurrentMention("");

    // Focus back to textarea
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newValue.length, newValue.length);
      }
    }, 0);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (showSuggestions && filteredUsers.length > 0) {
      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setSelectedIndex((prev) => (prev + 1) % filteredUsers.length);
          break;
        case "ArrowUp":
          e.preventDefault();
          setSelectedIndex(
            (prev) => (prev - 1 + filteredUsers.length) % filteredUsers.length
          );
          break;
        case "Enter":
        case "Tab":
          e.preventDefault();
          selectMention(filteredUsers[selectedIndex]);
          break;
        case "Escape":
          setShowSuggestions(false);
          break;
      }
    }

    onKeyDown?.(e);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        textareaRef.current &&
        !textareaRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="relative w-full">
      {/* Modern Suggestions Dropdown */}
      {showSuggestions && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full bottom-full mb-2 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm"
        >
          {/* Header */}
          <div className="flex items-center gap-2 px-4 py-3 border-b border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50 rounded-t-xl">
            <AtSign className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Mention someone
            </span>
            <Badge variant="secondary" className="ml-auto text-xs">
              {filteredUsers.length}{" "}
              {filteredUsers.length === 1 ? "user" : "users"}
            </Badge>
          </div>

          {/* Search hint */}
          {currentMention && (
            <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-800 bg-blue-50 dark:bg-blue-900/20">
              <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
                <Search className="h-3 w-3" />
                <span>Searching for "{currentMention}"</span>
              </div>
            </div>
          )}

          {/* User List */}
          <ScrollArea className="max-h-64">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                  Loading users...
                </div>
              </div>
            ) : filteredUsers.length > 0 ? (
              <div className="py-2">
                {filteredUsers.map((user, index) => (
                  <div
                    key={user.id}
                    className={`flex items-center gap-3 px-4 py-3 cursor-pointer transition-all duration-150 ${
                      index === selectedIndex
                        ? "bg-blue-50 dark:bg-blue-900/30 border-r-2 border-blue-500"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    }`}
                    onClick={() => selectMention(user)}
                  >
                    <Avatar className="h-8 w-8 ring-2 ring-white dark:ring-gray-800 shadow-sm">
                      <AvatarImage src={user.image} />
                      <AvatarFallback className="text-xs font-medium bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                        {getInitials(user.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {user.name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {user.email}
                      </div>
                    </div>
                    {index === selectedIndex && (
                      <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400">
                        <kbd className="px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/50 rounded text-xs font-mono">
                          ↵
                        </kbd>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Users className="h-8 w-8 text-gray-300 dark:text-gray-600 mb-2" />
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {currentMention
                    ? `No users found for "${currentMention}"`
                    : "No users available"}
                </div>
                <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  Try a different search term
                </div>
              </div>
            )}
          </ScrollArea>

          {/* Footer */}
          <div className="flex items-center justify-between px-4 py-2 border-t border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50 rounded-b-xl">
            <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
              <kbd className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs font-mono">
                ↑↓
              </kbd>
              <span>Navigate</span>
              <kbd className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs font-mono">
                ↵
              </kbd>
              <span>Select</span>
              <kbd className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs font-mono">
                Esc
              </kbd>
              <span>Close</span>
            </div>
          </div>
        </div>
      )}

      {/* Simple textarea without overlay for now - we'll style mentions after submission */}
      <Textarea
        ref={textareaRef}
        value={value}
        onChange={(e) => handleTextChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={className}
      />
    </div>
  );
};
