import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(
  req: NextRequest,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Find the chat and verify ownership
    const chat = await db.chat.findFirst({
      where: {
        id: params.chatId,
        userId: session.userId,
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "asc",
          },
          // include: {
          //   comments: {
          //     include: {
          //       author: {
          //         select: {
          //           id: true,
          //           name: true,
          //           email: true,
          //         },
          //       },
          //     },
          //   },
          // },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!chat) {
      return new NextResponse("Chat not found", { status: 404 });
    }

    // Format the export data
    const exportData = {
      chat: {
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt,
        author: chat.user,
      },
      messages: chat.messages.map((message) => ({
        id: message.id,
        role: message.role,
        content: message.content,
        sources: message.sources || [],
        metadata: message.metadata || {},
        createdAt: message.createdAt,
        // comments: message.comments.map((comment) => ({
        //   id: comment.id,
        //   content: comment.content,
        //   author: comment.author,
        //   status: comment.status,
        //   mentions: comment.mentions || [],
        //   createdAt: comment.createdAt,
        // })),
      })),
      exportedAt: new Date().toISOString(),
      exportedBy: {
        id: session.userId,
        name: session.userName || session.user?.name,
        email: session.user?.email,
      },
    };

    // Create the response with proper headers for file download
    const response = new NextResponse(JSON.stringify(exportData, null, 2), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Content-Disposition": `attachment; filename="${
          chat.title || "chat"
        }-export-${new Date().toISOString().split("T")[0]}.json"`,
      },
    });

    return response;
  } catch (error) {
    console.error("[CHAT_EXPORT]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
