"use client";

import { useState } from "react";
import { Loader2, Users, Database, Plus, Minus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plan } from "@/services/src/plans";
import { useLanguage } from "@/lib/language-context";

// Define storage add-on options based on the pricing model
const STORAGE_ADDONS = [
  { id: "10gb", size: 10, price: 29 },
  { id: "50gb", size: 50, price: 79 },
  { id: "100gb", size: 100, price: 149 },
];

interface AdditionalUsersDialogProps {
  plan: Plan;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (additionalUsers: number, additionalStorageGB: number) => void;
  onCancel: () => void;
}

export function AdditionalUsersDialog({
  plan,
  open,
  onOpenChange,
  onConfirm,
  onCancel,
}: AdditionalUsersDialogProps) {
  const [additionalUsers, setAdditionalUsers] = useState(0);
  const [additionalStorageGB, setAdditionalStorageGB] = useState(0);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("users"); // 'users' or 'storage'
  const { t } = useLanguage();

  const handleConfirm = () => {
    setLoading(true);
    // Simulate a small delay for better UX
    setTimeout(() => {
      onConfirm(additionalUsers, additionalStorageGB);
      setLoading(false);
    }, 500);
  };

  const handleCancel = () => {
    setAdditionalUsers(0);
    setAdditionalStorageGB(0);
    onCancel();
  };

  const incrementUsers = () => {
    setAdditionalUsers((prev) => prev + 1);
  };

  const decrementUsers = () => {
    setAdditionalUsers((prev) => (prev > 0 ? prev - 1 : 0));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setAdditionalUsers(value);
    } else if (e.target.value === "") {
      setAdditionalUsers(0);
    }
  };

  // Storage management functions
  const getStorageAddon = (size: number) => {
    if (size <= 0) return null;
    if (size <= 10) return STORAGE_ADDONS[0];
    if (size <= 50) return STORAGE_ADDONS[1];
    return STORAGE_ADDONS[2];
  };

  const incrementStorage = () => {
    const currentAddon = getStorageAddon(additionalStorageGB);
    const currentIndex = currentAddon
      ? STORAGE_ADDONS.findIndex((addon) => addon.id === currentAddon.id)
      : -1;

    if (currentIndex === -1) {
      // If no current addon, add the smallest one
      setAdditionalStorageGB(STORAGE_ADDONS[0].size);
    } else if (currentIndex < STORAGE_ADDONS.length - 1) {
      // Move to the next larger addon
      setAdditionalStorageGB(STORAGE_ADDONS[currentIndex + 1].size);
    }
  };

  const decrementStorage = () => {
    const currentAddon = getStorageAddon(additionalStorageGB);
    const currentIndex = currentAddon
      ? STORAGE_ADDONS.findIndex((addon) => addon.id === currentAddon.id)
      : -1;

    if (currentIndex > 0) {
      // Move to the next smaller addon
      setAdditionalStorageGB(STORAGE_ADDONS[currentIndex - 1].size);
    } else {
      // If at the smallest addon or no addon, set to 0
      setAdditionalStorageGB(0);
    }
  };

  // Calculate costs including base plan price and additional users
  const basePlanPrice = plan.price || 0; // Base plan price
  const additionalUserCost = additionalUsers * plan.additionalUserFee; // Cost for additional users

  // Calculate costs for storage
  const basePlanStorage = plan.vectorStoreGB || 0; // Base plan storage
  const currentStorageAddon = getStorageAddon(additionalStorageGB);
  const additionalStorageCost = currentStorageAddon
    ? currentStorageAddon.price
    : 0;

  // Calculate total costs
  const totalCost = basePlanPrice + additionalUserCost + additionalStorageCost; // Total cost includes base plan + additional users + additional storage
  const totalUsers = plan.includedUsers + additionalUsers;
  const totalStorage = basePlanStorage + additionalStorageGB;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("billing.additionalUsers")}</DialogTitle>
          <DialogDescription>
            {t("billing.additionalUsersDescription", {
              planName: t(`${plan.name}`),
              includedUsers: plan.includedUsers,
            })}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex space-x-2 mb-4">
            <Button
              type="button"
              variant={activeTab === "users" ? "default" : "outline"}
              onClick={() => setActiveTab("users")}
              className="flex-1"
            >
              <Users className="mr-2 h-4 w-4" />
              {t("billing.users")}
            </Button>
            <Button
              type="button"
              variant={activeTab === "storage" ? "default" : "outline"}
              onClick={() => setActiveTab("storage")}
              className="flex-1"
            >
              <Database className="mr-2 h-4 w-4" />
              {t("billing.storage")}
            </Button>
          </div>

          {activeTab === "users" && (
            <div className="space-y-2">
              <Label htmlFor="additionalUsers">
                {t("billing.additionalUsers")}
              </Label>
              <div className="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={decrementUsers}
                  disabled={additionalUsers === 0}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <Input
                  id="additionalUsers"
                  type="number"
                  min="0"
                  value={additionalUsers}
                  onChange={handleInputChange}
                  className="w-20 text-center"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={incrementUsers}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {activeTab === "storage" && (
            <div className="space-y-2">
              <Label htmlFor="additionalStorage">
                {t("billing.additionalStorage")}
              </Label>
              <div className="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={decrementStorage}
                  disabled={additionalStorageGB === 0}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <div className="w-20 text-center border rounded-md py-2">
                  {additionalStorageGB > 0
                    ? `${additionalStorageGB} GB`
                    : "0 GB"}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={incrementStorage}
                  disabled={additionalStorageGB >= 100}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label>{t("billing.summary")}</Label>
            <div className="rounded-md border p-4 space-y-2">
              <div className="flex justify-between">
                <span>
                  {t("billing.basePlan", { users: plan.includedUsers })}
                </span>
                <span>
                  {t("billing.priceCHF", { price: basePlanPrice.toFixed(2) })}
                </span>
              </div>

              {/* Additional Users */}
              <div className="flex justify-between">
                <span>
                  {t("billing.additionalUsersCount", {
                    count: additionalUsers,
                  })}
                </span>
                <span>
                  {t("billing.priceCHF", {
                    price: additionalUserCost.toFixed(2),
                  })}
                </span>
              </div>

              {/* Additional Storage */}
              <div className="flex justify-between">
                <span>
                  {additionalStorageGB > 0
                    ? t("billing.additionalStorageGB", {
                        count: additionalStorageGB,
                      })
                    : t("billing.noAdditionalStorage")}
                </span>
                <span>
                  {additionalStorageGB > 0
                    ? t("billing.priceCHF", {
                        price: additionalStorageCost.toFixed(2),
                      })
                    : t("billing.free")}
                </span>
              </div>

              <div className="border-t pt-2 mt-2 flex justify-between font-medium">
                <span>{t("billing.totalSubscriptionCost")}</span>
                <span>
                  {t("billing.pricePerMonth", { price: totalCost.toFixed(2) })}
                </span>
              </div>

              {/* Additional info */}
              <div className="text-xs text-muted-foreground mt-2">
                <div>{t("billing.totalUsers", { count: totalUsers })}</div>
                <div>
                  {t("billing.totalStorage")}: {totalStorage} GB
                </div>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            {t("common.cancel")}
          </Button>
          <Button onClick={handleConfirm} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("billing.processing")}
              </>
            ) : (
              <>
                <Users className="mr-2 h-4 w-4" />
                {t("billing.confirmAndCheckout")}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
