/**
 * Converts an array of objects to a CSV string
 * @param data Array of objects to convert
 * @param headers Optional custom headers (if not provided, will use object keys)
 * @returns CSV string
 */
export function objectsToCSV(data: any[], headers?: string[]): string {
  if (!data || data.length === 0) {
    return '';
  }

  // Get headers from the first object if not provided
  const csvHeaders = headers || Object.keys(data[0]);
  
  // Create header row
  let csv = csvHeaders.join(',') + '\n';
  
  // Add data rows
  data.forEach(item => {
    const row = csvHeaders.map(header => {
      // Get the value for this header
      const value = header.includes('.') 
        ? getNestedValue(item, header) 
        : item[header];
      
      // Format the value for CSV
      return formatValueForCSV(value);
    });
    
    csv += row.join(',') + '\n';
  });
  
  return csv;
}

/**
 * Gets a nested value from an object using dot notation
 * @param obj Object to get value from
 * @param path Path to the value using dot notation (e.g., 'user.name')
 * @returns The value at the specified path
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((prev, curr) => {
    return prev ? prev[curr] : null;
  }, obj);
}

/**
 * Formats a value for CSV
 * @param value Value to format
 * @returns Formatted value
 */
function formatValueForCSV(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (typeof value === 'object') {
    if (value instanceof Date) {
      return value.toISOString();
    }
    return JSON.stringify(value).replace(/"/g, '""');
  }
  
  // Convert to string and escape quotes
  const stringValue = String(value).replace(/"/g, '""');
  
  // Wrap in quotes if the value contains commas, quotes, or newlines
  if (/[",\n\r]/.test(stringValue)) {
    return `"${stringValue}"`;
  }
  
  return stringValue;
}

/**
 * Downloads a CSV file in the browser
 * @param csvContent CSV content
 * @param fileName File name
 */
export function downloadCSV(csvContent: string, fileName: string): void {
  // Create a blob with the CSV content
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // Create a download link
  const link = document.createElement('a');
  
  // Create a URL for the blob
  const url = URL.createObjectURL(blob);
  
  // Set link properties
  link.setAttribute('href', url);
  link.setAttribute('download', fileName);
  link.style.visibility = 'hidden';
  
  // Add link to the document
  document.body.appendChild(link);
  
  // Click the link to download the file
  link.click();
  
  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * Formats a date range for file names
 * @param dateRange Date range object
 * @returns Formatted date range string
 */
export function formatDateRangeForFileName(dateRange: { from: Date; to: Date }): string {
  const fromDate = dateRange.from.toISOString().split('T')[0];
  const toDate = dateRange.to.toISOString().split('T')[0];
  return `${fromDate}_to_${toDate}`;
}

/**
 * Exports data to CSV and downloads it
 * @param data Data to export
 * @param fileName File name
 * @param headers Optional custom headers
 */
export function exportToCSV(data: any[], fileName: string, headers?: string[]): void {
  const csvContent = objectsToCSV(data, headers);
  downloadCSV(csvContent, fileName);
}
