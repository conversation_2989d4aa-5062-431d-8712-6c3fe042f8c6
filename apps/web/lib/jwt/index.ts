import jwt from 'jsonwebtoken';

/**
 * Generate a JWT token for the current user
 * 
 * @param payload - The payload to include in the token
 * @returns The JWT token
 */
export const generateToken = (payload: any): string => {
  const secret = process.env.NEXTAUTH_SECRET;
  
  if (!secret) {
    throw new Error('NEXTAUTH_SECRET is not defined');
  }
  
  // Create a token that expires in 1 hour
  return jwt.sign(payload, secret, { expiresIn: '1h' });
};

/**
 * Verify a JWT token
 * 
 * @param token - The token to verify
 * @returns The decoded token payload
 */
export const verifyToken = (token: string): any => {
  const secret = process.env.NEXTAUTH_SECRET;
  
  if (!secret) {
    throw new Error('NEXTAUTH_SECRET is not defined');
  }
  
  return jwt.verify(token, secret);
};
