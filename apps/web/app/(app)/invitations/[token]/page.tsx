"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Building, Eye, EyeOff, Loader2, Mail, User } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { verifyInvitation, acceptInvitation } from "@/services";
import toast from "react-hot-toast";

// Create form schemas based on user status
const loggedInSchema = z.object({});

const loggedOutSchema = z
  .object({
    name: z.string().min(2, { message: "Name must be at least 2 characters" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export default function InvitationPage({
  params,
}: {
  params: { token: string };
}) {
  const router = useRouter();
  const { data: session, status: sessionStatus } = useSession();
  const [invitationData, setInvitationData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  // Determine whether to use logged in or logged out schema based on session
  const formSchema = session ? loggedInSchema : loggedOutSchema;

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Fetch invitation data
  useEffect(() => {
    const fetchInvitation = async () => {
      setLoading(true);
      setError(null);

      const result = await verifyInvitation(params.token);

      if (result.error) {
        setError(result.error);
      } else {
        setInvitationData(result.data);
      }

      setLoading(false);
    };

    if (params.token) {
      fetchInvitation();
    }
  }, [params.token]);

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setSubmitting(true);
    setError(null);

    try {
      const data: any = { token: params.token };

      // Add password and name if user is not logged in
      if (!session) {
        // Type check for logged out schema values
        const loggedOutValues = values as z.infer<typeof loggedOutSchema>;
        if (loggedOutValues.password && loggedOutValues.name) {
          data.password = loggedOutValues.password;
          data.name = loggedOutValues.name;
        }
      }

      const result = await acceptInvitation(data);

      if (result.error) {
        toast.error(result.error);
        setError(result.error);
      } else {
        toast.success("Invitation accepted successfully!");

        // Redirect to dashboard or sign-in based on session status
        if (session) {
          router.push("/dashboard");
        } else {
          router.push("/sign-in");
        }
      }
    } catch (error: any) {
      toast.error("Failed to accept invitation");
      setError(error.message || "Failed to accept invitation");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p>Verifying invitation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">
              Invitation Error
            </CardTitle>
            <CardDescription>
              We couldn't verify your invitation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-destructive/10 text-destructive rounded-lg p-4 mb-4">
              {error}
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/">Return to Home</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Render the invitation acceptance form
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-6">
            {/* Logo or avatar for the organization */}
            <div className="rounded-full bg-primary/10 p-4">
              <Building className="h-8 w-8 text-primary" />
            </div>
          </div>
          <CardTitle className="text-center text-2xl font-bold">
            Join {invitationData?.tenantName}
          </CardTitle>
          <CardDescription className="text-center">
            {invitationData?.inviterName} has invited you to join as a{" "}
            {invitationData?.role.toLowerCase()}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-md">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {invitationData?.email}
                </span>
              </div>

              {!session && (
                <>
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Your Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your full name"
                            {...field}
                            autoComplete="name"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Create Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="••••••••"
                              type={showConfirmPassword ? "text" : "password"}
                              {...field}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-2.5 text-muted-foreground"
                              onClick={() =>
                                setShowConfirmPassword(!showConfirmPassword)
                              }
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="••••••••"
                              type={showConfirmPassword ? "text" : "password"}
                              {...field}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-2.5 text-muted-foreground"
                              onClick={() =>
                                setShowConfirmPassword(!showConfirmPassword)
                              }
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              <Button type="submit" className="w-full" disabled={submitting}>
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing
                  </>
                ) : (
                  `Accept Invitation`
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <div className="text-sm text-center text-muted-foreground">
            {session ? (
              <p>
                Not {session.user?.name}?{" "}
                <Link href="/api/auth/signout" className="underline">
                  Sign out
                </Link>
              </p>
            ) : (
              <p>
                Already have an account?{" "}
                <Link
                  href={`/sign-in?callbackUrl=/invitations/${params.token}`}
                  className="underline"
                >
                  Sign in
                </Link>
              </p>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
