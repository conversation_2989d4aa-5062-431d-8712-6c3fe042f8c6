"use client";

import React from "react";
import { Search, Command } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useGlobalSearch } from "./global-search-provider";
import { cn } from "@/lib/utils";

interface GlobalSearchButtonProps {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  className?: string;
  showShortcut?: boolean;
}

export const GlobalSearchButton: React.FC<GlobalSearchButtonProps> = ({
  variant = "ghost",
  size = "default",
  className,
  showShortcut = false,
}) => {
  const { openSearch } = useGlobalSearch();

  return (
    <Button
      variant={variant}
      size={size}
      onClick={openSearch}
      className={cn(
        "relative justify-start text-muted-foreground",
        size === "sm" && "h-8",
        className
      )}
    >
      <Search className="h-4 w-4" />
      {showShortcut && (
        <div className="ml-auto hidden sm:flex items-center gap-1 text-xs">
          <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
            <Command className="h-3 w-3" />K
          </kbd>
        </div>
      )}
    </Button>
  );
};
