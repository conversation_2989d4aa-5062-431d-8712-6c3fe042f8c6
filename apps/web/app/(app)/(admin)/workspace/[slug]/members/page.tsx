import WorkspaceMembersPage from "@/components/wrapper-screens/workspace/members-wrapper";
import { authOptions } from "@/lib/next-auth";
import { getWorkspaceMember } from "@/services";
import { getServerSession } from "next-auth";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function Page({ params, searchParams }) {
  const { slug } = params;
  const pageId = searchParams.page || "";
  const session: any = await getServerSession(authOptions);

  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;
  const members = await getWorkspaceMember(tenantId, userId, slug);
  if (!tenantId) {
    return redirect("/sign-in");
  }

  try {
    return (
      <WorkspaceMembersPage
        members={members?.data ?? []}
        tenantId={tenantId}
        workspaceSlug={slug}
        currentUserId={session?.userId}
      />
    );
  } catch (error) {
    console.error("Error loading folder:", error);
    return redirect(`/workspace/${slug}${pageId ? `?page=${pageId}` : ""}`);
  }
}
