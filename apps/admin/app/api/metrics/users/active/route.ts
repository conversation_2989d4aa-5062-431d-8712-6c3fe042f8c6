import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "month"; // 'day' or 'month'
    
    // Calculate the start date based on the period
    const startDate = new Date();
    if (period === 'day') {
      startDate.setHours(0, 0, 0, 0); // Start of today
    } else {
      startDate.setDate(1); // Start of current month
      startDate.setHours(0, 0, 0, 0);
    }

    // Count unique users who have made API requests in the period
    const activeUsers = await db.aPIRequest.groupBy({
      by: ['userId'],
      where: {
        timestamp: {
          gte: startDate
        }
      },
      _count: {
        userId: true
      }
    });

    // Get total users
    const totalUsers = await db.user.count();
    
    // Calculate active user percentage
    const activeUserCount = activeUsers.length;
    const activeUserPercentage = totalUsers > 0 
      ? (activeUserCount / totalUsers) * 100 
      : 0;

    // Get active users by tenant
    const activeUsersByTenant = await db.aPIRequest.groupBy({
      by: ['tenantId'],
      where: {
        timestamp: {
          gte: startDate
        }
      },
      _count: {
        userId: true
      }
    });

    // Get tenant names for the active tenants
    const tenantIds = activeUsersByTenant.map(item => item.tenantId);
    const tenants = await db.tenant.findMany({
      where: {
        id: {
          in: tenantIds
        }
      },
      select: {
        id: true,
        name: true
      }
    });

    // Map tenant names to the results
    const activeUsersByTenantWithNames = activeUsersByTenant.map(item => {
      const tenant = tenants.find(t => t.id === item.tenantId);
      return {
        tenantId: item.tenantId,
        tenantName: tenant?.name || 'Unknown',
        activeUsers: item._count.userId
      };
    });

    return NextResponse.json({
      period,
      activeUserCount,
      totalUsers,
      activeUserPercentage: Math.round(activeUserPercentage * 100) / 100,
      activeUsersByTenant: activeUsersByTenantWithNames
    });
  } catch (error) {
    console.error("Error fetching active users metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch active users metrics" },
      { status: 500 }
    );
  }
}
