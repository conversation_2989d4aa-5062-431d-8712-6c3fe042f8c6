import { ChatForm } from "@/components/wrapper-screens/chat";
import { getChat, getUserAccount } from "@/services";
import { redirect } from "next/navigation";
import { cookies } from "next/headers";

export default async function HomePage({ params }) {
  const userId = await cookies().get("userId")?.value;
  const tenantId = cookies().get("currentOrganizationId")?.value;
  if (!userId || !tenantId) {
    return redirect("/sign-in");
  }
  const { id } = params;
  const [user, chats] = await Promise.all([
    getUserAccount(userId),
    getChat({ id, userId, tenantId }),
  ]);

  if (!chats?.chat?.id) {
    return redirect("/ask-ai");
  }

  return (
    <ChatForm
      userName={user?.name}
      userId={userId}
      tenantId={tenantId}
      chats={chats?.chat}
    />
  );
}
