import { NextRequest, NextResponse } from "next/server";

import axios from "axios";
import db from "@/lib/shared-db";

const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
export async function GET(req: NextRequest) {
  const url = req.url;
  const urlParams = new URLSearchParams(url.split("?")[1]);
  const code = urlParams.get("code");
  const rawState = urlParams.get("state") ?? "";
  const error = urlParams.get("error");

  if (!code) {
    console.error("Authorization code is missing");
    return Response.redirect(
      `${APP_URL}/settings/integrations/?error=Authorization code is missing`
    );
  }

  const tokenEndpoint = `https://login.microsoftonline.com/common/oauth2/v2.0/token`;
  const params = new URLSearchParams();

  params.append("client_id", process.env.NEXT_PUBLIC_MICROSOFT_CLIENT_ID ?? "");
  params.append(
    "scope",
    "offline_access Files.ReadWrite Files.ReadWrite.All Sites.ReadWrite.All User.Read"
  ); // Requested scopes
  params.append("code", code);
  params.append("redirect_uri", `${APP_URL}/api/integration/outlook/callback`);
  params.append("grant_type", "authorization_code");
  params.append("client_secret", process.env.MICROSOFT_CLIENT_SECRET ?? "");

  try {
    const response = await axios.post(tokenEndpoint, params, {
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
    });
    const state = JSON.parse(rawState);
    if (response.data?.access_token) {
      const payload: any = {
        platform: "OUTLOOK",
        accountId: " ",
        accessToken: response.data?.access_token,
        refreshToken: response.data?.refresh_token,
        tenantId: state?.tenantId,
        userId: state?.userId,
        config: response.data,
      };
      const integration = await db.integration.create({ data: payload });
      if (integration.id) {
        return Response.redirect(`${APP_URL}/settings/integrations`);
      } else {
        return Response.redirect(
          `${APP_URL}/settings/integrations/?error=${error}`
        );
      }
    } else {
      return Response.redirect(
        `${APP_URL}/settings/integrations/?error=${error}`
      );
    }
  } catch (error) {
    console.error("Token acquisition failed:", error?.response?.data);
    return Response.redirect(
      `${APP_URL}/settings/integrations/?error=Token acquisition failed`
    );
  }
}
