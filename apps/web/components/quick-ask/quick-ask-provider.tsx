"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { QuickAskDialog } from "./quick-ask-dialog";
import { useLanguage } from "@/lib/language-context";
import { Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";

interface QuickAskContextType {
  openQuickAsk: () => void;
  closeQuickAsk: () => void;
}

const QuickAskContext = createContext<QuickAskContextType>({
  openQuickAsk: () => {},
  closeQuickAsk: () => {},
});

export const useQuickAsk = () => useContext(QuickAskContext);

interface QuickAskProviderProps {
  children: React.ReactNode;
  userId: string;
  tenantId: string;
  userName: string;
}

export function QuickAskProvider({
  children,
  userId,
  tenantId,
  userName,
}: QuickAskProviderProps) {
  const [open, setOpen] = useState(false);
  // Handle keyboard shortcut (Cmd/Ctrl + K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault();
        setOpen((prev) => !prev);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  const openQuickAsk = () => setOpen(true);
  const closeQuickAsk = () => setOpen(false);

  return (
    <QuickAskContext.Provider value={{ openQuickAsk, closeQuickAsk }}>
      {children}
      <QuickAskDialog
        userId={userId}
        tenantId={tenantId}
        userName={userName}
        open={open}
        onOpenChange={setOpen}
      />
    </QuickAskContext.Provider>
  );
}

// Client component wrapper for server components
export function QuickAskButton() {
  const { openQuickAsk } = useQuickAsk();
  const { t } = useLanguage();

  return (
    <button
      onClick={openQuickAsk}
      className={cn(
        "group relative inline-flex items-center justify-center gap-2",
        "rounded-full text-sm font-medium",
        "bg-gradient-to-r from-primary/10 via-primary/5 to-transparent",
        "border border-primary/20 shadow-sm ",
        "hover:shadow-md hover:border-primary/30 hover:from-primary/20",
        "transition-all duration-200 ease-in-out",
        "h-9 md:px-4 md:py-2",
        "px-2 py-1" // smaller padding for mobile
      )}
    >
      <Sparkles className="h-3.5 w-3.5 text-primary/70 group-hover:text-primary transition-colors" />
      <span className="hidden md:inline font-medium">
        {t("quickAsk.buttonLabel") || "Quick Ask"}
      </span>
      <div className="hidden md:flex items-center gap-0.5 bg-background/80 rounded px-1.5 py-0.5 text-[10px] font-mono text-muted-foreground border border-border/30">
        <span className="text-[9px]">⌘</span>
        <span>K</span>
      </div>
    </button>
  );
}
