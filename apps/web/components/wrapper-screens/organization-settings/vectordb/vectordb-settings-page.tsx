"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { updateVectorDB } from "@/services";
import toast from "react-hot-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useLanguage } from "@/lib/language-context";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Create schema with translations
const createFormSchema = (t) =>
  z.object({
    provider: z.string().min(1, t("vectordb.providerRequired")),
    connectionString: z.string().min(1, t("vectordb.connectionStringRequired")),
    databaseName: z.string().min(1, t("vectordb.databaseNameRequired")),
    collectionName: z.string().min(1, t("vectordb.collectionNameRequired")),
  });

export default function VectorDBSettingsPage({ vector }) {
  const { t } = useLanguage();
  const disabled = true;
  // Create schema with translations
  const formSchema = createFormSchema(t);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      provider: vector?.provider || "mongodb",
      connectionString: vector?.connectionString || "",
      databaseName: vector?.databaseName || "",
      collectionName: vector?.collectionName || "",
    },
  });

  async function onSubmit(values) {
    try {
      await updateVectorDB({
        settings: values,
      });
      toast.success(t("vectordb.updateSuccess"));
    } catch (error) {
      console.error("Error updating VectorDB settings:", error);
      toast.error(t("vectordb.updateError"));
    }
  }

  return (
    <Card className="space-y-6">
      <CardHeader>
        <CardTitle>{t("vectordb.providerConfiguration")}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="provider"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("vectordb.provider")}</FormLabel>
                  <Select
                    disabled={disabled}
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t("vectordb.selectProvider")}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="mongodb">
                        {t("vectordb.mongodbVector")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {t("vectordb.chooseProvider")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="connectionString"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("vectordb.connectionString")}</FormLabel>
                  <FormControl>
                    <Input
                      disabled={disabled}
                      type="password"
                      placeholder={t("vectordb.connectionStringPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("vectordb.connectionStringDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="databaseName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("vectordb.databaseName")}</FormLabel>
                  <FormControl>
                    <Input
                      disabled={disabled}
                      placeholder={t("vectordb.databaseNamePlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("vectordb.databaseNameDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="collectionName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("vectordb.collectionName")}</FormLabel>
                  <FormControl>
                    <Input
                      disabled={disabled}
                      placeholder={t("vectordb.collectionNamePlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("vectordb.collectionNameDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button disabled={disabled} type="submit">
              {t("vectordb.saveChanges")}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
