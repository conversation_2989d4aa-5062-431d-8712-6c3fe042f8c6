import { createFolder, deleteFolder, updateFolder } from "@/services";
import { getCookie } from "@/utils/cookies";
import { set } from "date-fns";
import toast from "react-hot-toast";

interface FolderOperationsProps {
  workspaceSlug: string;
  tenantId: string;
  pageId: string;
  setIsLoading: any;
}

export const useFolderOperations = ({
  workspaceSlug,
  tenantId,
  pageId,
  setIsLoading,
}: FolderOperationsProps) => {
  const userId = getCookie("userId") || "";
  const handleCreateFolder = async (
    newFolderName: string,
    parentFolderId?: string
  ) => {
    if (newFolderName.trim()) {
      try {
        toast.loading("Creating folder...");
        setIsLoading(true);

        const parentIds = parentFolderId ? [parentFolderId] : [];

        const data = {
          name: newFolderName.trim(),
          workspaceSlug,
          tenantId,
          pageId,
          parentIds,
        };

        const newFolder = await createFolder(data, tenantId, userId);
        toast.remove();
        setIsLoading(false);
        if (newFolder?.folder?.id) {
          window.location.reload();
          toast.success(newFolder?.message ?? "Folder created successfully!");
          return true;
        } else {
          toast.error(
            newFolder?.error ?? "Failed to create folder. Please try again."
          );
          return false;
        }
      } catch (error) {
        console.error("Error creating folder:", error);
        return false;
      }
    }
    toast.error("Folder name cannot be empty.");
    return false;
  };

  const handleRenameFolder = async (folderId: string, newName: string) => {
    if (!newName.trim()) {
      toast.error("Folder name cannot be empty.");
      return false;
    }
    setIsLoading(true);
    toast.loading("Renaming folder...");
    const updatedFolder = await updateFolder(
      {
        id: folderId,
        name: newName,
        tenantId,
      },
      tenantId,
      userId
    );
    toast.remove();
    setIsLoading(false);
    if (updatedFolder?.message) {
      toast.success(updatedFolder?.message ?? "Folder renamed successfully!");
      window.location.reload();
      return true;
    } else {
      toast.error(
        updatedFolder?.error ?? "Failed to rename folder. Please try again."
      );
      return false;
    }
  };

  const handleDeleteFolder = async (folderId: string) => {
    toast.loading("Deleting folder...");
    setIsLoading(true);
    const deletedFolder = await deleteFolder(folderId, tenantId, userId);
    toast.remove();
    setIsLoading(false);
    if (deletedFolder?.message) {
      toast.success(deletedFolder?.message ?? "Folder deleted successfully!");
      window.location.reload();
      return true;
    } else {
      toast.error(
        deletedFolder?.error ?? "Failed to delete folder. Please try again."
      );
      return false;
    }
  };

  return {
    handleCreateFolder,
    handleRenameFolder,
    handleDeleteFolder,
  };
};
