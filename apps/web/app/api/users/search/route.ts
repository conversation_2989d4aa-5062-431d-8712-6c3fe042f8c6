import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

// Search users for @mentions
export async function GET(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const query = searchParams.get("q");
    const limit = parseInt(searchParams.get("limit") || "10");

    // Allow empty query to return all users (for @mention dropdown)
    const searchQuery = query?.trim() || "";

    // Get user's current tenant from membership
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
      },
    });

    if (!membership) {
      return new NextResponse("No tenant access", { status: 403 });
    }

    // Search for users in the same tenant
    const users = await db.user.findMany({
      where: {
        AND: [
          {
            membership: {
              some: {
                tenantId: membership.tenantId,
              },
            },
          },
          ...(searchQuery
            ? [
                {
                  OR: [
                    {
                      name: {
                        contains: searchQuery,
                        mode: "insensitive",
                      },
                    },
                    {
                      email: {
                        contains: searchQuery,
                        mode: "insensitive",
                      },
                    },
                  ],
                },
              ]
            : []),
          {
            id: {
              not: session.userId, // Exclude current user
            },
          },
        ],
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
      },
      take: limit,
    });

    return NextResponse.json({ users });
  } catch (error) {
    console.error("[USERS_SEARCH_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
