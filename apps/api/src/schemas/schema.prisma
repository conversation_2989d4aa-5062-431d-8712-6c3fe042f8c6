generator client {
    provider      = "prisma-client-js"
    binaryTargets = ["native", "debian-openssl-1.1.x"]
}

generator python_client {
    provider             = "prisma-client-py"
    recursive_type_depth = "5"
}

datasource db {
    provider = "mongodb"
    url      = env("DATABASE_URL")
}

enum MembershipRole {
    OWNER
    MEMBER
    ADMIN
}

enum LLMProvider {
    AZURE_OPENAI
    AZURE_DEEPSEEK
    OPENAI
    DEEPSEEK
}

enum VectorDBProvider {
    LANCEDB
    PINECONE
    CHROMADB
}

enum EmbeddingProvider {
    OPENAI
}

model Account {
    id                 String  @id @default(auto()) @map("_id") @db.ObjectId
    userId             String  @db.ObjectId
    type               String
    provider           String
    providerAccountId  String
    refresh_token      String?
    access_token       String?
    expires_at         Int?
    token_type         String?
    scope              String?
    id_token           String?
    session_state      String?
    oauth_token_secret String?
    oauth_token        String?
    user               User    @relation(fields: [userId], references: [id])

    @@unique([provider, providerAccountId])
    @@index([userId])
}

model Session {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    sessionToken String   @unique
    userId       String   @db.ObjectId
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
}

model User {
    id        String  @id @default(auto()) @map("_id") @db.ObjectId
    name      String? /// @encrypted <- annotate fields to encrypt
    email     String  @unique /// @encrypted <- annotate fields to encrypt
    emailHash String  @unique

    password              String?
    emailVerified         DateTime?
    image                 String?
    passwordResetRequired Boolean   @default(false)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    accounts        Account[]
    sessions        Session[]
    membership      Membership[]
    sentInvitations Invitation[]      @relation("SentInvitations")
    workspaceMember WorkspaceMember[]
    Chat            Chat[]
    Message         Message[]
    ChatGroup       ChatGroup[]
    TokenUsage      TokenUsage[]
    APIRequest      APIRequest[]
}

model VerificationToken {
    id         String   @id @default(auto()) @map("_id") @db.ObjectId
    identifier String
    token      String   @unique
    expires    DateTime
    type       String?

    @@unique([identifier, token])
}

model Tenant {
    id                String              @id @default(auto()) @map("_id") @db.ObjectId
    name              String
    slug              String
    image             String?
    description       String?
    url               String?
    createdAt         DateTime            @default(now())
    updatedAt         DateTime            @updatedAt
    isOnboarded       Boolean             @default(false)
    stripeCustomerId  String? // Stripe customer ID
    Membership        Membership[]
    workspaces        Workspace[]
    llmSettings       LLMSettings[]
    vectorDbSettings  VectorDBSettings[]
    embeddingSettings EmbeddingSettings[]
    Invitation        Invitation[]
    Integration       Integration[]
    TokenUsage        TokenUsage[]
    APIRequest        APIRequest[]
    Subscription      Subscription[]
    VectorStoreUsage  VectorStoreUsage[]
    WebSearchUsage    WebSearchUsage[]

    @@unique([slug, id])
    @@index([slug])
}

model Membership {
    id       String         @id @default(auto()) @map("_id") @db.ObjectId
    role     MembershipRole
    tenantId String         @db.ObjectId
    userId   String         @db.ObjectId
    user     User           @relation(fields: [userId], references: [id])
    tenant   Tenant         @relation(fields: [tenantId], references: [id])

    createdAt       DateTime          @default(now())
    updatedAt       DateTime          @updatedAt
    workspaceMember WorkspaceMember[]

    @@unique([tenantId, userId]) // Prevents duplicate memberships
    @@index([tenantId])
    @@index([userId])
}

model Workspace {
    id                String              @id @default(auto()) @map("_id") @db.ObjectId
    name              String
    slug              String
    description       String?
    initials          String?
    createdAt         DateTime            @default(now())
    updatedAt         DateTime            @updatedAt
    tenantId          String              @db.ObjectId
    tenant            Tenant              @relation(fields: [tenantId], references: [id])
    pages             Page[]
    folders           Folder[]
    files             File[]
    canEditMember     Boolean             @default(false)
    LLMSettings       LLMSettings[]
    VectorDBSettings  VectorDBSettings[]
    EmbeddingSettings EmbeddingSettings[]
    workspaceMember   WorkspaceMember[]

    @@unique([slug, tenantId])
    @@index([tenantId])
}

model Page {
    id               String  @id @default(auto()) @map("_id") @db.ObjectId
    name             String
    content          String?
    oneDriveFolderId String?
    gDriveFolderId   String?
    canEditMember    Boolean @default(false)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])
    folders     Folder[]
    files       File[]

    @@index([workspaceId])
}

model Folder {
    id            String  @id @default(auto()) @map("_id") @db.ObjectId
    name          String
    canEditMember Boolean @default(false)
    parentId      String? @db.ObjectId

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspaceId String    @db.ObjectId
    pageId      String?   @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])
    page        Page?     @relation(fields: [pageId], references: [id])

    parentRelations FolderHierarchy[] @relation("FolderParents")
    childRelations  FolderHierarchy[] @relation("FolderChildren")
    files           File[]

    @@index([workspaceId])
}

model FolderHierarchy {
    id       String  @id @default(auto()) @map("_id") @db.ObjectId
    parentId String  @db.ObjectId
    childId  String  @db.ObjectId
    pageId   String? @db.ObjectId

    parent Folder @relation("FolderParents", fields: [parentId], references: [id], onDelete: Cascade)
    child  Folder @relation("FolderChildren", fields: [childId], references: [id], onDelete: Cascade)

    @@unique([parentId, childId]) // Prevents duplicate relationships
}

model File {
    id             String    @id @default(auto()) @map("_id") @db.ObjectId
    name           String
    type           String
    extension      String?
    gDriveFileId   String?
    oneDriveFileId String?
    size           String?
    url            String?
    content        String?
    createdAt      DateTime  @default(now())
    updatedAt      DateTime  @updatedAt
    workspaceId    String    @db.ObjectId
    parentId       String?   @db.ObjectId
    pageId         String?   @db.ObjectId
    folderId       String?   @db.ObjectId
    workspace      Workspace @relation(fields: [workspaceId], references: [id])
    page           Page?     @relation(fields: [pageId], references: [id])
    folder         Folder?   @relation(fields: [folderId], references: [id])
    canEditMember  Boolean   @default(false)

    @@index([workspaceId])
}

model LLMSettings {
    id                    String      @id @default(auto()) @map("_id") @db.ObjectId
    provider              LLMProvider
    apiKey                String?
    // Azure OpenAI fields
    azureOpenAIModel      String?
    azureOpenAIApiVersion String?
    azureOpenAIDeployment String?
    azureOpenAIEndpoint   String?
    // OpenAI fields
    baseUrl               String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model VectorDBSettings {
    id          String           @id @default(auto()) @map("_id") @db.ObjectId
    provider    VectorDBProvider
    // LanceDB fields
    localUri    String?
    // Pinecone fields
    apiKey      String?
    environment String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model EmbeddingSettings {
    id            String            @id @default(auto()) @map("_id") @db.ObjectId
    provider      EmbeddingProvider
    // OpenAI Embeddings fields
    apiKey        String?
    embedderModel String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Invitation {
    id        String         @id @default(auto()) @map("_id") @db.ObjectId
    email     String /// @encrypted <- annotate fields to encrypt
    emailHash String
    role      MembershipRole
    tenantId  String         @db.ObjectId
    inviterId String         @db.ObjectId
    token     String         @unique
    expires   DateTime
    accepted  Boolean        @default(false)

    tenant  Tenant @relation(fields: [tenantId], references: [id])
    inviter User   @relation("SentInvitations", fields: [inviterId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([inviterId])
    @@index([email])
}

model WorkspaceMember {
    id   String         @id @default(auto()) @map("_id") @db.ObjectId
    role MembershipRole

    workspaceId  String      @db.ObjectId
    userId       String      @db.ObjectId
    membershipId String      @db.ObjectId
    workspace    Workspace   @relation(fields: [workspaceId], references: [id])
    user         User        @relation(fields: [userId], references: [id])
    membership   Membership? @relation(fields: [membershipId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([workspaceId, userId])
    @@index([workspaceId])
    @@index([userId])
}

model ChatGroup {
    id          String  @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    isArchived  Boolean @default(false)

    userId String @db.ObjectId
    user   User   @relation(fields: [userId], references: [id])

    chats     Chat[]
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
}

model Chat {
    id          String  @id @default(auto()) @map("_id") @db.ObjectId
    title       String?
    description String?
    isArchived  Boolean @default(false)

    userId  String     @db.ObjectId
    user    User       @relation(fields: [userId], references: [id])
    groupId String?    @db.ObjectId
    group   ChatGroup? @relation(fields: [groupId], references: [id])

    messages  Message[]
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt

    @@index([userId])
    @@index([groupId])
}

model Message {
    id       String @id @default(auto()) @map("_id") @db.ObjectId
    content  String /// @encrypted <- annotate fields to encrypt
    role     String // 'user' or 'assistant'
    metadata Json? // For storing additional message-specific data

    chatId String @db.ObjectId
    chat   Chat   @relation(fields: [chatId], references: [id], onDelete: Cascade)
    userId String @db.ObjectId
    user   User   @relation(fields: [userId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([chatId])
    @@index([userId])
}

enum IntegrationType {
    OUTLOOK
    GOOGLE
}

model Integration {
    id           String          @id @default(auto()) @map("_id") @db.ObjectId
    platform     IntegrationType
    accountId    String
    accountName  String?
    accessToken  String
    page         Json?
    refreshToken String?
    config       Json?
    tenantId     String          @db.ObjectId
    tenant       Tenant          @relation(fields: [tenantId], references: [id])

    @@map("integration")
}

model TokenUsage {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    userId       String?  @db.ObjectId
    user         User?    @relation(fields: [userId], references: [id])
    inputTokens  Int
    outputTokens Int
    timestamp    DateTime @default(now())
    requestType  String // e.g., "chat", "embedding", "completion"
    modelUsed    String // The specific model used for this request
    cost         Float // Estimated cost in USD
    tenantId     String   @db.ObjectId
    tenant       Tenant   @relation(fields: [tenantId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
    @@index([timestamp])
}

model APIRequest {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    userId       String   @db.ObjectId
    user         User     @relation(fields: [userId], references: [id])
    endpoint     String // The API endpoint called
    method       String // HTTP method used
    statusCode   Int // Response status code
    timestamp    DateTime @default(now())
    duration     Int // Request duration in milliseconds
    success      Boolean // Whether the request was successful
    errorMessage String? // Error message if request failed
    tenantId     String   @db.ObjectId
    tenant       Tenant   @relation(fields: [tenantId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
    @@index([timestamp])
}

enum PlanType {
    STARTER
    TEAM
    PRO
    ENTERPRISE
    SOLO
}

model Plan {
    id                String         @id @default(auto()) @map("_id") @db.ObjectId
    name              String
    type              PlanType
    description       String?
    includedUsers     Int
    additionalUserFee Float
    vectorStoreGB     Int
    webSearchLimit    Int            @default(50) // Daily web search limit
    price             Float? // Base price of the plan (null for Enterprise)
    isActive          Boolean        @default(true)
    createdAt         DateTime       @default(now())
    updatedAt         DateTime       @updatedAt
    subscriptions     Subscription[]
}

model Subscription {
    id       String @id @default(auto()) @map("_id") @db.ObjectId
    tenantId String @db.ObjectId
    tenant   Tenant @relation(fields: [tenantId], references: [id])
    planId   String @db.ObjectId
    plan     Plan   @relation(fields: [planId], references: [id])

    startDate                   DateTime  @default(now())
    endDate                     DateTime?
    isActive                    Boolean   @default(true)
    additionalUsers             Int       @default(0)
    additionalStorageGB         Int       @default(0) // Additional vector storage in GB
    stripeSubscriptionId        String? // Main Stripe subscription ID (for backward compatibility)
    stripePlanSubscriptionId    String? // Stripe subscription ID for the base plan
    stripeUsersSubscriptionId   String? // Stripe subscription ID for additional users
    stripeStorageSubscriptionId String? // Stripe subscription ID for additional storage
    stripeCustomerId            String? // Stripe customer ID
    createdAt                   DateTime  @default(now())
    updatedAt                   DateTime  @updatedAt

    @@index([tenantId])
    @@index([planId])
}

model VectorStoreUsage {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    usageGB   Float
    timestamp DateTime @default(now())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([timestamp])
}

model WebSearchUsage {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    userId    String   @db.ObjectId
    query     String
    cached    Boolean  @default(false)
    timestamp DateTime @default(now())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([userId])
    @@index([timestamp])
    @@index([createdAt])
}
