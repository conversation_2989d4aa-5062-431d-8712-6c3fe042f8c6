import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import jwt from "jsonwebtoken";
import { authOptions } from "../../../../lib/next-auth";

/**
 * Generate a JWT token for the current user
 *
 * This endpoint generates a JWT token for the current user based on their session.
 * It's used by the client to get a token for API authentication.
 */
export async function GET() {
  try {
    // Get the current session
    const session: any = await getServerSession(authOptions);

    // If there's no session, return an error
    if (!session) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Create a payload with the user information
    const payload = {
      userId: session.userId,
      email: session.user?.email,
      name: session.user?.name,
      // Include the current organization if available
      organizationId: session.currentOrganization?.id,
      // Add any other information you need
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate a token directly
    const secret = process.env.NEXTAUTH_SECRET;
    if (!secret) {
      throw new Error("NEXTAUTH_SECRET is not defined");
    }

    // Create a token that expires in 1 hour
    const token = jwt.sign(payload, secret, { expiresIn: "1h" });

    // Return the token
    return NextResponse.json({ token });
  } catch (error) {
    console.error("Error generating token:", error);
    return NextResponse.json(
      { error: "Failed to generate token" },
      { status: 500 }
    );
  }
}
