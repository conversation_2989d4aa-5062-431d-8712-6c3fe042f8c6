import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

export async function POST(request: any) {
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });

  const input = await request.json();

  try {
    // Use a transaction to ensure atomicity
    const response = await db.$transaction(async (prisma) => {
      const tenant = await prisma.tenant.create({
        data: { ...input, slug: `${input?.slug}-${Date.now()}` },
      });

      await prisma.membership.create({
        data: {
          userId: token.sub,
          tenantId: tenant.id,
          role: "OWNER",
        },
      });

      return tenant;
    });

    return NextResponse.json(
      {
        data: response,
        message: "Successfully created tenant.",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Create tenant transaction failed:", error);
    return NextResponse.json(
      { error: "Internal Server Error: Failed to create tenant." },
      { status: 500 }
    );
  }
}

export async function PUT(request: any) {
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });

  const input = await request.json();

  try {
    // Use a transaction to ensure atomicity
    const response = await db.$transaction(async (prisma) => {
      const tenant = await prisma.tenant.update({
        where: {
          id: input.id,
        },
        data: {
          name: input?.name,
          description: input?.description,
          url: input.url,
        },
      });
      return tenant;
    });

    return NextResponse.json(
      {
        data: response,
        message: "Successfully updated organization.",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Upadate tenant failed:", error);
    return NextResponse.json(
      { error: "Internal Server Error: Failed to update organization." },
      { status: 500 }
    );
  }
}
