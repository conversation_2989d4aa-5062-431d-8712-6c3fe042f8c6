import { apiUrl, fetchJson } from "..";

export const createChatGroup = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/chat/group`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create ChatGroup api calling error": error });
    return { error: "Error Creating The ChatGroup" };
  }
};

export const updateChatGroup = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/chat/group`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update chat-groups api calling error": error });
    return { error: "Error Update The chat-groups" };
  }
};

export const getChatGroup = async ({ userId, tenantId }) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/chat/group?userId=${userId}&tenantId=${tenantId}`
    );
    return response;
  } catch (error) {
    console.error("Error in getChatGroup service:", error);
    throw error;
  }
};

export const deleteChatGroup = async (id) => {
  try {
    const url = `${apiUrl}/chat/group?id=${id}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete ChatGroup api error": error });
    return { error: error.message || "Error deleting ChatGroup" };
  }
};
