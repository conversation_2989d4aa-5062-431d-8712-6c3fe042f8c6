import { NextResponse } from "next/server";
import db from "@/lib/shared-db";

// This is a debug endpoint to check the current subscription data
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Get all subscriptions for the tenant
    const subscriptions = await db.subscription.findMany({
      where: {
        tenantId,
      },
      include: {
        plan: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get the active subscription
    const activeSubscription = subscriptions.find(sub => sub.isActive);

    return NextResponse.json({
      subscriptions,
      activeSubscription,
      count: subscriptions.length,
    });
  } catch (error) {
    console.error("Error fetching subscription debug info:", error);
    return NextResponse.json(
      { error: "Failed to fetch subscription debug info" },
      { status: 500 }
    );
  }
}
