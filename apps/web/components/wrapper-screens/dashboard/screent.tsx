"use client";

import { redirect } from "next/navigation";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { CreateOrganizationButton } from "@/components/organizations/create-organization-button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Building, Users, AlertTriangle, CreditCard } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import Image from "next/image";
import Link from "next/link";
import { getCookie } from "@/utils/cookies";

export default function DashboardPage({ subscription, session }) {
  const { t } = useLanguage();
  // Get current organization and memberships
  const currentOrganization = (session as any)?.currentOrganization;
  const memberships = (session as any)?.memberships || [];
  const tenantId =
    getCookie("currentOrganizationId") ?? memberships[0]?.tenant?.id;
  const userRole = memberships.find(
    (m) => m.tenant.id === currentOrganization?.id
  )?.role;

  // Empty state when user has no organizations
  if (memberships.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-20">
        <div className="rounded-full bg-primary/10 p-6 mb-6">
          <Building className="h-12 w-12 text-primary" />
        </div>
        <h1 className="text-2xl font-semibold mb-2">
          {t("dashboard.welcomeMessage")}
        </h1>
        <p className="text-muted-foreground mb-6 text-center max-w-md">
          {t("dashboard.getStartedMessage")}
        </p>
        <CreateOrganizationButton size="lg" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">
        {t("dashboard.dashboardTitle")}
      </h1>

      {/* Subscription Alert */}
      {!subscription && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>
            {t("subscription.noActiveSubscription") || "No Active Subscription"}
          </AlertTitle>
          <AlertDescription>
            {t("subscription.noSubscriptionMessage") ||
              "You don't have an active subscription. You won't be able to create workspaces or invite members."}
          </AlertDescription>
          <div className="mt-4">
            <Link href="/billing">
              <Button variant="outline" size="sm" className="gap-2">
                <CreditCard className="h-4 w-4" />
                {t("subscription.subscribePlan") || "Subscribe to a Plan"}
              </Button>
            </Link>
          </div>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl flex items-center">
              <Building className="mr-2 h-5 w-5" />
              {t("organization.organizationInfo")}
            </CardTitle>
            <CardDescription>
              {userRole === "OWNER"
                ? t("roles.owner")
                : userRole === "ADMIN"
                  ? t("roles.admin")
                  : t("roles.member")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <span className="font-medium">{t("common.name")}:</span>{" "}
                {currentOrganization?.name}
              </div>
              {currentOrganization?.description && (
                <div>
                  <span className="font-medium">
                    {t("common.description")}:
                  </span>{" "}
                  {currentOrganization.description}
                </div>
              )}
              {currentOrganization?.url && (
                <div>
                  <span className="font-medium">
                    {t("organization.urlLabel")}:
                  </span>{" "}
                  <a
                    href={currentOrganization.url}
                    className="text-primary hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {currentOrganization.url}
                  </a>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <CreateOrganizationButton
              variant="outline"
              className="w-full mt-2"
            />
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl flex items-center">
              <Users className="mr-2 h-5 w-5" />
              {t("common.yourOrganizations")}
            </CardTitle>
            <CardDescription>
              {memberships.length === 1
                ? t("dashboard.organizationCount", {
                    count: memberships.length,
                  })
                : t("dashboard.organizationCountPlural", {
                    count: memberships.length,
                  })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {memberships.map((membership) => (
                <li
                  key={membership.tenant.id}
                  className="flex items-center justify-between p-2 rounded hover:bg-muted"
                >
                  <div className="flex items-center">
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                      {membership.tenant.image ? (
                        <Image
                          src={membership.tenant.image}
                          alt={membership.tenant.name}
                          className="h-8 w-8 rounded-full"
                        />
                      ) : (
                        <Building className="h-4 w-4 text-primary" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium">
                        {membership.tenant.name}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {membership.role === "OWNER"
                          ? t("roles.owner")
                          : membership.role === "ADMIN"
                            ? t("roles.admin")
                            : t("roles.member")}
                      </div>
                    </div>
                  </div>
                  {tenantId === membership.tenant.id && (
                    <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                      {t("common.current")}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <CreateOrganizationButton
              variant="outline"
              className="w-full mt-2"
            />
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
