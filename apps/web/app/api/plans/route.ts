import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import { PlanType } from "@prisma/client";

// Get all plans
export async function GET(request: Request) {
  try {
    const plans = await db.plan.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        includedUsers: "asc",
      },
    });

    return NextResponse.json({ plans });
  } catch (error) {
    console.error("Error fetching plans:", error);
    return NextResponse.json(
      { error: "Failed to fetch plans" },
      { status: 500 }
    );
  }
}

// Create a new plan (admin only)
export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, type, description, includedUsers, additionalUserFee, vectorStoreGB } = body;

    // Validate required fields
    if (!name || !type || !includedUsers || additionalUserFee === undefined || !vectorStoreGB) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Validate plan type
    if (!Object.values(PlanType).includes(type)) {
      return NextResponse.json(
        { error: "Invalid plan type" },
        { status: 400 }
      );
    }

    // Create the plan
    const plan = await db.plan.create({
      data: {
        name,
        type,
        description,
        includedUsers,
        additionalUserFee,
        vectorStoreGB,
        isActive: true,
      },
    });

    return NextResponse.json({ plan }, { status: 201 });
  } catch (error) {
    console.error("Error creating plan:", error);
    return NextResponse.json(
      { error: "Failed to create plan" },
      { status: 500 }
    );
  }
}
