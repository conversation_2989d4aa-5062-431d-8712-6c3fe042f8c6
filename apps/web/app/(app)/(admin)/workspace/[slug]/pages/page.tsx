import WorkspacePages from "@/components/wrapper-screens/workspace/pages-wrapper";
import { getPage } from "@/services";
import { getDriveFolders } from "@/services/src/integration";
import { cookies } from "next/headers";

export default async function pages({ params }: any) {
  const workspaceSlug = params.slug;
  const tenantId = await cookies().get("currentOrganizationId")?.value;
  const userId = await cookies().get("userId")?.value;
  const [pages, folders] = await Promise.all([
    getPage({ workspaceSlug, tenantId, userId }),
    getDriveFolders(tenantId, userId),
  ]);

  return (
    <WorkspacePages
      folder={folders}
      workspaceId={pages?.data?.[0]?.workspaceId}
      tenantId={tenantId}
      workspaceSlug={workspaceSlug}
      pages={pages?.data ?? []}
    />
  );
}
