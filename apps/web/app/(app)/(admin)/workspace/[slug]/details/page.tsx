"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { getWorkspace, updateWorkspace } from "@/services";
import { getCookie } from "@/utils/cookies";
import { useLanguage } from "@/lib/language-context";
import toast from "react-hot-toast";

export default function WorkspaceDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { t } = useLanguage();
  const workspaceSlug = params?.slug as string;

  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [workspace, setWorkspace] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    initials: "",
  });
  const [errors, setErrors] = useState({
    name: "",
    initials: "",
  });

  useEffect(() => {
    const fetchWorkspaceDetails = async () => {
      setIsPageLoading(true);
      try {
        const tenantId = getCookie("currentOrganizationId");
        const userId = getCookie("userId");

        if (!tenantId || !userId || !workspaceSlug) {
          setIsPageLoading(false);
          return;
        }

        const response = await getWorkspace(tenantId, userId, workspaceSlug);
        if (response?.workspace) {
          setWorkspace(response.workspace);
          setFormData({
            name: response.workspace.name || "",
            description: response.workspace.description || "",
            initials: response.workspace.initials || "",
          });
        }
      } catch (error) {
        console.error("Error fetching workspace details:", error);
        toast.error(t("workspace.errorFetchingDetails"));
      } finally {
        setIsPageLoading(false);
      }
    };

    fetchWorkspaceDetails();
  }, [workspaceSlug, t]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;

    if (id === "initials" && value.length > 2) {
      return; // Prevent more than 2 characters for initials
    }

    setFormData((prev) => ({
      ...prev,
      [id]: id === "initials" ? value.toUpperCase() : value,
    }));

    // Clear error when user types
    if (errors[id as keyof typeof errors]) {
      setErrors((prev) => ({
        ...prev,
        [id]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {
      name: "",
      initials: "",
    };

    if (!formData.name.trim()) {
      newErrors.name = t("workspace.nameRequired");
    }

    if (!formData.initials || formData.initials.length !== 2) {
      newErrors.initials = t("workspace.initialsRequired");
    }

    setErrors(newErrors);
    return !newErrors.name && !newErrors.initials;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const tenantId = getCookie("currentOrganizationId");
      const userId = getCookie("userId");

      if (!workspace?.id) {
        toast.error(t("workspace.workspaceNotFound"));
        return;
      }

      const result = await updateWorkspace(
        workspace.id,
        {
          data: {
            name: formData.name,
            description: formData.description,
            initials: formData.initials,
          },
        },
        tenantId,
        userId
      );

      if (result?.workspace) {
        toast.success(t("workspace.updateSuccess"));
        window.location.href = `/workspace/${workspace.slug}`;
      } else {
        toast.error(result?.error || t("workspace.updateFailed"));
      }
    } catch (error) {
      console.error("Error updating workspace:", error);
      toast.error(t("workspace.updateFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (isPageLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <div className="h-8 w-64 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
          <div className="h-4 w-96 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>

        <Card>
          <CardHeader>
            <div className="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
            <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-24 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!workspace) {
    return (
      <div className="flex flex-col items-center justify-center h-full gap-4">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="text-muted-foreground">
          {t("workspace.workspaceNotFound")}
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">
          {t("workspace.workspaceDetails")}
        </h1>
        <p className="text-muted-foreground">
          {t("workspace.manageWorkspaceDetails")}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("workspace.details")}</CardTitle>
          <CardDescription>{t("workspace.detailsDescription")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t("workspace.name")}</Label>
            <Input
              id="name"
              placeholder={t("workspace.namePlaceholder")}
              value={formData.name}
              onChange={handleChange}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-xs text-red-500 mt-1">{errors.name}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">{t("workspace.description")}</Label>
            <Textarea
              id="description"
              placeholder={t("workspace.descriptionPlaceholder")}
              rows={4}
              value={formData.description}
              onChange={handleChange}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="initials">{t("workspace.initials")}</Label>
            <Input
              id="initials"
              placeholder={t("workspace.initialsPlaceholder")}
              maxLength={2}
              className={`uppercase ${errors.initials ? "border-red-500" : ""}`}
              value={formData.initials}
              onChange={handleChange}
            />
            {errors.initials ? (
              <p className="text-xs text-red-500 mt-1">{errors.initials}</p>
            ) : (
              <p className="text-sm text-muted-foreground">
                {t("workspace.initialsDescription")}
              </p>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleCancel}>
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="min-w-[100px]"
          >
            {isLoading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                {t("common.saving")}
              </>
            ) : (
              t("common.save")
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
