import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { hash } from "bcrypt";
import { cookies } from "next/headers";
import { decode } from "next-auth/jwt";
import crypto from "crypto";
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { token, password, name } = body;

    if (!token) {
      return NextResponse.json(
        { error: "Invitation token is required" },
        { status: 400 }
      );
    }

    // Get the current user if they're authenticated
    // Extract the session token from cookies directly
    const cookieStore = cookies();
    const sessionCookie = cookieStore.get("next-auth.session-token")?.value;

    // Decode the session token if it exists
    let sessionToken: any = null;
    if (sessionCookie) {
      try {
        sessionToken = await decode({
          token: sessionCookie,
          secret: process.env.NEXTAUTH_SECRET || "",
        });
      } catch (error) {
        console.error("Error decoding session token:", error);
      }
    }

    // Find the invitation by token
    const invitation = await db.invitation.findUnique({
      where: { token },
      include: {
        tenant: true,
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "Invalid invitation token" },
        { status: 404 }
      );
    }

    // Check if invitation has expired
    if (invitation.expires < new Date()) {
      return NextResponse.json(
        { error: "Invitation has expired" },
        { status: 400 }
      );
    }

    // Check if invitation has already been accepted
    if (invitation.accepted) {
      return NextResponse.json(
        { error: "Invitation has already been accepted" },
        { status: 400 }
      );
    }
    const emailHash = crypto
      .createHash("sha256")
      .update(invitation.email)
      .digest("hex");
    // Check if a user with this email already exists
    let user = await db.user.findUnique({
      where: { emailHash },
    });

    // If no user exists and we don't have a password, we can't create the user
    if (!user && !password) {
      return NextResponse.json(
        { error: "Password is required for new user registration" },
        { status: 400 }
      );
    }

    // If the user is authenticated but has a different email than the invitation
    if (
      sessionToken &&
      sessionToken.email &&
      sessionToken.email !== invitation.email
    ) {
      return NextResponse.json(
        { error: "This invitation was sent to a different email address" },
        { status: 403 }
      );
    }

    // Create a new user if one doesn't exist
    if (!user) {
      const hashedPassword = await hash(password, 12);
      const emailHash = crypto
        .createHash("sha256")
        .update(invitation.email)
        .digest("hex");
      user = await db.user.create({
        data: {
          email: invitation.email,
          emailHash,
          password: hashedPassword,
          name: name || invitation.email.split("@")[0],
          emailVerified: new Date(),
        },
      });
    }

    // Create a membership for the user in the tenant
    const membership = await db.membership.create({
      data: {
        userId: user.id,
        tenantId: invitation.tenantId,
        role: invitation.role,
        customRoleId: invitation?.customRoleId,
      },
      include: {
        tenant: true,
      },
    });

    // Mark the invitation as accepted
    await db.invitation.update({
      where: { id: invitation.id },
      data: { accepted: true },
    });
    if (invitation.role === "OWNER" || invitation.role === "ADMIN") {
      const workspace = await db.workspace.findMany({
        where: {
          tenantId: membership.tenantId,
        },
        select: {
          id: true,
        },
      });
      const workSpaceMember = workspace?.map((item) => ({
        userId: user?.id,
        workspaceId: item?.id,
        membershipId: membership?.id,
        role: invitation.role,
      }));
      // Create membership with OWNER role
      await db.WorkspaceMember.createMany({
        data: workSpaceMember,
      });
    }

    return NextResponse.json({
      data: {
        membership,
        tenant: invitation.tenant,
      },
      message: "Invitation accepted successfully",
    });
  } catch (error) {
    console.error("Accept invitation error:", error);
    return NextResponse.json(
      { error: "Failed to accept invitation" },
      { status: 500 }
    );
  }
}
