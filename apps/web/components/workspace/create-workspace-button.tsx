"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useLanguage } from "@/lib/language-context";
import { CreateGuard } from "@/components/permission-guard";

interface CreateWorkspaceButtonProps {
  size?: "default" | "sm" | "lg" | "icon";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
}

export default function CreateWorkspaceButton({
  size = "default",
  variant = "default",
  className = "mr-2",
}: CreateWorkspaceButtonProps) {
  const { t } = useLanguage();

  return (
    <CreateGuard 
      resource="WORKSPACE"
      fallback={
        <Button 
          size={size} 
          variant="outline" 
          className={className}
          disabled
          title={t("common.noPermission")}
        >
          {t("workspace.createWorkspace")}
        </Button>
      }
    >
      <Link href="/workspace/create">
        <Button 
          size={size} 
          variant={variant} 
          className={className}
        >
          {t("workspace.createWorkspace")}
        </Button>
      </Link>
    </CreateGuard>
  );
}
