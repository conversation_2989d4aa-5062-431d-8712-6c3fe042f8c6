import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      {
        error: "Unauthorized",
      },
      { status: 401 }
    );
  }

  try {
    // Get all tenants with subscription info
    const tenants = await db.tenant.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        createdAt: true,
        Subscription: {
          where: {
            isActive: true,
          },
          select: {
            plan: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Format the tenants
    const formattedTenants = tenants.map((tenant) => ({
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      createdAt: tenant.createdAt,
      plan: tenant.Subscription[0]?.plan?.name || "No Plan",
      hasActiveSubscription: tenant.Subscription.length > 0,
    }));

    return NextResponse.json(formattedTenants);
  } catch (error) {
    console.error("Error fetching tenants:", error);
    return NextResponse.json(
      {
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
