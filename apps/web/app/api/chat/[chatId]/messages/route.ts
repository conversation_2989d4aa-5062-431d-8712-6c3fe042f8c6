import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

export async function POST(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { content, role, metadata, sources, originalMessageId } =
      await req.json();
    if (!content) {
      return new NextResponse("Message content is required", { status: 400 });
    }

    const message = await db.message.create({
      data: {
        content,
        role: role || "user",
        chatId: params.chatId,
        userId: session?.userId,
        metadata: metadata || {},
        sources: sources || null,
        originalMessageId: originalMessageId || null,
      },
    });

    return NextResponse.json(message);
  } catch (error) {
    console.error("[MESSAGE_POST]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function PUT(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const {
      messageId,
      metadata,
      alternatives,
      currentAlternativeIndex,
      sources,
      originalMessageId,
      content,
    } = await req.json();
    if (!messageId) {
      return new NextResponse("Message ID is required", { status: 400 });
    }

    // Create update data object
    const updateData: any = {};
    if (metadata !== undefined) updateData.metadata = metadata;
    if (alternatives !== undefined) updateData.alternatives = alternatives;
    if (currentAlternativeIndex !== undefined)
      updateData.currentAlternativeIndex = currentAlternativeIndex;
    if (sources !== undefined) updateData.sources = sources;
    if (originalMessageId !== undefined)
      updateData.originalMessageId = originalMessageId;
    if (content !== undefined) updateData.content = content;

    const message = await db.message.update({
      where: {
        id: messageId,
        chatId: params.chatId,
        userId: session?.userId,
      },
      data: updateData,
    });

    return NextResponse.json(message);
  } catch (error) {
    console.error("[MESSAGE_PUT]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function GET(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const { searchParams } = new URL(req.url);
    const cursor = searchParams.get("cursor");
    const userId = searchParams.get("userId");
    const limit = parseInt(searchParams.get("limit") || "10");

    // Get all messages for this chat, including regenerated messages
    const messages = await db.message.findMany({
      where: {
        chatId: params.chatId,
        userId,
      },
      take: limit,
      skip: cursor ? 1 : 0,
      cursor: cursor ? { id: cursor } : undefined,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        // Include regenerated messages for each original message
        regeneratedMessages: {
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    let nextCursor = null;
    if (messages.length === limit) {
      nextCursor = messages[messages.length - 1].id;
    }

    return NextResponse.json({
      items: messages,
      nextCursor,
    });
  } catch (error) {
    console.error("[MESSAGE_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
