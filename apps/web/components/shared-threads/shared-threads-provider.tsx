"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  SharedThread,
  getSharedThreads,
  getSharedThreadsUnreadCount,
} from "@/services/src/shared-threads";

interface UnreadCounts {
  privateUnreadCount: number;
  publicUnreadCount: number;
  totalUnreadCount: number;
  totalPrivateThreads: number;
  totalPublicThreads: number;
  totalThreads: number;
}

interface SharedThreadsContextType {
  privateThreads: SharedThread[];
  publicThreads: SharedThread[];
  unreadCounts: UnreadCounts;
  loading: boolean;
  error: string | null;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  refreshThreads: () => Promise<void>;
  markThreadAsRead: (chatId: string) => Promise<void>;
}

const SharedThreadsContext = createContext<
  SharedThreadsContextType | undefined
>(undefined);

export const useSharedThreads = () => {
  const context = useContext(SharedThreadsContext);
  if (!context) {
    throw new Error(
      "useSharedThreads must be used within a SharedThreadsProvider"
    );
  }
  return context;
};

interface SharedThreadsProviderProps {
  children: React.ReactNode;
}

export const SharedThreadsProvider: React.FC<SharedThreadsProviderProps> = ({
  children,
}) => {
  const [privateThreads, setPrivateThreads] = useState<SharedThread[]>([]);
  const [publicThreads, setPublicThreads] = useState<SharedThread[]>([]);
  const [unreadCounts, setUnreadCounts] = useState<UnreadCounts>({
    privateUnreadCount: 0,
    publicUnreadCount: 0,
    totalUnreadCount: 0,
    totalPrivateThreads: 0,
    totalPublicThreads: 0,
    totalThreads: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchThreads = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch both private and public threads
      const [privateResponse, publicResponse] = await Promise.all([
        getSharedThreads({ search: searchTerm, type: "private" }),
        getSharedThreads({ search: searchTerm, type: "public" }),
      ]);

      setPrivateThreads(privateResponse.privateThreads || []);
      setPublicThreads(publicResponse.publicThreads || []);
    } catch (err) {
      console.error("Error fetching shared threads:", err);
      setError("Failed to load shared threads");
    } finally {
      setLoading(false);
    }
  }, [searchTerm]);

  const fetchUnreadCounts = useCallback(async () => {
    try {
      const counts = await getSharedThreadsUnreadCount();
      setUnreadCounts(counts);
    } catch (err) {
      console.error("Error fetching unread counts:", err);
    }
  }, []);

  const refreshThreads = useCallback(async () => {
    await Promise.all([fetchThreads(), fetchUnreadCounts()]);
  }, [fetchThreads, fetchUnreadCounts]);

  const markThreadAsRead = useCallback(
    async (chatId: string) => {
      try {
        // TODO: Implement mark as read functionality
        // await markSharedThreadAsRead(chatId);

        // Update local state to reflect read status
        setPrivateThreads((prev) =>
          prev.map((thread) =>
            thread.chatId === chatId
              ? { ...thread, hasNewActivity: false, unreadCount: 0 }
              : thread
          )
        );

        setPublicThreads((prev) =>
          prev.map((thread) =>
            thread.chatId === chatId
              ? { ...thread, hasNewActivity: false, unreadCount: 0 }
              : thread
          )
        );

        // Refresh unread counts
        await fetchUnreadCounts();
      } catch (err) {
        console.error("Error marking thread as read:", err);
      }
    },
    [fetchUnreadCounts]
  );

  // Initial load
  useEffect(() => {
    refreshThreads();
  }, [refreshThreads]);

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchThreads();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, fetchThreads]);

  // Real-time updates are now handled by the RealTimeNotificationProvider
  // The provider will call refreshThreads when thread updates are received

  const value: SharedThreadsContextType = {
    privateThreads,
    publicThreads,
    unreadCounts,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    refreshThreads,
    markThreadAsRead,
  };

  return (
    <SharedThreadsContext.Provider value={value}>
      {children}
    </SharedThreadsContext.Provider>
  );
};
