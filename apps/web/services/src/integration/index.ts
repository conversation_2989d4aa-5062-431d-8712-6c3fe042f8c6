import {
  apiUrl,
  fetchJson,
  googleDriveService,
  outlookDriveService,
} from "../../index";

export const createIntegration = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/integration`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create Integration api calling error": error });
    return { error: "Error Creating The Integration" };
  }
};

export const updateIntegration = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/integration`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update integrations api calling error": error });
    return { error: "Error Update The Integration" };
  }
};

export const getIntegration = async ({ id = "", tenantId, userId }) => {
  try {
    if (id) {
      const response = await fetchJson(
        `${apiUrl}/integration?id=${id}&tenantId=${tenantId}&userId=${userId}`
      );

      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/integration?tenantId=${tenantId}&userId=${userId}`
    );

    return response;
  } catch (error) {
    console.error("Error in getIntegration service:", error);
    throw error;
  }
};

export const deleteIntegration = async (id, tenantId, userId) => {
  try {
    const url = `${apiUrl}/integration?id=${id}&tenantId=${tenantId}&userId=${userId}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete Integration api error": error });
    return { error: error.message || "Error deleting Integration" };
  }
};

export const getDriveFolders = async (tenantId, userId) => {
  try {
    const [gdriveFolders, outlookFolders] = await Promise.all([
      googleDriveService.listFolders({ tenantId, userId }),
      outlookDriveService.listFolders({ tenantId, userId }),
    ]);
    return { gDrive: gdriveFolders, oneDrive: outlookFolders };
  } catch (error) {
    console.error("Error in getIntegrationList service:", error);
    throw error;
  }
};
