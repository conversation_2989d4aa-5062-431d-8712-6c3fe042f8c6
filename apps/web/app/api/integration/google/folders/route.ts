import { NextRequest, NextResponse } from "next/server";
import { google } from "googleapis";
import db from "@/lib/shared-db";

// Helper function to get Google Drive client
async function getGoogleDriveClient(tenantId: string, userId: string) {
  try {
    // Get Google credentials from database
    const integration = await db.integration.findFirst({
      where: {
        tenantId,
        platform: "GOOGLE",
        userId,
      },
    });

    if (!integration || !integration.accessToken) {
      throw new Error("Google Drive integration not found or not authorized");
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({
      access_token: integration.accessToken,
      refresh_token: integration.refreshToken,
    });

    // Create Drive client
    return google.drive({ version: "v3", auth: oauth2Client });
  } catch (error) {
    console.error("Error getting Google Drive client:", error);
    throw error;
  }
}

export async function POST(req: NextRequest) {
  try {
    const { tenantId, parentId = "root", userId } = await req.json();

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Get Google Drive client
    const drive = await getGoogleDriveClient(tenantId, userId);

    // List folders in Google Drive
    const response = await drive.files.list({
      q: `'${parentId}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false`,
      fields: "files(id, name, mimeType)",
      pageSize: 100,
    });

    // Return folders
    return NextResponse.json({
      folders: response.data.files || [],
    });
  } catch (error: any) {
    console.error("Error listing Google Drive folders:", error);
    return NextResponse.json(
      {
        error: error.message || "Failed to list Google Drive folders",
      },
      { status: 500 }
    );
  }
}
