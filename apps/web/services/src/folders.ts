import { apiUrl, fetchJson } from "..";

export const createFolder = async (data, tenantId, userId) => {
  try {
    const response = await fetch(`${apiUrl}/folders`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create folder api calling error": error });
    return { error: "Error Creating The Folder" };
  }
};

export const updateFolder = async (data, tenantId, userId) => {
  try {
    const response = await fetch(`${apiUrl}/folders`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update folders api calling error": error });
    return { error: "Error Update The folders" };
  }
};

export const getFolder = async ({
  tenantId,
  userId,
  workspaceSlug,
  folderId = "",
}) => {
  try {
    if (folderId) {
      const response = await fetchJson(
        `${apiUrl}/folders?folderId=${folderId}&workspaceSlug=${workspaceSlug}`,
        userId,
        tenantId
      );
      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/folders?workspaceSlug=${workspaceSlug}`,
      userId,
      tenantId
    );
    console.log(
      `Fetched all folders for workspace ${workspaceSlug}:`,
      response
    );
    return response;
  } catch (error) {
    console.error("Error in getFolder service:", error);
    throw error;
  }
};

export const deleteFolder = async (folderId, tenantId, userId) => {
  try {
    const url = `${apiUrl}/folders?id=${folderId}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete folder api error": error });
    return { error: error.message || "Error deleting folder" };
  }
};
