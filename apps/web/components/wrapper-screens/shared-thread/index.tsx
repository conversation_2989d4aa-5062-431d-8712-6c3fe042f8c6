"use client";

import React, { useState, useEffect } from "react";
import { ChatMessage } from "../chat/components/ChatMessage";
import { CommentSidebar } from "./components/CommentSidebar";
import { ThreadHeader } from "./components/ThreadHeader";
import { CitationModal } from "../chat/citation-modal";
import { useLanguage } from "@/lib/language-context";
import { Message, Source } from "../chat/types";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface ShareInfo {
  shareToken: string;
  isPublic: boolean;
  expiresAt?: string;
  createdBy: User;
  createdAt: string;
}

interface Tenant {
  id: string;
  name: string;
  slug: string;
}

interface Chat {
  id: string;
  title?: string;
  description?: string;
  messages: Message[];
  user: User;
  createdAt: string;
  updatedAt: string;
}

interface SharedThreadViewProps {
  chat: Chat;
  shareInfo: ShareInfo;
  tenant: Tenant;
  canComment: boolean;
  currentUser: User | null;
  highlightedMessageId?: string;
}

export const SharedThreadView: React.FC<SharedThreadViewProps> = ({
  chat,
  shareInfo,
  tenant,
  canComment,
  currentUser,
  highlightedMessageId,
}) => {
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(
    null
  );
  const [isCommentSidebarOpen, setIsCommentSidebarOpen] = useState(false);
  const [selectedSource, setSelectedSource] = useState<Source | null>(null);
  const [isCitationModalOpen, setIsCitationModalOpen] = useState(false);

  // Highlight message if specified in URL
  useEffect(() => {
    if (highlightedMessageId) {
      const element = document.getElementById(
        `message-${highlightedMessageId}`
      );
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "center" });
        element.classList.add("highlight-message");
        setTimeout(() => {
          element.classList.remove("highlight-message");
        }, 3000);
      }
    }
  }, [highlightedMessageId]);

  const handleCommentClick = (messageId: string) => {
    setSelectedMessageId(messageId);
    setIsCommentSidebarOpen(true);
  };

  const handleCloseSidebar = () => {
    setIsCommentSidebarOpen(false);
    setSelectedMessageId(null);
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Main Content */}
      <div
        className={`flex-1 flex flex-col ${
          isCommentSidebarOpen ? "mr-96" : ""
        } transition-all duration-300`}
      >
        {/* Thread Header */}
        <ThreadHeader
          chat={chat}
          shareInfo={shareInfo}
          tenant={tenant}
          currentUser={currentUser}
        />

        {/* Messages */}
        <div className="flex-1 overflow-y-auto px-4 py-6">
          <div className="max-w-4xl mx-auto space-y-6">
            {chat.messages.map((message, index) => (
              <div className="my-2 sm:my-4 p-2 sm:p-4 flex h-fit min-h-full flex-col gap-3 sm:gap-4 overflow-y-auto">
                <ChatMessage
                  message={message}
                  index={index}
                  processedMessages={chat.messages}
                  displayIndicesRef={{ current: {} }}
                  isLoading={false}
                  updateDisplayIndex={() => {}}
                  handleFeedback={() => {}}
                  handleRegenerate={() => {}}
                  toggleCitationAccordion={() => {}}
                  setSelectedSource={setSelectedSource}
                  setIsCitationModalOpen={setIsCitationModalOpen}
                  onCommentClick={
                    canComment
                      ? () => handleCommentClick(message.id!)
                      : undefined
                  }
                  isSharedView={true}
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Comment Sidebar */}
      {isCommentSidebarOpen && selectedMessageId && canComment && (
        <CommentSidebar
          messageId={selectedMessageId}
          chatId={chat.id}
          currentUser={currentUser!}
          onClose={handleCloseSidebar}
        />
      )}

      {/* Citation Modal */}
      {selectedSource && (
        <CitationModal
          isOpen={isCitationModalOpen}
          onClose={() => {
            setIsCitationModalOpen(false);
            setSelectedSource(null);
          }}
          source={selectedSource}
        />
      )}

      {/* Highlight styles */}
      <style jsx>{`
        .highlight-message {
          background-color: rgba(59, 130, 246, 0.1);
          border-left: 4px solid rgb(59, 130, 246);
          padding-left: 1rem;
          border-radius: 0.5rem;
        }
      `}</style>
    </div>
  );
};
