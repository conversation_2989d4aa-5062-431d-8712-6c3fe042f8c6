// If the import above doesn't work, you may need to adjust the path based on your project structure

import { addAuthHeaders } from "@/lib/api/auth-token";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const API_VERSION = "/api/v1";

export const workspaceChatService = {
  async chat(query: any) {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/chat?current_user=${query.userId}`,
        {
          method: "POST",
          headers,
          body: JSON.stringify(query),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in chat:", error);
      throw error;
    }
  },
  async uploadForIndexing(query: any, headers: HeadersInit | null = null) {
    try {
      // Update file status to PROCESSING before starting vectorization
      try {
        const updateResponse = await fetch(`/api/files`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            "x-user-id": query.userId,
            "x-tenant-id": query.tenantId,
          },
          body: JSON.stringify({
            id: query.file_id,
            vectorizationStatus: "PROCESSING",
          }),
        });

        if (!updateResponse.ok) {
          console.warn("Failed to update file status to PROCESSING");
        }
      } catch (updateError) {
        console.error("Error updating file status to PROCESSING:", updateError);
        // Continue with vectorization even if status update fails
      }

      // Get authentication headers
      if (!headers) {
        headers = await addAuthHeaders({
          "Content-Type": "application/json",
        });
      }

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/index?current_user=${query.userId}&tenant_id=${query.tenantId}&file_id=${query.file_id}`,
        {
          method: "POST",
          headers,
          body: JSON.stringify(query),
        }
      );

      if (!response.ok) {
        // Update file status to FAILED if vectorization fails
        try {
          await fetch(`/api/files`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              "x-user-id": query.userId,
              "x-tenant-id": query.tenantId,
            },
            body: JSON.stringify({
              id: query.file_id,
              vectorizationStatus: "FAILED",
            }),
          });
        } catch (updateError) {
          console.error("Error updating file status to FAILED:", updateError);
        }

        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Update file status to COMPLETED if vectorization succeeds
      try {
        await fetch(`/api/files`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            "x-user-id": query.userId,
            "x-tenant-id": query.tenantId,
          },
          body: JSON.stringify({
            id: query.file_id,
            vectorizationStatus: "COMPLETED",
            vectorizedAt: new Date().toISOString(),
          }),
        });
      } catch (updateError) {
        console.error("Error updating file status to COMPLETED:", updateError);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in uploadForIndexing:", error);
      throw error;
    }
  },

  async checkAccess() {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/check-access`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in checkAccess:", error);
      throw error;
    }
  },
  async generateTitle(query: string, tenantId: string) {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/generate-title?query=${query}&tenant_id=${tenantId}`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in generateTitle:", error);
      throw error;
    }
  },
  async deleteFile(query: {
    fileId: string;
    tenantId: string;
    workspaceSlug: string;
  }) {
    try {
      // Get authentication headers
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });
      console.log({ headers });
      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/workspace-chat/delete-file?file_id=${query?.fileId}&tenant_id=${query?.tenantId}&workspaceSlug=${query?.workspaceSlug}`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in deleteFile:", error);
      throw error;
    }
  },
};
