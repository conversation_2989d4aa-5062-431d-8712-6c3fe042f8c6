"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  Users,
  Building,
  CreditCard,
  Settings,
  HardDrive,
  MessageSquare,
  Bell,
  ChevronLeft,
  ChevronRight,
  LayoutDashboard,
  Database,
  LifeBuoy,
  Shield,
  FileText,
  Gauge,
  ChevronDown,
  Menu,
  X,
  Home,
} from "lucide-react";
import { Logo } from "./logo";
import { Button } from "@/components/ui/button";
import { useSidebar } from "./sidebar-context";

const sidebarItems = [
  // Overview section
  {
    title: "Overview",
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
        icon: LayoutDashboard,
      },
      {
        title: "Metrics",
        href: "#",
        icon: BarChart3,
        subItems: [
          {
            title: "Tenant Metrics",
            href: "/metrics/tenants",
          },
          {
            title: "User Metrics",
            href: "/metrics/users",
          },
          {
            title: "Financial Metrics",
            href: "/metrics/finance",
          },
        ],
      },
    ],
  },

  // Management section
  {
    title: "Management",
    items: [
      {
        title: "Tenants",
        href: "/tenants",
        icon: Building,
      },
      {
        title: "Users",
        href: "/users",
        icon: Users,
      },
      {
        title: "Subscriptions",
        href: "/subscriptions",
        icon: CreditCard,
      },
    ],
  },

  // Usage & Analytics section
  {
    title: "Usage & Analytics",
    items: [
      {
        title: "Storage Usage",
        href: "/storage",
        icon: HardDrive,
      },
      {
        title: "Token Usage",
        href: "/tokens",
        icon: MessageSquare,
      },
      {
        title: "API Usage",
        href: "/api-usage",
        icon: Database,
      },
      {
        title: "Performance",
        href: "/performance",
        icon: Gauge,
      },
    ],
  },

  // Administration section
  {
    title: "Administration",
    items: [
      {
        title: "Notifications",
        href: "/notifications",
        icon: Bell,
      },
      {
        title: "Security",
        href: "/security",
        icon: Shield,
      },
      {
        title: "Logs",
        href: "/logs",
        icon: FileText,
      },
      {
        title: "Support",
        href: "/support",
        icon: LifeBuoy,
      },
      {
        title: "Settings",
        href: "/settings",
        icon: Settings,
      },
    ],
  },
];

// Mobile menu toggle button that appears in the header
export function MobileMenuToggle() {
  const { mobileOpen, setMobileOpen } = useSidebar();

  return (
    <Button
      variant="ghost"
      size="icon"
      className="lg:hidden"
      onClick={() => setMobileOpen(!mobileOpen)}
    >
      {mobileOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      <span className="sr-only">Toggle menu</span>
    </Button>
  );
}

export function Sidebar() {
  const pathname = usePathname();
  const { collapsed, setCollapsed, mobileOpen, setMobileOpen, isMobile } =
    useSidebar();
  const [expandedSections, setExpandedSections] = React.useState<
    Record<string, boolean>
  >({});

  // Close mobile sidebar when clicking outside
  React.useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (isMobile && mobileOpen) {
        const sidebar = document.getElementById("sidebar");
        if (sidebar && !sidebar.contains(event.target as Node)) {
          setMobileOpen(false);
        }
      }
    };

    document.addEventListener("mousedown", handleOutsideClick);
    return () => document.removeEventListener("mousedown", handleOutsideClick);
  }, [isMobile, mobileOpen, setMobileOpen]);

  // Close mobile sidebar when navigating
  React.useEffect(() => {
    if (isMobile && mobileOpen) {
      setMobileOpen(false);
    }
  }, [pathname, isMobile, mobileOpen, setMobileOpen]);

  // Effect to update main content margin when sidebar collapses/expands
  React.useEffect(() => {
    const mainContent = document.getElementById("main-content");
    if (mainContent) {
      // Only apply margin on desktop
      if (!isMobile) {
        mainContent.style.marginLeft = collapsed ? "70px" : "240px";
      } else {
        mainContent.style.marginLeft = "0";
      }
    }
  }, [collapsed, isMobile]);

  // Initialize expanded sections based on active path
  React.useEffect(() => {
    if (pathname) {
      const newExpandedSections: Record<string, boolean> = {};

      sidebarItems.forEach((section, index) => {
        // Check if any item in this section is active
        const isActive = section.items.some((item) => {
          if (item.subItems) {
            return item.subItems.some((subItem) =>
              pathname.startsWith(subItem.href)
            );
          }
          return pathname.startsWith(item.href);
        });

        newExpandedSections[`section-${index}`] = isActive;
      });

      setExpandedSections(newExpandedSections);
    }
  }, [pathname]);

  const toggleSection = (sectionKey: string) => {
    if (!collapsed) {
      setExpandedSections((prev) => ({
        ...prev,
        [sectionKey]: !prev[sectionKey],
      }));
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      action();
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isMobile && mobileOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 lg:hidden"
          onClick={() => setMobileOpen(false)}
        />
      )}

      <div
        id="sidebar"
        className={cn(
          "fixed left-0 top-0 z-30 flex h-screen flex-col border-r bg-card transition-all duration-300",
          // Desktop states
          !isMobile && (collapsed ? "w-[70px]" : "w-[240px]"),
          // Mobile states
          isMobile && (mobileOpen ? "translate-x-0" : "-translate-x-full"),
          isMobile && "w-[240px] shadow-xl"
        )}
      >
        {/* Collapse button - only on desktop */}
        {!isMobile && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute -right-3 top-6 z-40 h-6 w-6 rounded-full border bg-background shadow-md"
            onClick={() => setCollapsed(!collapsed)}
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {collapsed ? (
              <ChevronRight className="h-3 w-3" />
            ) : (
              <ChevronLeft className="h-3 w-3" />
            )}
          </Button>
        )}

        {/* Logo */}
        <div className="flex h-16 items-center justify-between border-b px-4">
          <Link href="/dashboard" className="flex">
            <Logo isCollapsed={collapsed && !isMobile} />
          </Link>

          {/* Close button - only on mobile */}
          {isMobile && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={() => setMobileOpen(false)}
              aria-label="Close sidebar"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Navigation with scrolling */}
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="grid items-start px-2 text-sm">
            {/* Home link for collapsed mobile view */}
            {isMobile && collapsed && (
              <Link
                href="/dashboard"
                className={cn(
                  "group mb-4 flex items-center justify-center rounded-lg p-2 font-medium transition-all",
                  pathname === "/dashboard"
                    ? "bg-indigo-50 text-indigo-600 dark:bg-indigo-950 dark:text-indigo-400"
                    : "text-muted-foreground hover:bg-muted hover:text-foreground"
                )}
              >
                <Home
                  className={cn(
                    "h-5 w-5",
                    pathname === "/dashboard"
                      ? "text-indigo-600 dark:text-indigo-400"
                      : "text-muted-foreground"
                  )}
                />
                <span className="sr-only">Dashboard</span>
              </Link>
            )}

            {sidebarItems.map((section, sectionIndex) => {
              const sectionKey = `section-${sectionIndex}`;
              const isExpanded = expandedSections[sectionKey];

              // Check if any item in this section is active
              const isSectionActive = section.items.some((item) => {
                if (item.subItems) {
                  return item.subItems.some(
                    (subItem) => pathname?.startsWith(subItem.href)
                  );
                }
                return pathname?.startsWith(item.href);
              });

              return (
                <div key={sectionKey} className="mb-4">
                  {/* Section Header */}
                  {(!collapsed || isMobile) && (
                    <div
                      className={cn(
                        "mb-1 flex cursor-pointer items-center justify-between rounded-md px-3 py-1.5 text-xs font-semibold uppercase transition-colors",
                        isSectionActive
                          ? "text-indigo-600 dark:text-indigo-400"
                          : "text-muted-foreground hover:text-foreground"
                      )}
                      onClick={() => toggleSection(sectionKey)}
                      onKeyDown={(e) =>
                        handleKeyDown(e, () => toggleSection(sectionKey))
                      }
                      tabIndex={0}
                      role="button"
                      aria-expanded={isExpanded}
                      aria-controls={`section-content-${sectionKey}`}
                    >
                      <span>{section.title}</span>
                      <ChevronDown
                        className={cn(
                          "h-3 w-3 transition-transform",
                          isExpanded ? "rotate-0" : "-rotate-90"
                        )}
                      />
                    </div>
                  )}

                  {/* Section Items */}
                  <div
                    id={`section-content-${sectionKey}`}
                    className={cn(
                      "grid gap-1 transition-all",
                      !collapsed && !isExpanded && !isMobile ? "hidden" : "",
                      collapsed && !isMobile ? "items-center" : ""
                    )}
                  >
                    {section.items.map((item, itemIndex) => {
                      const isItemActive = pathname === item.href;
                      const isItemSectionActive = pathname?.startsWith(
                        item.href
                      );
                      const isActive = isItemActive || isItemSectionActive;

                      return item.subItems ? (
                        <div
                          key={`${sectionKey}-item-${itemIndex}`}
                          className="mb-1"
                        >
                          <div
                            className={cn(
                              "group relative flex items-center gap-3 rounded-lg px-3 py-2 font-medium transition-all",
                              pathname?.startsWith(
                                item.subItems[0].href
                                  .split("/")
                                  .slice(0, -1)
                                  .join("/")
                              )
                                ? "bg-indigo-50 text-indigo-600 dark:bg-indigo-950 dark:text-indigo-400"
                                : "text-muted-foreground hover:bg-muted hover:text-foreground"
                            )}
                          >
                            <item.icon
                              className={cn(
                                "h-4 w-4",
                                pathname?.startsWith(
                                  item.subItems[0].href
                                    .split("/")
                                    .slice(0, -1)
                                    .join("/")
                                )
                                  ? "text-indigo-600 dark:text-indigo-400"
                                  : "text-muted-foreground group-hover:text-foreground"
                              )}
                            />
                            {(!collapsed || isMobile) && (
                              <span className="text-sm">{item.title}</span>
                            )}

                            {/* Tooltip for collapsed state */}
                            {collapsed && !isMobile && (
                              <div className="absolute left-full ml-6 hidden rounded-md bg-black px-2 py-1 text-xs text-white group-hover:block">
                                {item.title}
                              </div>
                            )}
                          </div>

                          {(!collapsed || isMobile) && (
                            <div className="ml-7 mt-1 space-y-1 pl-2 pt-1">
                              {item.subItems.map((subItem, subIndex) => {
                                const isSubActive = pathname === subItem.href;
                                return (
                                  <Link
                                    key={`${sectionKey}-item-${itemIndex}-sub-${subIndex}`}
                                    href={subItem.href}
                                    className={cn(
                                      "block rounded-md border-l-2 border-transparent px-2 py-1 text-xs",
                                      isSubActive
                                        ? "border-l-indigo-600 bg-indigo-50 text-indigo-600 dark:border-l-indigo-400 dark:bg-indigo-950 dark:text-indigo-400"
                                        : "text-muted-foreground hover:bg-muted hover:text-foreground"
                                    )}
                                  >
                                    {subItem.title}
                                  </Link>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      ) : (
                        <Link
                          key={`${sectionKey}-item-${itemIndex}`}
                          href={item.href}
                          className={cn(
                            "group relative flex items-center gap-3 rounded-lg px-3 py-2 font-medium transition-all",
                            isActive
                              ? "bg-indigo-50 text-indigo-600 dark:bg-indigo-950 dark:text-indigo-400"
                              : "text-muted-foreground hover:bg-muted hover:text-foreground",
                            collapsed && !isMobile && "justify-center"
                          )}
                        >
                          <item.icon
                            className={cn(
                              "h-4 w-4",
                              isActive
                                ? "text-indigo-600 dark:text-indigo-400"
                                : "text-muted-foreground group-hover:text-foreground"
                            )}
                          />
                          {(!collapsed || isMobile) && (
                            <span className="text-sm">{item.title}</span>
                          )}

                          {/* Tooltip for collapsed state */}
                          {collapsed && !isMobile && (
                            <div className="absolute left-full ml-6 hidden rounded-md bg-black px-2 py-1 text-xs text-white group-hover:block">
                              {item.title}
                            </div>
                          )}

                          {/* Active indicator */}
                          {isActive && (
                            <div className="absolute inset-y-0 left-0 w-1 rounded-full bg-indigo-600 dark:bg-indigo-400" />
                          )}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </nav>
        </div>
      </div>
    </>
  );
}
