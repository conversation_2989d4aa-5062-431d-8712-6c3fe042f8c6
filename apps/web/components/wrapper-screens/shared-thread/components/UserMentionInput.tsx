"use client";

import React, { useState, useRef, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface UserMentionInputProps {
  value: string;
  onChange: (value: string, mentions: string[]) => void;
  placeholder?: string;
  className?: string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

export const UserMentionInput: React.FC<UserMentionInputProps> = ({
  value,
  onChange,
  placeholder,
  className,
  onKeyDown,
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<User[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [mentionQuery, setMentionQuery] = useState("");
  const [mentionStart, setMentionStart] = useState(-1);
  const [mentions, setMentions] = useState<string[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Search for users when @ is typed
  const searchUsers = async (query: string) => {
    if (query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const response = await fetch(
        `/api/users/search?q=${encodeURIComponent(query)}&limit=5`
      );
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.users);
      }
    } catch (error) {
      console.error("Error searching users:", error);
    }
  };

  // Handle text change
  const handleTextChange = (newValue: string) => {
    // Check if we're typing after an @
    const words = newValue.split(/\s/);
    const lastWord = words[words.length - 1];

    if (lastWord.startsWith("@") && lastWord.length > 1) {
      const query = lastWord.slice(1);
      setMentionQuery(query);
      setMentionStart(newValue.lastIndexOf(lastWord));
      setShowSuggestions(true);
      setSelectedIndex(0);
      searchUsers(query);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
    }

    // Extract mentions from the text
    const mentionMatches = newValue.match(/@\[([^\]]+)\]\(([^)]+)\)/g) || [];
    const extractedMentions = mentionMatches
      .map((match) => {
        const userIdMatch = match.match(/@\[[^\]]+\]\(([^)]+)\)/);
        return userIdMatch ? userIdMatch[1] : "";
      })
      .filter(Boolean);

    setMentions(extractedMentions);
    onChange(newValue, extractedMentions);
  };

  // Handle mention selection
  const selectMention = (user: User) => {
    const words = value.split(/\s/);
    const lastWordIndex = words.length - 1;

    // Replace the last word (which should be @query) with the mention
    words[lastWordIndex] = `@[${user.name}](${user.id})`;
    const newValue = words.join(" ") + " ";

    onChange(newValue, [...mentions, user.id]);
    setShowSuggestions(false);
    setMentionStart(-1);
    setMentionQuery("");

    // Focus back to textarea
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newValue.length, newValue.length);
      }
    }, 0);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (showSuggestions && suggestions.length > 0) {
      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setSelectedIndex((prev) => (prev + 1) % suggestions.length);
          break;
        case "ArrowUp":
          e.preventDefault();
          setSelectedIndex(
            (prev) => (prev - 1 + suggestions.length) % suggestions.length
          );
          break;
        case "Enter":
        case "Tab":
          e.preventDefault();
          selectMention(suggestions[selectedIndex]);
          break;
        case "Escape":
          setShowSuggestions(false);
          break;
      }
    }

    onKeyDown?.(e);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        textareaRef.current &&
        !textareaRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="relative">
      <Textarea
        ref={textareaRef}
        value={value}
        onChange={(e) => handleTextChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={className}
      />

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-48 overflow-y-auto"
        >
          {suggestions.map((user, index) => (
            <div
              key={user.id}
              className={`flex items-center gap-2 p-2 cursor-pointer ${
                index === selectedIndex
                  ? "bg-gray-100 dark:bg-gray-700"
                  : "hover:bg-gray-50 dark:hover:bg-gray-750"
              }`}
              onClick={() => selectMention(user)}
            >
              <Avatar className="h-6 w-6">
                <AvatarImage src={user.image} />
                <AvatarFallback className="text-xs">
                  {getInitials(user.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {user.name}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {user.email}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
