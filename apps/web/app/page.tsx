import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { cookies } from "next/headers";

export default async function Page() {
  const session: any = await getServerSession(authOptions);

  // If user is authenticated, redirect to dashboard
  if (!session) {
    return redirect("/sign-in");
  } else if (!session?.memberships || session?.memberships?.length === 0) {
    return redirect("/onboarding");
  }
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;

  const memberships = session?.memberships || [];
  const userRole = memberships.find((m) => m.tenant.id === tenantId)?.role;

  if (!["admin", "owner", "custom"].includes(userRole?.toLowerCase())) {
    return redirect("/ask-ai");
  }

  // Otherwise redirect to sign in
  return redirect("/dashboard");
}
