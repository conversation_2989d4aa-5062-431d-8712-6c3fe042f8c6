import WorkspacePage from "@/components/wrapper-screens/workspace/page-wrapper";
import { getPage, getVectorStoreUsageSummary } from "@/services";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function Page({ params }) {
  const { pageId, slug } = params;

  const tenantId = await cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;
  if (!tenantId || !userId) {
    return redirect("/sign-in");
  }
  const [page, usageSummary] = await Promise.all([
    getPage({
      workspaceSlug: slug,
      tenantId,
      pageId,
      userId,
    }),
    getVectorStoreUsageSummary(tenantId, userId),
  ]);

  return (
    <WorkspacePage
      tenantId={tenantId}
      page={page?.data ?? []}
      workspaceSlug={slug}
      pageId={pageId}
      usageSummary={usageSummary}
    />
  );
}
