variable "storage_account_name" {
  description = "Name of the storage account"
  type        = string
}

variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "location" {
  description = "Azure region where the storage account will be created"
  type        = string
}
variable "environment" {
  description = "Environment name"
  type        = string
}

variable "account_tier" {
  description = "Performance tier of the storage account (Standard or Premium)"
  type        = string
  default     = "Standard"
}

variable "replication_type" {
  description = "Type of replication for the storage account"
  type        = string
  default     = "LRS"
}

