from typing import ClassVar, Sequence

from pydantic import BaseModel


class Document(BaseModel):
    id: str
    name: str
    description: str
    table_name: ClassVar[str] = "documenty"


class SpellCreate(BaseModel):
    id: str
    name: str
    description: str


class SpellUpdate(BaseModel):
    id: str
    name: str
    description: str


class SpellSearchResults(BaseModel):
    results: Sequence[Spell]
