import { redirect } from "next/navigation";
import { SidebarInset, SidebarProvider } from "@/components/layouts/sidebar";
import { AppSidebar } from "@/components/layouts/app-sidebar";
import { SiteHeader } from "@/components/layouts/site-header";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { getWorkspace } from "@/services";
import { cookies } from "next/headers";
import { QuickAskWrapper } from "@/components/quick-ask";
import { GlobalSearchProvider } from "@/components/global-search";

export default async function HomePage({ children }: any) {
  const session: any = await getServerSession(authOptions);

  if (!session) {
    return redirect("/sign-in");
  } else if (!session?.memberships || session?.memberships?.length === 0) {
    return redirect("/onboarding");
  }
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;

  const memberships = session?.memberships || [];
  const userRole = memberships.find((m) => m.tenant.id === tenantId)?.role;

  if (!["admin", "owner", "custom"].includes(userRole?.toLowerCase())) {
    return redirect("/ask-ai");
  }
  const workspace = await getWorkspace(tenantId, session?.userId);

  return (
    <GlobalSearchProvider tenantId={tenantId}>
      <QuickAskWrapper>
        <div className="[--header-height:calc(theme(spacing.14))]">
          <SidebarProvider className="flex flex-col">
            <SiteHeader />
            <div className="flex flex-1">
              <AppSidebar
                workspace={workspace?.workspaces ?? []}
                session={session}
                role={userRole?.toLowerCase()}
              />
              <SidebarInset className="p-4"> {children}</SidebarInset>
            </div>
          </SidebarProvider>
        </div>
      </QuickAskWrapper>
    </GlobalSearchProvider>
  );
}
