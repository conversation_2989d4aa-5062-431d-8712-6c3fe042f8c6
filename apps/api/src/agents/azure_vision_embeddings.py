import os
import requests
from typing import List, Optional
from langchain_core.embeddings import Embeddings
import logging

logger = logging.getLogger(__name__)

class AzureVisionEmbeddings(Embeddings):
    """
    Custom Azure Vision embeddings implementation using the embed-v-4-0 model.
    This implementation follows the Azure Vision API endpoint format.
    """

    def __init__(
        self,
        azure_endpoint: Optional[str] = None,
        api_key: Optional[str] = None,
        deployment_name: Optional[str] = None,
        api_version: str = "2023-12-01-preview",
        model: str = "embed-v-4-0"
    ):
        """
        Initialize Azure Vision embeddings.

        Args:
            azure_endpoint: Azure endpoint URL
            api_key: Azure API key
            deployment_name: Azure deployment name
            api_version: API version
            model: Model name (default: embed-v-4-0)
        """
        self.azure_endpoint = azure_endpoint or os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT")
        self.api_key = api_key or os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY")
        self.deployment_name = deployment_name or os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0")
        self.api_version = api_version
        self.model = model

        if not self.azure_endpoint:
            raise ValueError("Azure endpoint is required")
        if not self.api_key:
            raise ValueError("API key is required")
        if not self.deployment_name:
            raise ValueError("Deployment name is required")

    def _get_endpoint_url(self) -> str:
        """Construct the Azure Vision API endpoint URL."""
        return f"{self.azure_endpoint}/openai/deployments/{self.deployment_name}/embeddings?api-version={self.api_version}"

    def _get_headers(self) -> dict:
        """Get request headers."""
        return {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }

    def _embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of texts or image URLs using Azure Vision API.

        Args:
            texts: List of texts or image URLs to embed

        Returns:
            List of embeddings
        """
        endpoint = self._get_endpoint_url()
        headers = self._get_headers()

        # Prepare the payload following the reference format
        data = {
            "model": self.model,
            "input": texts
        }

        try:
            response = requests.post(endpoint, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()

            # Extract embeddings from response
            embeddings = []
            if "data" in result:
                for item in result["data"]:
                    if "embedding" in item:
                        embeddings.append(item["embedding"])

            if len(embeddings) != len(texts):
                raise ValueError(f"Expected {len(texts)} embeddings, got {len(embeddings)}")

            return embeddings

        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Azure Vision embeddings API: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response text: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in Azure Vision embeddings: {e}")
            raise

    def embed_image(self, image_url: str) -> List[float]:
        """
        Embed a single image URL.

        Args:
            image_url: Image URL (data URL or HTTP URL) to embed

        Returns:
            Single embedding vector
        """
        logger.info("Embedding image using Azure Vision embed-v-4-0")
        embeddings = self._embed_texts([image_url])
        return embeddings[0]

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents.

        Args:
            texts: List of document texts to embed

        Returns:
            List of embeddings
        """
        logger.info(f"Embedding {len(texts)} documents using Azure Vision embed-v-4-0")
        return self._embed_texts(texts)

    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query text.

        Args:
            text: Query text to embed

        Returns:
            Single embedding vector
        """
        logger.info("Embedding query using Azure Vision embed-v-4-0")
        embeddings = self._embed_texts([text])
        return embeddings[0]

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Async version of embed_documents.
        Note: This implementation is synchronous but wrapped for async compatibility.
        """
        return self.embed_documents(texts)

    async def aembed_query(self, text: str) -> List[float]:
        """
        Async version of embed_query.
        Note: This implementation is synchronous but wrapped for async compatibility.
        """
        return self.embed_query(text)
