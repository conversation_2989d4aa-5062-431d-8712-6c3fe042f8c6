import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Get the storage tier
    const storageTier = await db.storageTier.findUnique({
      where: {
        id,
      },
    });

    if (!storageTier) {
      return NextResponse.json(
        { error: "Storage tier not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ storageTier });
  } catch (error) {
    console.error(`Error fetching storage tier ${params.id}:`, error);
    return NextResponse.json(
      { error: "Failed to fetch storage tier" },
      { status: 500 }
    );
  }
}
