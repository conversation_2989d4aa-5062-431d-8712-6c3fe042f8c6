"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  FileText,
  Plus,
  MoreVertical,
  Trash2,
  Edit2,
  Refresh<PERSON>cw,
  Folder,
  FolderPlus,
  ChevronRight,
  LayoutGrid,
  List,
  LayoutList,
} from "lucide-react";
import Link from "next/link";
import { outlookDriveService } from "@/services";
import { updatePage } from "@/services/src/pages";
import { SharePointFolderDialog } from "@/components/integrations/sharepoint-folder-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createPage, deletePage } from "@/services/src/pages";
import toast from "react-hot-toast";
import { googleDriveService } from "@/services";
import { useLanguage } from "@/lib/language-context";
import { fetchWorkspaceBySlugWithCache } from "@/utils/workspace";
import { getCookie } from "@/utils/cookies";
import CreatePageButton from "@/components/workspace/create-page-button";

export default function WorkspacePages({
  tenantId,
  pages,
  workspaceSlug,
  folder,
  workspaceId,
}) {
  const userId = getCookie("userId") ?? "";
  const router = useRouter();
  const { t } = useLanguage();
  const [workspaceTitle, setWorkspaceTitle] = useState("");

  // Fetch the workspace name when component mounts
  useEffect(() => {
    const getWorkspaceName = async () => {
      try {
        const workspace = await fetchWorkspaceBySlugWithCache(workspaceSlug);
        if (workspace && workspace.name) {
          setWorkspaceTitle(workspace.name);
        } else {
          // Fallback to formatting the slug if workspace data is not available
          const formattedTitle = workspaceSlug
            .split("-")
            .slice(0, -1)
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
          setWorkspaceTitle(formattedTitle);
        }
      } catch (error) {
        console.error("Error fetching workspace name:", error);
        // Fallback to formatting the slug
        const formattedTitle = workspaceSlug
          .split("-")
          .slice(0, -1)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        setWorkspaceTitle(formattedTitle);
      }
    };

    getWorkspaceName();
  }, [workspaceSlug]);

  // State for pages
  const [newPageName, setNewPageName] = useState("");
  const [updatePages, setUpdatePages]: any = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreatePageOpen, setIsCreatePageOpen]: any = useState(null);
  const [syncFolderOpen, setSyncFolderOpen]: any = useState(null);
  const [newFolderName, setNewFolderName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [folders, setFolders]: any = useState(folder);
  const [pageId, setPageId]: any = useState("");

  // View options - can be "tiles", "grid", or "list"
  const [viewMode, setViewMode] = useState<"tiles" | "grid" | "list">("tiles");

  // Create a new page
  const handleCreatePage = async () => {
    if (newPageName.trim()) {
      try {
        setIsLoading(true);
        const newPage = await createPage(
          {
            name: newPageName,
            workspaceSlug: workspaceSlug,
            tenantId: tenantId,
          },
          tenantId,
          userId
        );
        setIsLoading(false);

        if (newPage?.data?.id) {
          setIsCreatePageOpen(null);
          setNewPageName("");
          toast.success(newPage?.message ?? t("workspace.pageCreatedSuccess"));
          router.refresh();
        } else {
          toast.error(newPage?.error ?? t("workspace.failedToCreatePage"));
        }
      } catch (error) {
        console.error("Error creating page:", error);
      }
    }
  };
  // update a page
  const handleRenamePage = async () => {
    if (newPageName.trim()) {
      try {
        const newPage = await updatePage(
          {
            id: updatePages,
            name: newPageName,
            workspaceSlug: workspaceSlug,
            tenantId: tenantId,
          },
          tenantId,
          userId
        );

        if (newPage?.data?.id) {
          setIsCreatePageOpen(null);
          setNewPageName("");
          toast.success(newPage?.message ?? t("workspace.pageCreatedSuccess"));
          router.refresh();
        } else {
          toast.error(newPage?.error ?? t("workspace.failedToCreatePage"));
        }
      } catch (error) {
        console.error("Error creating page:", error);
      }
    }
  };

  // Delete a page
  const handleDeletePage = async (pageId: string) => {
    // Don't allow deleting the last page
    if (pages.length <= 1) {
      return;
    }
    const deletedPage = await deletePage(pageId, tenantId, userId);

    if (deletedPage?.message) {
      toast.success(deletedPage?.message ?? t("workspace.pageDeletedSuccess"));
      router.refresh();
    } else {
      toast.error(deletedPage?.error ?? t("workspace.failedToDeletePage"));
    }
  };

  // Filter pages based on search term
  const filteredPages = pages.filter((page: any) =>
    page.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateFolder = async (id: string, pageId: string) => {
    if (!newFolderName.trim()) return;
    toast.loading(t("workspace.creatingAndSyncingFolder"));
    setIsLoading(true);
    try {
      let newFolder: any, folder: any;
      if (syncFolderOpen === "gDrive") {
        newFolder = {
          name: newFolderName,
          parentId: id,
          tenantId,
          userId,
        };
        folder = await googleDriveService.createFolder(newFolder);
      } else {
        newFolder = {
          name: newFolderName,
          mimeType: "application/vnd.google-apps.folder",
          tenantId,
          userId,
          parentFolderId: id,
        };
        folder = await outlookDriveService.createFolder(newFolder);
      }
      toast.remove();
      if (folder?.id) {
        let data: any = {};
        if (syncFolderOpen === "gDrive") {
          data = {
            gDriveFolderId: folder.id,
          };
        } else {
          data = {
            oneDriveFolderId: folder.id,
          };
        }
        await updatePage(
          {
            ...data,
            id: pageId,
            workspaceSlug: workspaceSlug,
            tenantId: tenantId,
          },
          tenantId,
          userId
        );
        toast.success(t("workspace.folderCreatedAndSyncedSuccess"));
        setNewFolderName("");
        setSyncFolderOpen(null);
      } else {
        toast.error(t("workspace.errorCreatingFolder"));
      }
    } catch (error) {
      console.error("Error creating folder:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFolderClick = async (folder: any, id: string) => {
    toast.loading(t("workspace.syncingFolder"));
    let data = {};
    if (syncFolderOpen === "gDrive") {
      data = {
        gDriveFolderId: folder.id,
      };
    } else {
      // Handle SharePoint site folders
      if (folder.siteId && folder.driveId) {
        data = {
          oneDriveFolderId: folder.id,
          sharePointSiteId: folder.siteId,
          sharePointDriveId: folder.driveId,
        };
      } else {
        // Legacy OneDrive support
        data = {
          oneDriveFolderId: folder.id,
        };
      }
    }
    const upload = await updatePage(
      {
        ...data,
        id,
        workspaceSlug: workspaceSlug,
        tenantId: tenantId,
      },
      tenantId,
      userId
    );
    toast.remove();
    if (upload?.data?.id) {
      toast.success(t("workspace.folderSyncedSuccess"));
      setSyncFolderOpen(null);
      router.refresh();
    } else {
      toast.error(t("workspace.errorSyncingFolder"));
    }
  };

  const getCurrentFolders = () => {
    if (folders?.[syncFolderOpen]?.error) {
      toast.remove();
      toast.error(t("workspace.connectAccountInSettings"));
      setSyncFolderOpen(null);
      return [];
    }
    if (syncFolderOpen === "gDrive") {
      return folders?.gDrive?.data || [];
    } else {
      return folders?.oneDrive?.value || [];
    }
  };

  return (
    <div className="h-full px-1 py-2 md:px-4">
      {/* Header with title */}
      <div className="flex items-center justify-between">
        <div className="">
          <h1 className="text-2xl font-bold md:text-3xl tracking-tight">
            {workspaceTitle} {t("workspace.workspace")}
          </h1>
          <p className="text-muted-foreground mt-1">
            {workspaceTitle} {t("workspace.workspace")}
          </p>
        </div>
        <Button
          onClick={() => router.push(`/workspace/${workspaceSlug}/members`)}
        >
          <Plus className="h-4 w-4 mr-2" />
          {t("workspace.addRemoveMember")}
        </Button>
      </div>

      {/* SharePoint-like command bar */}
      <div className="flex flex-col md:flex-row justify-between gap-3 mb-6 bg-muted/30 p-3 rounded-md">
        <div className="flex flex-wrap gap-2">
          {/* + New Page button */}
          <CreatePageButton onClick={() => setIsCreatePageOpen("create")} />
        </div>

        <div className="flex gap-3 items-center">
          {/* Search */}
          <div className="relative">
            <Input
              type="search"
              placeholder={t("workspace.searchPages")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-9 max-w-64 min-w-40 pr-8"
            />
            <svg
              className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          {/* View options */}
          <div className="flex gap-1 items-center">
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "list"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.listView")}
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "grid"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.gridView")}
              onClick={() => setViewMode("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`h-8 w-8 ${
                viewMode === "tiles"
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              title={t("workspace.tileView")}
              onClick={() => setViewMode("tiles")}
            >
              <LayoutList className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Pages listing */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">{t("workspace.allPages")}</h2>
          {filteredPages.length > 0 && (
            <p className="text-sm text-muted-foreground">
              {filteredPages.length} {}
              {filteredPages.length !== 1
                ? t("workspace.page")
                : t("workspace.pages")}
            </p>
          )}
        </div>

        {/* List View */}
        {viewMode === "list" && (
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="text-left px-4 py-2 font-medium text-sm">
                    {t("workspace.name")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm hidden md:table-cell">
                    {t("workspace.created")}
                  </th>
                  <th className="text-left px-4 py-2 font-medium text-sm w-20">
                    {t("workspace.actions")}
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredPages.map((page: any) => (
                  <tr key={page.id} className="border-t hover:bg-muted/20">
                    <td className="px-4 py-2">
                      <Link
                        href={`/workspace/${workspaceSlug}/page/${page.id}`}
                        className="flex items-center gap-2 hover:underline"
                      >
                        <FileText className="h-5 w-5 text-blue-500 shrink-0" />
                        <span
                          className="overflow-hidden text-ellipsis whitespace-nowrap font-medium max-w-[200px] inline-block"
                          title={page.name}
                        >
                          {page.name}
                        </span>
                      </Link>
                    </td>
                    <td className="px-4 py-2 text-sm text-muted-foreground hidden md:table-cell">
                      {new Date(page.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-4 flex items-center py-2 w-20">
                      {(page?.gDriveFolderId || page?.oneDriveFolderId) && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <RefreshCcw
                              className="h-4 w-4 cursor-pointer"
                              onClick={async (e) => {
                                e.stopPropagation();
                                toast.loading(
                                  t("workspace.checkingSyncStatus")
                                );
                                try {
                                  if (page?.gDriveFolderId) {
                                    const driveSync =
                                      await googleDriveService.checkAndSyncFolder(
                                        {
                                          folderId: page.gDriveFolderId,
                                          tenantId,
                                          workspaceSlug,
                                          pageId: page?.id,
                                          recursive: true,
                                          checkSubFiles: true,
                                          userId,
                                        }
                                      );
                                    toast.remove();
                                    if (driveSync?.synced) {
                                      toast.success(
                                        t("workspace.filesAndFoldersInSync", {
                                          provider: "Google Drive",
                                        })
                                      );
                                    } else if (driveSync?.error) {
                                      toast.error(driveSync.error);
                                    } else if (driveSync?.syncedFiles) {
                                      toast.success(
                                        t("workspace.synchronizedFiles", {
                                          count: driveSync.syncedFiles,
                                          provider: "Google Drive",
                                        })
                                      );
                                    } else {
                                      toast.success(
                                        t(
                                          "workspace.folderSyncedSuccessfully",
                                          {
                                            provider: "Google Drive",
                                          }
                                        )
                                      );
                                    }
                                  } else {
                                    // Check if this is a SharePoint site folder
                                    const syncOptions: any = {
                                      folderId: page.oneDriveFolderId,
                                      tenantId,
                                      workspaceSlug,
                                      pageId: page?.id,
                                      recursive: true,
                                      checkSubFiles: true,
                                      slug: workspaceSlug,
                                      userId,
                                    };

                                    // Add SharePoint site and drive IDs if available
                                    if (
                                      page.sharePointSiteId &&
                                      page.sharePointDriveId
                                    ) {
                                      syncOptions.siteId =
                                        page.sharePointSiteId;
                                      syncOptions.driveId =
                                        page.sharePointDriveId;
                                    }

                                    const driveSync =
                                      await outlookDriveService.syncFolder(
                                        syncOptions
                                      );
                                    toast.remove();
                                    if (driveSync?.synced) {
                                      toast.success(
                                        t("workspace.filesAndFoldersInSync", {
                                          provider: "SharePoint",
                                        })
                                      );
                                    } else if (driveSync?.error) {
                                      toast.error(driveSync.error);
                                    } else if (driveSync?.syncedFiles) {
                                      toast.success(
                                        t("workspace.synchronizedFiles", {
                                          count: driveSync.syncedFiles,
                                          provider: "SharePoint",
                                        })
                                      );
                                    } else {
                                      toast.success(
                                        t(
                                          "workspace.folderSyncedSuccessfully",
                                          {
                                            provider: "SharePoint",
                                          }
                                        )
                                      );
                                    }
                                  }
                                  // Add similar sync check for OneDrive
                                } catch (error) {
                                  console.error("Sync error:", error);
                                  toast.error(
                                    t("workspace.failedToSyncFolder")
                                  );
                                }
                              }}
                            />
                          </TooltipTrigger>
                          <TooltipContent align="center">
                            {page?.gDriveFolderId && page?.oneDriveFolderId
                              ? t("workspace.googleDriveAndOneDrive")
                              : page?.gDriveFolderId
                                ? t("workspace.googleDrive")
                                : t("workspace.oneDrive")}{" "}
                            {t("workspace.sync")}
                          </TooltipContent>
                        </Tooltip>
                      )}
                      <MoreButton
                        handleDeletePage={handleDeletePage}
                        setSyncFolderOpen={setSyncFolderOpen}
                        setPageId={setPageId}
                        setIsCreatePageOpen={setIsCreatePageOpen}
                        setNewPageName={setNewPageName}
                        setUpdatePages={setUpdatePages}
                        pages={pages}
                        page={page}
                        isPage={true}
                      />
                      <FolderDialog
                        pageId={pageId}
                        syncFolderOpen={syncFolderOpen}
                        setSyncFolderOpen={setSyncFolderOpen}
                        getCurrentFolders={getCurrentFolders}
                        handleFolderClick={handleFolderClick}
                        page={page}
                        newFolderName={newFolderName}
                        handleCreateFolder={handleCreateFolder}
                        folder={folder}
                        isLoading={isLoading}
                        setNewFolderName={setNewFolderName}
                        tenantId={tenantId}
                      />
                    </td>
                  </tr>
                ))}

                {filteredPages.length === 0 && (
                  <tr>
                    <td
                      colSpan={3}
                      className="px-4 py-8 text-center text-muted-foreground"
                    >
                      {t("workspace.noPagesFound")}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Grid View */}
        {viewMode === "grid" && (
          <div>
            {filteredPages.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                {filteredPages.map((page: any) => (
                  <Link
                    key={page.id}
                    href={`/workspace/${workspaceSlug}/page/${page.id}`}
                    className="group relative bg-card hover:bg-muted/20 rounded-md border p-3 flex flex-col items-center text-center hover:underline cursor-pointer w-full"
                  >
                    <div className="absolute flex items-center top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      {(page?.gDriveFolderId || page?.oneDriveFolderId) && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <RefreshCcw
                              className="h-4 w-4 cursor-pointer"
                              onClick={async (e) => {
                                e.stopPropagation();
                                toast.loading(
                                  t("workspace.checkingSyncStatus")
                                );
                                try {
                                  if (page?.gDriveFolderId) {
                                    const driveSync =
                                      await googleDriveService.checkAndSyncFolder(
                                        {
                                          folderId: page.gDriveFolderId,
                                          tenantId,
                                          workspaceSlug,
                                          pageId: page?.id,
                                          recursive: true,
                                          checkSubFiles: true,
                                          userId,
                                        }
                                      );
                                    toast.remove();
                                    if (driveSync?.synced) {
                                      toast.success(
                                        t("workspace.filesAndFoldersInSync", {
                                          provider: "Google Drive",
                                        })
                                      );
                                    } else if (driveSync?.error) {
                                      toast.error(driveSync.error);
                                    } else if (driveSync?.syncedFiles) {
                                      toast.success(
                                        t("workspace.synchronizedFiles", {
                                          count: driveSync.syncedFiles,
                                          provider: "Google Drive",
                                        })
                                      );
                                    } else {
                                      toast.success(
                                        t(
                                          "workspace.folderSyncedSuccessfully",
                                          {
                                            provider: "Google Drive",
                                          }
                                        )
                                      );
                                    }
                                  } else {
                                    // Prepare sync options
                                    const syncOptions: any = {
                                      folderId: page.oneDriveFolderId,
                                      tenantId,
                                      workspaceSlug,
                                      pageId: page?.id,
                                      recursive: true,
                                      checkSubFiles: true,
                                      slug: workspaceSlug,
                                      userId,
                                    };

                                    // Add SharePoint site and drive IDs if available
                                    if (
                                      page.sharePointSiteId &&
                                      page.sharePointDriveId
                                    ) {
                                      syncOptions.siteId =
                                        page.sharePointSiteId;
                                      syncOptions.driveId =
                                        page.sharePointDriveId;
                                    }

                                    const driveSync =
                                      await outlookDriveService.syncFolder(
                                        syncOptions
                                      );
                                    toast.remove();
                                    if (driveSync?.synced) {
                                      toast.success(
                                        t("workspace.filesAndFoldersInSync", {
                                          provider: "OneDrive",
                                        })
                                      );
                                    } else if (driveSync?.error) {
                                      toast.error(driveSync.error);
                                    } else if (driveSync?.syncedFiles) {
                                      toast.success(
                                        t("workspace.synchronizedFiles", {
                                          count: driveSync.syncedFiles,
                                          provider: "OneDrive",
                                        })
                                      );
                                    } else {
                                      toast.success(
                                        t(
                                          "workspace.folderSyncedSuccessfully",
                                          {
                                            provider: "OneDrive",
                                          }
                                        )
                                      );
                                    }
                                  }
                                } catch (error) {
                                  console.error("Sync error:", error);
                                  toast.error(
                                    t("workspace.failedToSyncFolder")
                                  );
                                }
                              }}
                            />
                          </TooltipTrigger>
                          <TooltipContent align="center">
                            {page?.gDriveFolderId && page?.oneDriveFolderId
                              ? t("workspace.googleDriveAndOneDrive")
                              : page?.gDriveFolderId
                                ? t("workspace.googleDrive")
                                : t("workspace.oneDrive")}{" "}
                            {t("workspace.sync")}
                          </TooltipContent>
                        </Tooltip>
                      )}
                      <MoreButton
                        setPageId={setPageId}
                        handleDeletePage={handleDeletePage}
                        setUpdatePages={setUpdatePages}
                        setSyncFolderOpen={setSyncFolderOpen}
                        setIsCreatePageOpen={setIsCreatePageOpen}
                        setNewPageName={setNewPageName}
                        pages={pages}
                        page={page}
                        isPage={true}
                      />
                      <FolderDialog
                        pageId={pageId}
                        syncFolderOpen={syncFolderOpen}
                        setSyncFolderOpen={setSyncFolderOpen}
                        getCurrentFolders={getCurrentFolders}
                        handleFolderClick={handleFolderClick}
                        page={page}
                        newFolderName={newFolderName}
                        handleCreateFolder={handleCreateFolder}
                        folder={folder}
                        isLoading={isLoading}
                        setNewFolderName={setNewFolderName}
                        tenantId={tenantId}
                      />
                    </div>
                    <FileText className="h-12 w-12 text-blue-500 mb-2" />
                    <div className="w-full px-1">
                      <span
                        className="text-sm font-medium overflow-hidden text-ellipsis whitespace-nowrap inline-block w-full"
                        title={page.name}
                      >
                        {page.name}
                      </span>
                    </div>
                    <span className="text-xs text-muted-foreground mt-1">
                      {t("workspace.created")}{" "}
                      {new Date(page.createdAt).toLocaleDateString()}
                    </span>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-8">
                {t("workspace.noPagesFound")}
              </p>
            )}
          </div>
        )}

        {/* Tiles View */}
        {viewMode === "tiles" && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {filteredPages.map((page: any) => (
              <Card
                key={page.id}
                className="group hover:border-primary transition-colors"
              >
                <CardHeader className="p-4 pb-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center flex-1 min-w-0 pr-4">
                      <FileText className="h-8 w-8 mr-2 text-blue-500 flex-shrink-0" />
                      <CardTitle
                        className="text-base overflow-hidden text-ellipsis whitespace-nowrap w-full"
                        title={page.name}
                      >
                        {page.name}
                      </CardTitle>
                    </div>
                    {(page?.gDriveFolderId || page?.oneDriveFolderId) && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <RefreshCcw
                            className="h-5 w-5 cursor-pointer"
                            onClick={async (e) => {
                              e.stopPropagation();
                              toast.loading(t("workspace.checkingSyncStatus"));
                              try {
                                if (page?.gDriveFolderId) {
                                  const driveSync =
                                    await googleDriveService.checkAndSyncFolder(
                                      {
                                        folderId: page.gDriveFolderId,
                                        tenantId,
                                        workspaceSlug,
                                        pageId: page?.id,
                                        recursive: true,
                                        checkSubFiles: true,
                                        userId,
                                      }
                                    );
                                  toast.remove();

                                  if (driveSync?.synced) {
                                    toast.success(
                                      t("workspace.filesAndFoldersInSync", {
                                        provider: "Google Drive",
                                      })
                                    );
                                  } else if (driveSync?.error) {
                                    toast.error(driveSync.error);
                                  } else if (driveSync?.syncedFiles) {
                                    toast.success(
                                      t("workspace.synchronizedFiles", {
                                        count: driveSync.syncedFiles,
                                        provider: "Google Drive",
                                      })
                                    );
                                  } else {
                                    toast.success(
                                      t("workspace.folderSyncedSuccessfully", {
                                        provider: "Google Drive",
                                      })
                                    );
                                  }
                                } else {
                                  // Prepare sync options
                                  const syncOptions: any = {
                                    folderId: page.oneDriveFolderId,
                                    tenantId,
                                    workspaceSlug,
                                    pageId: page?.id,
                                    recursive: true,
                                    checkSubFiles: true,
                                    slug: workspaceSlug,
                                    userId,
                                  };

                                  // Add SharePoint site and drive IDs if available
                                  if (
                                    page.sharePointSiteId &&
                                    page.sharePointDriveId
                                  ) {
                                    syncOptions.siteId = page.sharePointSiteId;
                                    syncOptions.driveId =
                                      page.sharePointDriveId;
                                  }

                                  const driveSync =
                                    await outlookDriveService.syncFolder(
                                      syncOptions
                                    );
                                  toast.remove();
                                  if (driveSync?.synced) {
                                    toast.success(
                                      t("workspace.filesAndFoldersInSync", {
                                        provider: "OneDrive",
                                      })
                                    );
                                  } else if (driveSync?.error) {
                                    toast.error(driveSync.error);
                                  } else if (driveSync?.syncedFiles) {
                                    toast.success(
                                      t("workspace.synchronizedFiles", {
                                        count: driveSync.syncedFiles,
                                        provider: "OneDrive",
                                      })
                                    );
                                  } else {
                                    toast.success(
                                      t("workspace.folderSyncedSuccessfully", {
                                        provider: "OneDrive",
                                      })
                                    );
                                  }
                                }
                              } catch (error) {
                                console.error("Sync error:", error);
                                toast.error(t("workspace.failedToSyncFolder"));
                              }
                            }}
                          />
                        </TooltipTrigger>
                        <TooltipContent align="center">
                          {page?.gDriveFolderId && page?.oneDriveFolderId
                            ? t("workspace.googleDriveAndOneDrive")
                            : page?.gDriveFolderId
                              ? t("workspace.googleDrive")
                              : t("workspace.oneDrive")}{" "}
                          {t("workspace.sync")}
                        </TooltipContent>
                      </Tooltip>
                    )}
                    <div className="flex-shrink-0">
                      <MoreButton
                        setPageId={setPageId}
                        setUpdatePages={setUpdatePages}
                        handleDeletePage={handleDeletePage}
                        setIsCreatePageOpen={setIsCreatePageOpen}
                        setNewPageName={setNewPageName}
                        pages={pages}
                        page={page}
                        setSyncFolderOpen={setSyncFolderOpen}
                        isPage={true}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-2">
                  <p className="text-sm text-muted-foreground">
                    {t("workspace.created")}{" "}
                    {new Date(page.createdAt).toLocaleDateString()}
                  </p>
                </CardContent>
                <CardFooter className="p-4 pt-0">
                  <Button
                    variant="ghost"
                    asChild
                    className="w-full justify-start p-0 h-8 hover:underline cursor-pointer"
                  >
                    <Link href={`/workspace/${workspaceSlug}/page/${page.id}`}>
                      {t("workspace.openPage")}
                    </Link>
                  </Button>
                </CardFooter>
                <FolderDialog
                  pageId={pageId}
                  syncFolderOpen={syncFolderOpen}
                  setSyncFolderOpen={setSyncFolderOpen}
                  getCurrentFolders={getCurrentFolders}
                  handleFolderClick={handleFolderClick}
                  page={page}
                  newFolderName={newFolderName}
                  handleCreateFolder={handleCreateFolder}
                  folder={folder}
                  isLoading={isLoading}
                  setNewFolderName={setNewFolderName}
                  tenantId={tenantId}
                />
              </Card>
            ))}

            {filteredPages.length === 0 && (
              <p className="text-muted-foreground text-center py-8 col-span-full">
                {t("workspace.noPagesFound")}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Create Page Dialog */}
      <Dialog
        open={isCreatePageOpen !== null}
        onOpenChange={() => setIsCreatePageOpen(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isCreatePageOpen === "rename"
                ? t("workspace.renamePage")
                : t("workspace.createNewPage")}
            </DialogTitle>
            <DialogDescription>
              {isCreatePageOpen === "rename"
                ? t("workspace.enterNewPageName")
                : t("workspace.enterPageName")}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="pageName">{t("workspace.pageName")}</Label>
              <Input
                id="pageName"
                value={newPageName}
                onChange={(e) => setNewPageName(e.target.value)}
                placeholder={t("workspace.enterPageNamePlaceholder")}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreatePageOpen(null)}>
              {t("common.cancel")}
            </Button>
            <Button
              disabled={isLoading || !newPageName.trim()}
              onClick={
                isCreatePageOpen === "rename"
                  ? handleRenamePage
                  : handleCreatePage
              }
            >
              {isCreatePageOpen === "rename"
                ? t("workspace.renamePage")
                : t("workspace.createNewPage")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

const MoreButton = ({
  setIsCreatePageOpen,
  setNewPageName,
  page,
  pages,
  handleDeletePage,
  setUpdatePages,
  setPageId,
  setSyncFolderOpen = (provider: any) => {},
  isPage = false,
}) => {
  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";
  const tenantId = getCookie("tenantId") ?? "";

  const handleSyncFolderClick = (e: React.MouseEvent, provider: string) => {
    e.stopPropagation();
    setPageId(page.id);
    setSyncFolderOpen(provider);
  };

  const handleRemoveSync = async (e: React.MouseEvent, pageId: string) => {
    e.stopPropagation();
    try {
      toast.loading(t("workspace.removingSync"));
      const result = await updatePage(
        {
          id: pageId,
          gDriveFolderId: null,
          oneDriveFolderId: null,
        },
        tenantId,
        userId
      );

      toast.remove();
      if (result?.data?.id) {
        toast.success(t("workspace.syncRemovedSuccess"));
        window.location.reload(); // Refresh the page to update the UI
      } else {
        toast.error(t("workspace.failedToRemoveSync"));
      }
    } catch (error) {
      console.error("Error removing sync:", error);
      toast.error(t("workspace.failedToRemoveSync"));
    }
  };

  // Determine if the page is synced with any cloud storage
  const isSynced = page?.gDriveFolderId || page?.oneDriveFolderId;
  const syncProvider =
    page?.gDriveFolderId && page?.oneDriveFolderId
      ? "both Google Drive and SharePoint"
      : page?.gDriveFolderId
        ? "Google Drive"
        : "SharePoint";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("workspace.actions")}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            setIsCreatePageOpen("rename");
            setUpdatePages(page?.id);
            setNewPageName(page.name);
          }}
        >
          <Edit2 className="h-4 w-4 mr-2" />
          {t("workspace.rename")}
        </DropdownMenuItem>
        {isPage && (
          <>
            {/* Only show sync options if not already synced */}
            {!isSynced ? (
              <>
                <DropdownMenuItem
                  onClick={(e) => handleSyncFolderClick(e, "oneDrive")}
                  className="cursor-pointer"
                >
                  <Folder className="mr-2 h-4 w-4" />
                  <span>{t("workspace.syncWithSharePoint")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => handleSyncFolderClick(e, "gDrive")}
                  className="cursor-pointer"
                >
                  <Folder className="mr-2 h-4 w-4" />
                  <span>{t("workspace.syncWithGoogleDrive")}</span>
                </DropdownMenuItem>
              </>
            ) : (
              <DropdownMenuItem
                onClick={(e) => handleRemoveSync(e, page.id)}
                className="cursor-pointer"
              >
                <RefreshCcw className="mr-2 h-4 w-4" />
                <span>
                  {t("workspace.removeSync")} ({syncProvider})
                </span>
              </DropdownMenuItem>
            )}
          </>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => handleDeletePage(page.id)}
          className="text-red-500"
          disabled={pages.length <= 1}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {t("common.delete")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const FolderDialog = ({
  syncFolderOpen,
  setSyncFolderOpen,
  getCurrentFolders,
  handleFolderClick,
  page,
  pageId,
  newFolderName,
  handleCreateFolder,
  folder,
  isLoading,
  setNewFolderName,
  tenantId,
}) => {
  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";

  // If this is a SharePoint/OneDrive sync, use the SharePoint dialog
  if (syncFolderOpen === "oneDrive") {
    return (
      <SharePointFolderDialog
        isOpen={syncFolderOpen === "oneDrive"}
        onClose={() => setSyncFolderOpen(null)}
        tenantId={tenantId}
        onFolderSelect={(folderId, folderName, siteId, driveId) => {
          // Create a folder object that matches the expected format
          const folderObj = {
            id: folderId,
            name: folderName,
            siteId: siteId,
            driveId: driveId,
          };
          handleFolderClick(folderObj, pageId);
        }}
        onCreateFolder={async (name, siteId, driveId, folderId) => {
          // Create a folder in SharePoint
          const newFolder = {
            name,
            tenantId,
            userId,
            siteId,
            driveId,
            parentFolderId: folderId,
          };

          const folder = await outlookDriveService.createFolder(newFolder);

          if (folder?.id) {
            // Update the page with the new folder ID
            await updatePage(
              {
                oneDriveFolderId: folder.id,
                id: pageId,
                workspaceSlug: page.workspaceSlug,
                tenantId: tenantId,
                sharePointSiteId: siteId,
                sharePointDriveId: driveId,
              },
              tenantId,
              userId
            );
          }
        }}
        isLoading={isLoading}
      />
    );
  }

  // For Google Drive or other providers, use the original dialog
  return (
    <Dialog
      open={syncFolderOpen !== null}
      onOpenChange={() => setSyncFolderOpen(null)}
    >
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("workspace.syncFolder")}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2 max-h-[300px] overflow-y-auto">
            {getCurrentFolders().map((folder: any) => (
              <div
                key={folder.id}
                onClick={() => handleFolderClick(folder, pageId)}
                className={`flex items-center justify-between p-2 rounded-md hover:bg-muted cursor-pointer ${
                  page?.gDriveFolderId === folder.id ||
                  page?.oneDriveFolderId === folder.id
                    ? "bg-secondary "
                    : ""
                }`}
              >
                <div className="flex items-center gap-2">
                  <Folder className="h-4 w-4" />
                  <span>{folder.name}</span>
                </div>
                {folder.children && <ChevronRight className="h-4 w-4" />}
              </div>
            ))}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setSyncFolderOpen(null)}>
            {t("workspace.close")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
