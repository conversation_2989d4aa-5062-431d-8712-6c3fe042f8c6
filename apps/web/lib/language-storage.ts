import { getCookie, set<PERSON><PERSON>ie } from "@/utils/cookies";

// Key used in local storage to store the current language
const LANGUAGE_STORAGE_KEY = "language";

// Function to get the current language from storage
export function getStoredLanguage(): string | null {
  if (typeof window === "undefined") {
    return null;
  }

  return getCookie(LANGUAGE_STORAGE_KEY) ?? "";
}

// Function to set the current language in storage
export function setStoredLanguage(language: string): void {
  if (typeof window === "undefined") {
    return;
  }

  setCookie(LANGUAGE_STORAGE_KEY, language);

  // Dispatch a custom event to notify components about the language change
  window.dispatchEvent(
    new CustomEvent("languageChanged", { detail: language })
  );
}

// Function to get the preferred language
export function getPreferredLanguage(): string {
  // First try to get from local storage
  const storedLanguage = getStoredLanguage();
  if (storedLanguage) {
    return storedLanguage;
  }

  // If not found in storage and we're in the browser, try to get from navigator
  if (typeof window !== "undefined" && window.navigator) {
    const browserLanguages = window.navigator.languages || [
      window.navigator.language,
    ];

    // Look for German or English in browser preferences
    for (const lang of browserLanguages) {
      const langCode = lang.split("-")[0].toLowerCase();
      if (langCode === "de") {
        return "de";
      }
      if (langCode === "en") {
        return "en";
      }
    }
  }

  // Default to English if no preference found
  return "en";
}
