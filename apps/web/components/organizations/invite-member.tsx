"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { UserPlus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

// Create schema with translations
const createFormSchema = (t) =>
  z.object({
    email: z.string().email({
      message: t("inviteMember.validEmail"),
    }),
    role: z.enum(["MEMBER", "ADMIN"], {
      required_error: t("inviteMember.selectRole"),
    }),
  });

export function InviteMember() {
  const { data: session } = useSession();
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useLanguage();

  // Create schema with translations
  const formSchema = createFormSchema(t);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      role: "MEMBER",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);

    try {
      const currentOrg = (session as any)?.currentOrganization;
      if (!currentOrg?.id) {
        throw new Error("No organization selected");
      }

      const response = await fetch("/api/admin/invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: values.email,
          role: values.role,
          tenantId: currentOrg.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Check for user limit error
        if (data.error === "subscription.userLimitReached" && data.data) {
          const { currentCount, limit } = data.data;
          toast.error(
            t("subscription.userLimitMessage", { currentCount, limit })
          );
        } else {
          // Handle other errors
          toast.error(data.error || t("inviteMember.invitationFailed"));
        }
        throw new Error(data.error || "Failed to send invitation");
      }

      toast.success(t("inviteMember.invitationSent", { email: values.email }));
      form.reset();
      setOpen(false);
    } catch (error) {
      console.error("Failed to send invitation:", error);
      // Error is already displayed in the try block
    } finally {
      setIsLoading(false);
    }
  };

  // Only show if user is logged in and has admin/owner role
  const currentOrg = (session as any)?.currentOrganization;
  const userRole = (session as any)?.memberships?.find(
    (m) => m.tenant.id === currentOrg?.id
  )?.role;

  const canInvite = userRole === "OWNER" || userRole === "ADMIN";

  if (!canInvite) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="icon" variant="ghost">
          <UserPlus className="h-4 w-4" />
          <span className="sr-only">{t("inviteMember.inviteMember")}</span>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("inviteMember.inviteTeamMember")}</DialogTitle>
          <DialogDescription>
            {t("inviteMember.inviteDescription")}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("inviteMember.emailAddress")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("inviteMember.emailPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("roles.role")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t("inviteMember.selectRolePlaceholder")}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ADMIN">{t("roles.admin")}</SelectItem>
                      <SelectItem value="MEMBER">
                        {t("roles.member")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading
                  ? t("inviteMember.sending")
                  : t("inviteMember.sendInvitation")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
