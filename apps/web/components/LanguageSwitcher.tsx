import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useConfig } from "nextra-theme-docs";
import { useTheme } from "next-themes";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Button } from "./ui/button";
import { Check } from "lucide-react";
import { FlagIcon } from "./flags";

export function LanguageSwitcher() {
  const router = useRouter();
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const i18n = [
    {
      locale: "en",
      text: "English",
    },
    {
      locale: "de",
      text: "Deutsch",
    },
  ];

  // After mounting, we have access to the theme
  useEffect(() => setMounted(true), []);

  // Get current locale from path
  const currentPath = router.asPath;
  const isDocsPath = currentPath.startsWith("/docs/");

  if (!isDocsPath || !i18n) {
    return null;
  }
  console.log({ currentPath });

  // Determine current locale from path
  const currentLocale = currentPath.startsWith("/docs/en")
    ? "en"
    : currentPath.startsWith("/docs/de")
      ? "de"
      : "en"; // Default to English

  // Get current locale display text
  const currentLocaleConfig = i18n.find((l) => l.locale === currentLocale);

  // Function to switch language
  const switchLanguage = (locale: string) => {
    if (locale === currentLocale) return;

    // Create new path by replacing the locale part
    const newPath = currentPath.replace(
      `/docs/${currentLocale}`,
      `/docs/${locale}`
    );
    // Navigate to the new path
    router.push(newPath);
  };

  // Get the current theme for styling
  const currentTheme = theme === "system" ? resolvedTheme : theme;
  const isDarkTheme = currentTheme === "dark";

  // Styles
  const buttonStyle = {
    height: "2rem", // h-8
    paddingLeft: "0.5rem", // px-2
    paddingRight: "0.5rem", // px-2
    display: "flex", // flex
    alignItems: "center", // items-center
    color: mounted ? (isDarkTheme ? "#f8fafc" : "#0f172a") : "inherit", // Dark theme: slate-50, Light theme: slate-900
    transition: "color 0.2s ease, background-color 0.2s ease", // Smooth transition for hover
  };

  const flagIconStyle = {
    height: "1rem", // h-4
    width: "1.5rem", // w-6
    marginRight: "0.5rem", // mr-2
    display: "inline-block",
    verticalAlign: "middle",
  };

  // Only show theme-dependent styles after mounting to prevent hydration mismatch
  const dropdownContentStyle = {
    width: "10rem", // w-40
    padding: "0.5rem", // p-2
    backgroundColor: mounted
      ? isDarkTheme
        ? "#1e293b"
        : "#f0f0f0f0"
      : "transparent", // Dark theme: slate-800, Light theme: white
    cursor: "pointer",
    borderRadius: "0.5rem", // rounded-md
    color: mounted ? (isDarkTheme ? "#f8fafc" : "#0f172a") : "inherit", // Dark theme: slate-50, Light theme: slate-900
  };

  const menuItemBaseStyle = {
    display: "flex", // flex
    alignItems: "center", // items-center
    justifyContent: "space-between", // justify-between
    paddingTop: "0.5rem", // py-2
    paddingBottom: "0.5rem", // py-2
    transition: "background-color 0.2s ease", // Smooth transition for hover
  };

  const checkIconStyle = {
    height: "1rem", // h-4
    width: "1rem", // w-4
    marginLeft: "0.5rem", // ml-2
  };

  // Default navbar variant
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          style={buttonStyle}
          onMouseEnter={(e) => {
            if (mounted) {
              e.currentTarget.style.backgroundColor = isDarkTheme
                ? "#334155"
                : "#f1f5f9";
            }
          }}
          onMouseLeave={(e) => {
            if (mounted) {
              e.currentTarget.style.backgroundColor = "transparent";
            }
          }}
        >
          <FlagIcon locale={currentLocale} style={flagIconStyle} />
          <span>{currentLocaleConfig?.text || "Language"}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" style={dropdownContentStyle}>
        {i18n.map((locale) => {
          const isActive = locale.locale === currentLocale;
          return (
            <DropdownMenuItem
              key={locale.locale}
              onClick={() => switchLanguage(locale.locale)}
              style={{
                ...menuItemBaseStyle,
                ...(isActive
                  ? {
                      backgroundColor: "var(--accent)", // bg-accent
                      color: "var(--accent-foreground)", // text-accent-foreground
                    }
                  : {}),
              }}
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                <FlagIcon locale={locale.locale} style={flagIconStyle} />
                <span>{locale.text}</span>
              </div>
              {isActive && <Check style={checkIconStyle} />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
