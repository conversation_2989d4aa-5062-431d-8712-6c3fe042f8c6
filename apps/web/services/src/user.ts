import { apiUrl } from "..";

/**
 * Update user profile information
 */
export const updateUserProfile = async (
  userId: string,
  data: {
    forename: string;
    surname: string;
    email: string;
  }
) => {
  try {
    const url = `${apiUrl}/user?userId=${userId}`;
    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error updating user profile" };
    }

    return await response.json();
  } catch (error) {
    console.error("Update profile API error:", error);
    return { error: "Error updating profile. Please try again." };
  }
};

/**
 * Delete user account
 */
export const deleteUserAccount = async (userId: string) => {
  try {
    const url = `${apiUrl}/user?userId=${userId}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error deleting user account" };
    }

    return { success: true };
  } catch (error) {
    console.error("Delete account API error:", error);
    return { error: "Error deleting account. Please try again." };
  }
};

/**
 * Update user password
 */
export const updateUserPassword = async (
  userId: string,
  data: {
    currentPassword: string;
    newPassword: string;
  }
) => {
  try {
    const url = `${apiUrl}/user/${userId}/password`;
    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error updating password" };
    }

    return { success: true };
  } catch (error) {
    console.error("Update password API error:", error);
    return { error: "Error updating password. Please try again." };
  }
};

export const getUserAccount = async (userId: string) => {
  try {
    const url = `${apiUrl}/user?userId=${userId}`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error deleting user account" };
    }

    return await response.json();
  } catch (error) {
    console.error("fetching account API error:", error);
    return { error: "Error fetching account. Please try again." };
  }
};
