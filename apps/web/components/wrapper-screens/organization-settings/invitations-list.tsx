"use client";

import { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Copy,
  Loader2,
  Mail,
  RefreshCcw,
  Shield,
  User,
  UserPlus,
  X,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { getLocale } from "@/utils/date-locale";

interface InvitationsListProps {
  tenantId: string;
  userRole: string;
  members: any;
}

import { cancelInvitation, resendInvitation } from "@/services";

// Removed mock functions as we're now using real API endpoints

export function InvitationsList({
  tenantId,
  userRole,
  members,
}: InvitationsListProps) {
  const { t, language } = useLanguage();
  const [invitations, setInvitations] = useState<any[]>(members);
  const [loadingId, setLoadingId] = useState<string | null>(null);
  const [confirmCancel, setConfirmCancel] = useState<{
    open: boolean;
    id: string | null;
    email: string;
  }>({
    open: false,
    id: null,
    email: "",
  });

  const baseUrl =
    typeof window !== "undefined"
      ? `${window.location.protocol}//${window.location.host}`
      : "";

  const handleCancelInvitation = async () => {
    if (!confirmCancel.id) return;

    setLoadingId(confirmCancel.id);
    try {
      const result = await cancelInvitation(confirmCancel.id, tenantId);
      if (result.message) {
        setInvitations(
          invitations.filter((inv) => inv.id !== confirmCancel.id)
        );
        toast.success(t("invitations.cancelSuccess"));
      } else {
        toast.error(t("invitations.cancelError"));
      }
    } catch (error) {
      console.error("Failed to cancel invitation", error);
      toast.error(t("invitations.cancelError"));
    } finally {
      setLoadingId(null);
      setConfirmCancel({ open: false, id: null, email: "" });
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    setLoadingId(invitationId);
    try {
      const result = await resendInvitation(invitationId, tenantId);
      if (result.message) {
        toast.success(t("invitations.resendSuccess"));
      } else {
        toast.error(t("invitations.resendError"));
      }
    } catch (error) {
      console.error("Failed to resend invitation", error);
      toast.error(t("invitations.resendError"));
    } finally {
      setLoadingId(null);
    }
  };

  const copyInviteLink = (token: string) => {
    const inviteUrl = `${baseUrl}/invitations/${token}`;
    navigator.clipboard.writeText(inviteUrl);
    toast.success(t("invitations.linkCopied"));
  };

  if (invitations.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <UserPlus className="h-6 w-6 text-primary" />
        </div>
        <h3 className="mt-4 text-lg font-semibold">
          {t("invitations.noPending")}
        </h3>
        <p className="mt-2 text-sm text-muted-foreground">
          {t("invitations.inviteMembers")}
        </p>
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("common.email")}</TableHead>
            <TableHead>{t("common.role")}</TableHead>
            <TableHead>{t("invitations.sent")}</TableHead>
            <TableHead>{t("invitations.expires")}</TableHead>
            {["ADMIN", "OWNER"]?.includes(userRole) && (
              <TableHead className="text-right">
                {t("common.actions")}
              </TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {invitations.map((invitation) => (
            <TableRow key={invitation.id}>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback>
                      <Mail className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="font-medium">{invitation.email}</div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  {invitation.role === "ADMIN" ? (
                    <Shield className="h-4 w-4 mr-1 text-amber-500" />
                  ) : (
                    <User className="h-4 w-4 mr-1 text-muted-foreground" />
                  )}
                  <span>
                    {invitation.role.charAt(0) +
                      invitation.role.slice(1).toLowerCase()}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                {formatDistanceToNow(new Date(invitation.createdAt), {
                  addSuffix: true,
                  locale: getLocale(language),
                })}
              </TableCell>
              <TableCell>
                {formatDistanceToNow(new Date(invitation.expires), {
                  addSuffix: true,
                  locale: getLocale(language),
                })}
              </TableCell>
              {["ADMIN", "OWNER"]?.includes(userRole) && (
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => copyInviteLink(invitation.token)}
                      disabled={
                        userRole === "ADMIN" && invitation?.role === "OWNER"
                      }
                      title={t("invitations.copyLink")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleResendInvitation(invitation.id)}
                      title={t("invitations.resend")}
                      disabled={
                        loadingId === invitation.id ||
                        (userRole === "ADMIN" && invitation?.role === "OWNER")
                      }
                    >
                      {loadingId === invitation.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCcw className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() =>
                        setConfirmCancel({
                          open: true,
                          id: invitation.id,
                          email: invitation.email,
                        })
                      }
                      title={t("invitations.cancel")}
                      disabled={
                        loadingId === invitation.id ||
                        (userRole === "ADMIN" && invitation?.role === "OWNER")
                      }
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog
        open={confirmCancel.open}
        onOpenChange={(open) => setConfirmCancel({ ...confirmCancel, open })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("invitations.cancelInvitation")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("invitations.cancelConfirmation", {
                email: confirmCancel.email,
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("invitations.keep")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelInvitation}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("invitations.cancelInvitation")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
