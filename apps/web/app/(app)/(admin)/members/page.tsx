import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { MemberList } from "@/components/wrapper-screens/settings/member-list";
import { authOptions } from "@/lib/next-auth";
import { getMembers } from "@/services";
import { getTranslations } from "@/lib/server-i18n";

export default async function Page() {
  const session: any = await getServerSession(authOptions);
  const { t } = await getTranslations();

  if (!session) {
    return redirect("/sign-in");
  }

  if (!session.memberships || session.memberships.length === 0) {
    return redirect("/onboarding");
  }

  // Get current tenant (first one for now, could be extended to support tenant selection)
  const currentTenant = session.memberships[0].tenant;
  const member = await getMembers(currentTenant.id, session.userId);

  return (
    <div className="container py-6">
      <h1 className="text-2xl font-bold mb-6">{t("memberList.teamMembers")}</h1>
      <MemberList
        member={member ?? []}
        currentUserId={session.userId}
        tenantId={currentTenant.id}
      />
    </div>
  );
}
