"use client";

import { useLanguage } from "@/lib/language-context";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";

import { Shield } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { PrivacyPolicyHeader } from "./header";
import { useEffect } from "react";
import { getCookie } from "@/utils/cookies";

export default function PrivacyPolicyScreen() {
  const { t, setLanguage } = useLanguage();
  useEffect(() => {
    if (typeof window !== "undefined") {
      const language = getCookie("language");
      if (!language) {
        setLanguage("de");
      }
    }
  }, [typeof window]);

  return (
    <>
      <PrivacyPolicyHeader />
      <div className="container mx-auto py-8 px-4 max-w-5xl">
        <Card className="mb-8 border-t-4 border-t-primary shadow-lg">
          <CardHeader className="pb-3 bg-muted/30">
            <CardTitle className="text-3xl font-bold flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="h-8 w-8 text-primary" />
                {t("privacyPolicy.title")}
              </div>
              <span className="text-sm font-normal text-muted-foreground bg-primary/10 px-3 py-1 rounded-full">
                {t("privacyPolicy.version")}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  0. {t("privacyPolicy.scope")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.scopeContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  1. {t("privacyPolicy.preamble")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.preambleContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  2. {t("privacyPolicy.controller")}
                </h2>
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-md text-muted-foreground">
                  {t("privacyPolicy.controllerContent")}
                </pre>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  3. {t("privacyPolicy.purposes")}
                </h2>
                <div className="space-y-2 text-muted-foreground">
                  <p>{t("privacyPolicy.purposesContent1")}</p>
                  <p>{t("privacyPolicy.purposesContent2")}</p>
                  <p>{t("privacyPolicy.purposesContent3")}</p>
                  <p>{t("privacyPolicy.purposesContent4")}</p>
                  <p>{t("privacyPolicy.purposesContent5")}</p>
                  <p>{t("privacyPolicy.purposesContent6")}</p>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  4. {t("privacyPolicy.categories")}
                </h2>
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-md text-muted-foreground">
                  {t("privacyPolicy.categoriesContent")}
                </pre>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  5. {t("privacyPolicy.legalBases")}
                </h2>
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-md text-muted-foreground">
                  {t("privacyPolicy.legalBasesContent")}
                </pre>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  6. {t("privacyPolicy.retention")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.retentionContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  7. {t("privacyPolicy.recipients")}
                </h2>
                <div className="space-y-2 text-muted-foreground">
                  <ReactMarkdown>
                    {t("privacyPolicy.recipientsContent1")}
                  </ReactMarkdown>
                  <ReactMarkdown>
                    {t("privacyPolicy.recipientsContent2")}
                  </ReactMarkdown>
                  <ReactMarkdown>
                    {t("privacyPolicy.recipientsContent3")}
                  </ReactMarkdown>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  7a. {t("privacyPolicy.ai")}
                </h2>
                <div className="space-y-2">
                  <p className="text-muted-foreground">
                    {t("privacyPolicy.aiContent1")}
                  </p>
                  <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-md text-muted-foreground">
                    {t("privacyPolicy.aiContent2")}
                    {t("privacyPolicy.aiContent3")}
                    {t("privacyPolicy.aiContent4")}
                  </pre>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  7b. {t("privacyPolicy.thirdParty")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.thirdPartyContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  8. {t("privacyPolicy.transfers")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.transfersContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  9. {t("privacyPolicy.security")}
                </h2>
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-md text-muted-foreground">
                  {t("privacyPolicy.securityContent")}
                </pre>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  10. {t("privacyPolicy.rights")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.rightsContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  11. {t("privacyPolicy.cookies")}
                </h2>
                <div className="space-y-2">
                  <p className="text-muted-foreground">
                    {t("privacyPolicy.cookiesContent1")}
                  </p>
                  <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-md text-muted-foreground">
                    {t("privacyPolicy.cookiesContent2")}
                  </pre>
                  <p className="text-muted-foreground">
                    {t("privacyPolicy.cookiesContent5")}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  12. {t("privacyPolicy.minors")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.minorsContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  13. {t("privacyPolicy.changes")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.changesContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  14. {t("privacyPolicy.disclaimer")}
                </h2>
                <p className="text-muted-foreground">
                  {t("privacyPolicy.disclaimerContent")}
                </p>
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-semibold border-b pb-2">
                  15. {t("privacyPolicy.contact")}
                </h2>
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-md text-muted-foreground">
                  {t("privacyPolicy.contactContent")}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
