import { apiUrl } from "..";

export const createTenant = async (data) => {
  try {
    const url = `${apiUrl}/admin/tenant`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create tenant api calling error": error });
    return { error: "Error Creating The Tenant" };
  }
};
export const updateTenant = async (id, data) => {
  try {
    const url = `${apiUrl}/admin/tenant`;
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ ...data, id }),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create tenant api calling error": error });
    return { error: "Error Creating The Tenant" };
  }
};

export const inviteUser = async (data) => {
  try {
    const url = `${apiUrl}/admin/invite`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Invite user api error": error });
    return { error: error.message || "Error inviting user" };
  }
};

export const verifyInvitation = async (token) => {
  try {
    const url = `${apiUrl}/invitations/verify?token=${token}`;
    const response = await fetch(url);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Verify invitation api error": error });
    return { error: error.message || "Error verifying invitation" };
  }
};

export const acceptInvitation = async (data) => {
  try {
    const url = `${apiUrl}/invitations/accept`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Accept invitation api error": error });
    return { error: error.message || "Error accepting invitation" };
  }
};

export const getMembers = async (tenantId, userId) => {
  try {
    const url = `${apiUrl}/admin/invite?tenantId=${tenantId}&userId=${userId}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Get members api error": error });
    return { error: "Error fetching tenant members" };
  }
};
export const getPendingMembers = async (tenantId, userId) => {
  try {
    const url = `${apiUrl}/invitations/pending?tenantId=${tenantId}&userId=${userId}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Get members api error": error });
    return { error: "Error fetching tenant members" };
  }
};

export const updateMemberRole = async (membershipId, role) => {
  try {
    const url = `${apiUrl}/admin/members/${membershipId}`;
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ role }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Update member role api error": error });
    return { error: error.message || "Error updating member role" };
  }
};

export const removeMember = async (membershipId) => {
  try {
    const url = `${apiUrl}/admin/members/${membershipId}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Remove member api error": error });
    return { error: error.message || "Error removing member" };
  }
};

export const cancelInvitation = async (invitationId, tenantId) => {
  try {
    const url = `${apiUrl}/admin/invite?id=${invitationId}&tenantId=${tenantId}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Cancel invitation api error": error });
    return { error: error.message || "Error cancelling invitation" };
  }
};

export const resendInvitation = async (id, tenantId) => {
  try {
    const url = `${apiUrl}/admin/invite?id=${id}&tenantId=${tenantId}`;
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Resend invitation api error": error });
    return { error: error.message || "Error resending invitation" };
  }
};
