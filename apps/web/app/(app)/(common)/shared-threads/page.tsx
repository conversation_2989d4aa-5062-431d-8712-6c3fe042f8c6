"use client";

import React, { useState } from "react";
import {
  Users,
  Search,
  Filter,
  RefreshCw,
  Lock,
  Globe,
  MessageCircle,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/lib/language-context";
import {
  SharedThreadsProvider,
  useSharedThreads,
} from "@/components/shared-threads/shared-threads-provider";
import { SharedThreadItem } from "@/components/shared-threads/shared-thread-item";

const SharedThreadsContent: React.FC = () => {
  const { t } = useLanguage();
  const {
    privateThreads,
    publicThreads,
    unreadCounts,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    refreshThreads,
  } = useSharedThreads();

  const [filterType, setFilterType] = useState<"all" | "unread">("all");
  const [activeTab, setActiveTab] = useState<"all" | "private" | "public">(
    "all"
  );

  const handleRefresh = async () => {
    await refreshThreads();
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  // Filter threads based on filter type
  const filteredPrivateThreads = privateThreads.filter((thread) => {
    if (filterType === "unread") {
      return thread.hasNewActivity;
    }
    return true;
  });

  const filteredPublicThreads = publicThreads.filter((thread) => {
    if (filterType === "unread") {
      return thread.hasNewActivity;
    }
    return true;
  });

  const allThreads = [...filteredPrivateThreads, ...filteredPublicThreads].sort(
    (a, b) =>
      new Date(b.lastActivityAt).getTime() -
      new Date(a.lastActivityAt).getTime()
  );

  const getThreadsForTab = () => {
    switch (activeTab) {
      case "private":
        return filteredPrivateThreads;
      case "public":
        return filteredPublicThreads;
      default:
        return allThreads;
    }
  };

  const threadsToShow = getThreadsForTab();

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Users className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Shared Threads</h1>
              {unreadCounts.totalUnreadCount > 0 && (
                <Badge variant="destructive" className="h-6 px-2">
                  {unreadCounts.totalUnreadCount} unread
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
              >
                <RefreshCw
                  className={cn("h-4 w-4 mr-2", loading && "animate-spin")}
                />
                Refresh
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search shared threads..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 pr-9"
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            {/* Tabs */}
            <Tabs
              value={activeTab}
              onValueChange={(value) => setActiveTab(value as any)}
            >
              <TabsList className="grid w-full max-w-md grid-cols-3">
                <TabsTrigger value="all" className="flex items-center gap-2">
                  <MessageCircle className="h-4 w-4" />
                  All ({allThreads.length})
                </TabsTrigger>
                <TabsTrigger
                  value="private"
                  className="flex items-center gap-2"
                >
                  <Lock className="h-4 w-4" />
                  Private ({filteredPrivateThreads.length})
                </TabsTrigger>
                <TabsTrigger value="public" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Public ({filteredPublicThreads.length})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  {filterType === "all" ? "All" : "Unread"}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setFilterType("all")}>
                  All Threads
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterType("unread")}>
                  Unread Only
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <div className="px-6 py-6 h-full">
          {error && (
            <div className="text-center text-sm text-destructive bg-destructive/10 p-4 rounded-lg mb-6">
              {error}
            </div>
          )}

          {loading ? (
            <div className={cn("space-y-4")}>
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-32 w-full" />
                </div>
              ))}
            </div>
          ) : threadsToShow.length > 0 ? (
            <div className={cn("space-y-4")}>
              {threadsToShow.map((thread) => (
                <SharedThreadItem key={thread.id} thread={thread} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <MessageCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                No shared threads found
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm
                  ? "No threads match your search criteria"
                  : filterType === "unread"
                    ? "No unread threads at the moment"
                    : activeTab === "private"
                      ? "No private shared threads available"
                      : activeTab === "public"
                        ? "No public shared threads available"
                        : "No shared threads available"}
              </p>
              {searchTerm && (
                <Button variant="outline" onClick={clearSearch}>
                  Clear Search
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default function SharedThreadsPage() {
  return (
    <SharedThreadsProvider>
      <SharedThreadsContent />
    </SharedThreadsProvider>
  );
}
