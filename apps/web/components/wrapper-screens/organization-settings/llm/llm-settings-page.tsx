"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useLanguage } from "@/lib/language-context";

const createFormSchema = (t: any) =>
  z.object({
    provider: z.enum(["openai", "azure", "deepseek"]),
    apiKey: z.string().min(1, t("llmSettings.apiKeyRequired")),
    model: z.string().min(1, t("llmSettings.modelRequired")),
    azureEndpoint: z.string().optional(),
    azureDeploymentName: z.string().optional(),
  });

export default function LLMSettingsPage({ settings }: { settings: any }) {
  const { t } = useLanguage();
  const disabled = true;

  // Create schema with translations
  const formSchema = createFormSchema(t);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      provider: settings?.provider || "deepseek",
      apiKey: settings?.apiKey || "",
      model: settings?.model || "",
      azureEndpoint: settings?.azureEndpoint || "",
      azureDeploymentName: settings?.azureDeploymentName || "",
    },
  });

  const provider = form.watch("provider");

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      // TODO: Implement save functionality
    } catch (error) {
      console.error("Error saving LLM settings:", error);
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("llmSettings.providerConfiguration")}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="provider"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("llmSettings.provider")}</FormLabel>
                  <Select
                    disabled={disabled}
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t("llmSettings.selectProvider")}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI</SelectItem>
                      <SelectItem value="azure">Azure OpenAI</SelectItem>
                      <SelectItem value="deepseek">DeepSeek</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {t("llmSettings.chooseProvider")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="apiKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("llmSettings.apiKey")}</FormLabel>
                  <FormControl>
                    <Input
                      disabled={disabled}
                      type="password"
                      placeholder={t("llmSettings.enterApiKey")}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("llmSettings.apiKeyDescription", {
                      provider: provider === "azure" ? "Azure" : "OpenAI",
                    })}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("llmSettings.modelName")}</FormLabel>
                  <FormControl>
                    <Input
                      disabled={disabled}
                      placeholder={t("llmSettings.enterModelName", {
                        provider: provider,
                      })}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t("llmSettings.modelDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {provider === "azure" && (
              <>
                <FormField
                  control={form.control}
                  name="azureEndpoint"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("llmSettings.azureEndpoint")}</FormLabel>
                      <FormControl>
                        <Input
                          disabled={disabled}
                          placeholder={t("llmSettings.enterAzureEndpoint")}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("llmSettings.azureEndpointDescription")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="azureDeploymentName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("llmSettings.azureDeploymentName")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          disabled={disabled}
                          placeholder={t("llmSettings.enterDeploymentName")}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("llmSettings.deploymentDescription")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <Button disabled={disabled} type="submit">
              {t("llmSettings.saveSettings")}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
