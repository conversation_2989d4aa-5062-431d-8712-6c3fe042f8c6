"use client";

import { useEffect, useState } from "react";
import {
  audioNotificationManager,
  areNotificationSoundsEnabled,
  getNotificationVolume,
  hasUserInteractedForAudio,
  type NotificationSoundType,
} from "@/lib/audio/notification-sounds";

interface UseAudioNotificationsReturn {
  isEnabled: boolean;
  volume: number;
  hasUserInteracted: boolean;
  playSound: (type: NotificationSoundType) => Promise<void>;
  setEnabled: (enabled: boolean) => void;
  setVolume: (volume: number) => void;
  testSound: (type: NotificationSoundType) => Promise<void>;
}

/**
 * Hook for managing audio notifications
 * Provides easy access to audio notification controls and status
 */
export function useAudioNotifications(): UseAudioNotificationsReturn {
  const [isEnabled, setIsEnabledState] = useState(areNotificationSoundsEnabled());
  const [volume, setVolumeState] = useState(getNotificationVolume());
  const [hasUserInteracted, setHasUserInteracted] = useState(hasUserInteractedForAudio());

  // Update user interaction status periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const interacted = hasUserInteractedForAudio();
      if (interacted !== hasUserInteracted) {
        setHasUserInteracted(interacted);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [hasUserInteracted]);

  // Listen for storage changes to sync settings across tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "notification-audio-preferences") {
        setIsEnabledState(areNotificationSoundsEnabled());
        setVolumeState(getNotificationVolume());
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  const playSound = async (type: NotificationSoundType): Promise<void> => {
    try {
      await audioNotificationManager.playNotificationSound(type);
    } catch (error) {
      console.warn(`Failed to play ${type} notification sound:`, error);
    }
  };

  const setEnabled = (enabled: boolean) => {
    audioNotificationManager.setEnabled(enabled);
    setIsEnabledState(enabled);
  };

  const setVolume = (newVolume: number) => {
    audioNotificationManager.setVolume(newVolume);
    setVolumeState(newVolume);
  };

  const testSound = async (type: NotificationSoundType): Promise<void> => {
    try {
      await audioNotificationManager.testSound(type);
    } catch (error) {
      console.warn(`Failed to test ${type} sound:`, error);
      throw error;
    }
  };

  return {
    isEnabled,
    volume,
    hasUserInteracted,
    playSound,
    setEnabled,
    setVolume,
    testSound,
  };
}
