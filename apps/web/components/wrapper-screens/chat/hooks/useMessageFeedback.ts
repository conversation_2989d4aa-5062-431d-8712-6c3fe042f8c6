import { useRef } from 'react';
import { toast } from 'react-hot-toast';
import { Message } from '../types';
import { useLanguage } from "@/lib/language-context";
import { updateMessage } from "@/services/src/message";

export const useMessageFeedback = (
  messages: Message[],
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  chatId: string | null,
  displayIndicesRef: React.MutableRefObject<Record<string, number>>
) => {
  const { t } = useLanguage();

  // Function to handle feedback (like/dislike)
  const handleFeedback = async (
    messageIndex: number,
    feedback: "like" | "dislike"
  ) => {
    const message = messages[messageIndex];
    if (!chatId || message.role !== "assistant") return;

    try {
      // Determine which message to update (original or regenerated)
      let targetMessage = message;

      // Get the current display index for this message from our ref for more stable rendering
      const displayIndex = message.id
        ? displayIndicesRef.current[message.id] || 0
        : 0;

      // If display index is > 0, we're showing a regenerated message
      if (
        displayIndex > 0 &&
        message.regeneratedMessages &&
        message.regeneratedMessages.length > 0 &&
        message.regeneratedMessages[displayIndex - 1]
      ) {
        // Get the currently displayed regenerated message
        targetMessage = message.regeneratedMessages[displayIndex - 1];
      }

      // Update the message in the UI
      setMessages((prevMessages) => {
        return prevMessages.map((msg) => {
          // If this is the target message, update its feedback
          if (msg.id === targetMessage.id) {
            return {
              ...msg,
              metadata: {
                ...msg.metadata,
                feedback,
              },
            };
          }
          return msg;
        });
      });

      // Update the message in the database if it has an ID
      if (targetMessage.id) {
        await updateMessage({
          chatId,
          messageId: targetMessage.id,
          metadata: {
            ...targetMessage.metadata,
            feedback,
          },
        });
      }
    } catch (error) {
      console.error("Error updating feedback:", error);
      toast.error(t("chat.feedbackError"));
    }
  };

  return {
    handleFeedback,
  };
};
