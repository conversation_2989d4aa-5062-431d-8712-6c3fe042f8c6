import axios from "axios";

export const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api`;

export const fetchJson = async (url) => {
  try {
    const response = await axios.get(url, {
      headers: {
        cache: "no-store",
      },
    });
    if (!response.data) {
      throw new Error("Network error: Unable to fetch data.");
    }

    return response.data;
  } catch (error) {
    console.log(`error fetching data in ${url}`, { error });
  }
};
