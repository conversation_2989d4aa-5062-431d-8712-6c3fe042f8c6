"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { QuickAskProvider } from "./quick-ask-provider";

export function QuickAskWrapper({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const [userId, setUserId] = useState<string>("");
  const [tenantId, setTenantId] = useState<string>("");
  const [userName, setUserName] = useState<string>("");

  useEffect(() => {
    if (session) {
      // Get user ID from session
      const userIdValue = (session as any)?.userId || "";
      setUserId(userIdValue);

      // Get tenant ID from session
      const tenantIdValue =
        (session as any)?.memberships?.[0]?.tenant?.id || "";
      setTenantId(tenantIdValue);

      // Get user name from session
      const userNameValue = (session as any)?.user?.name || "";
      setUserName(userNameValue);
    }
  }, [session]);

  // Only render the provider if we have the necessary information
  if (!userId || !tenantId) {
    return <>{children}</>;
  }

  return (
    <QuickAskProvider userId={userId} tenantId={tenantId} userName={userName}>
      {children}
    </QuickAskProvider>
  );
}
