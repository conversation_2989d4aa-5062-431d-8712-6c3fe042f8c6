"use client";

import { useState } from "react";
import { useLanguage } from "@/lib/language-context";
import { Button } from "@/components/ui/button";
import { Globe } from "lucide-react";
import { UrlImportDialog } from "./url-import-dialog";
import { CreateGuard } from "@/components/permission-guard";

interface UrlImportButtonProps {
  workspaceSlug: string;
  workspaceId: string;
  pageId: string;
  folderId?: string;
  onSuccess?: () => void;
  size?: "default" | "sm" | "lg" | "icon";
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  className?: string;
  disabled?: boolean;
}

export function UrlImportButton({
  workspaceSlug,
  workspaceId,
  pageId,
  folderId,
  onSuccess,
  size = "sm",
  variant = "outline",
  className = "gap-2",
  disabled = false,
}: UrlImportButtonProps) {
  const { t } = useLanguage();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <CreateGuard
        resource="FILE"
        fallback={
          <Button
            size={size}
            variant="outline"
            className={className}
            disabled={true}
            title={t("common.noPermission")}
          >
            <Globe className="h-4 w-4" />
            {t("workspace.importFromUrl")}
          </Button>
        }
      >
        <Button
          variant={variant}
          size={size}
          className={className}
          onClick={() => setIsDialogOpen(true)}
          disabled={disabled}
        >
          <Globe className="h-4 w-4" />
          {t("workspace.importFromUrl")}
        </Button>
      </CreateGuard>

      <UrlImportDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        workspaceSlug={workspaceSlug}
        workspaceId={workspaceId}
        pageId={pageId}
        folderId={folderId}
        onSuccess={onSuccess}
      />
    </>
  );
}
