"use client";

import { useState } from "react";
import { Users, UserPlus } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { InviteUserForm } from "@/components/model/invite-user-model";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { MemberList } from "@/components/wrapper-screens/organization-settings/member-list";
import { InvitationsList } from "@/components/wrapper-screens/organization-settings/invitations-list";
import { useRouter } from "next/navigation";

export default function MembersSettingsPage({
  pendingMembers,
  organization,
  members,
  userRole,
  currentUserId,
}) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("members");
  const { t } = useLanguage();

  const isAdmin = userRole === "ADMIN" || userRole === "OWNER";

  return (
    <div className="space-y-6">
      <Tabs
        defaultValue="members"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="grid w-full md:w-auto md:inline-flex grid-cols-3">
          <TabsTrigger value="members">
            <Users className="h-4 w-4 mr-2" />
            {t("organization.members")}
          </TabsTrigger>
          <TabsTrigger value="invitations">
            <UserPlus className="h-4 w-4 mr-2" />
            {t("organization.invitations")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="members" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>{t("organization.teamMembers")}</CardTitle>
              </div>
              {isAdmin && (
                <InviteUserForm
                  userRole={userRole}
                  tenantId={organization.id}
                />
              )}
            </CardHeader>
            <CardContent>
              <MemberList
                members={members ?? []}
                currentUserId={currentUserId}
                tenantId={organization.id}
                isAdmin={isAdmin}
                updateMembers={() => router.refresh()}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invitations" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>{t("organization.pendingInvitations")}</CardTitle>
                <CardDescription>
                  {t("organization.managePendingInvitations")}
                </CardDescription>
              </div>
              {isAdmin && (
                <InviteUserForm
                  userRole={userRole}
                  tenantId={organization.id}
                />
              )}
            </CardHeader>
            <CardContent>
              <InvitationsList
                members={pendingMembers}
                tenantId={organization.id}
                userRole={userRole}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
