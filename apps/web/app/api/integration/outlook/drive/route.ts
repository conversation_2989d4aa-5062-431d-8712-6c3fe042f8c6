import { NextResponse } from "next/server";
import { Client } from "@microsoft/microsoft-graph-client";
import db from "@/lib/shared-db";
import axios from "axios";

// Helper function to get authenticated Graph client
const getAuthenticatedClient = async (tenantId: string, userId: string) => {
  const integration = await db.integration.findFirst({
    where: { tenantId, platform: "OUTLOOK", userId },
  });

  if (!integration) {
    throw new Error("Outlook integration not found");
  }
  const response = await axios.get(
    `${process.env.NEXTAUTH_URL}/api/integration/outlook/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`
  );

  const client = Client.init({
    authProvider: (done) =>
      done(null, response?.data?.integration?.accessToken),
  });

  return client;
};

// Create a folder in OneDrive or SharePoint site
export async function POST(request: Request) {
  try {
    const { name, parentFolderId, tenantId, siteId, driveId, userId } =
      await request.json();
    if (!name || !tenantId) {
      return NextResponse.json(
        { error: "Folder name and tenant ID are required" },
        { status: 400 }
      );
    }

    const client = await getAuthenticatedClient(tenantId, userId);
    const driveItem = {
      name,
      folder: {},
      "@microsoft.graph.conflictBehavior": "rename",
    };

    // If siteId is provided, create folder in SharePoint site
    if (siteId) {
      // If driveId is not provided, get the default document library
      const targetDriveId =
        driveId || (await client.api(`/sites/${siteId}/drive`).get()).id;

      const apiPath = parentFolderId
        ? `/sites/${siteId}/drives/${targetDriveId}/items/${parentFolderId}/children`
        : `/sites/${siteId}/drives/${targetDriveId}/root/children`;

      const folder = await client.api(apiPath).post(driveItem);
      return NextResponse.json({ folder }, { status: 201 });
    } else {
      // Create folder in personal OneDrive (legacy support)
      const folder = await client
        .api(
          `/me/drive/${
            parentFolderId
              ? `items/${parentFolderId}/children`
              : "root/children"
          }`
        )
        .post(driveItem);

      return NextResponse.json({ folder }, { status: 201 });
    }
  } catch (error) {
    console.error("Error creating folder:", error);
    return NextResponse.json(
      { error: "Failed to create folder" },
      { status: 500 }
    );
  }
}

// List all folders in OneDrive
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const parentFolderId = url.searchParams.get("parentFolderId");
    const skipToken = url.searchParams.get("skipToken");
    const siteId = url.searchParams.get("siteId");
    const driveId = url.searchParams.get("driveId");
    const userId = url.searchParams.get("userId") ?? "";

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const client = await getAuthenticatedClient(tenantId, userId);

    // If siteId is provided, use SharePoint site API
    if (siteId) {
      // If driveId is not provided, get the default document library
      const targetDriveId =
        driveId || (await client.api(`/sites/${siteId}/drive`).get()).id;

      let apiPath = parentFolderId
        ? `/sites/${siteId}/drives/${targetDriveId}/items/${parentFolderId}/children`
        : `/sites/${siteId}/drives/${targetDriveId}/root/children`;

      if (skipToken) {
        apiPath += `?$skiptoken=${skipToken}`;
      }

      const response = await client
        .api(apiPath)
        .select("id,name,folder,parentReference,size,lastModifiedDateTime")
        .filter("folder ne null")
        .get();

      return NextResponse.json(response, { status: 200 });
    } else {
      // Use personal OneDrive API (legacy support)
      let apiPath = parentFolderId
        ? `/me/drive/items/${parentFolderId}/children`
        : "/me/drive/root/children";

      if (skipToken) {
        apiPath += `?$skiptoken=${skipToken}`;
      }

      const response = await client
        .api(apiPath)
        .select("id,name,folder,parentReference,size,lastModifiedDateTime")
        .filter("folder ne null")
        .get();

      return NextResponse.json(response, { status: 200 });
    }
  } catch (error) {
    console.error("Error listing folders:", error);
    return NextResponse.json(
      { error: "Failed to list folders" },
      { status: 500 }
    );
  }
}

// Upload large file to OneDrive or SharePoint site
export async function PUT(request: Request) {
  try {
    const { name, slug, size, tenantId, siteId, driveId, folderId, userId } =
      await request.json();
    if (!name || !size || !tenantId) {
      return NextResponse.json(
        { error: "File name, size, and tenant ID are required" },
        { status: 400 }
      );
    }

    const client = await getAuthenticatedClient(tenantId, userId);

    // If siteId is provided, upload to SharePoint site
    if (siteId) {
      // If driveId is not provided, get the default document library
      const targetDriveId =
        driveId || (await client.api(`/sites/${siteId}/drive`).get()).id;

      // If folderId is not provided, use the root folder
      const apiPath = folderId
        ? `/sites/${siteId}/drives/${targetDriveId}/items/${folderId}:/${name}:/createUploadSession`
        : `/sites/${siteId}/drives/${targetDriveId}/root:/${name}:/createUploadSession`;

      // Create upload session
      const uploadSession = await client.api(apiPath).post({
        item: {
          "@microsoft.graph.conflictBehavior": "rename",
        },
      });

      return NextResponse.json(
        { uploadUrl: uploadSession.uploadUrl },
        { status: 200 }
      );
    } else {
      // Upload to personal OneDrive (legacy support)
      const workspace = await db.workspace.findFirst({
        where: { tenantId, slug },
      });

      if (!workspace?.oneDriveFolderId) {
        return NextResponse.json({ message: "integration not found" });
      }

      // Create upload session
      const uploadSession = await client
        .api(
          `/me/drive/items/${workspace?.oneDriveFolderId}:/${name}:/createUploadSession`
        )
        .post({
          item: {
            "@microsoft.graph.conflictBehavior": "rename",
          },
        });

      return NextResponse.json(
        { uploadUrl: uploadSession.uploadUrl },
        { status: 200 }
      );
    }
  } catch (error) {
    console.error("Error initiating file upload:", error);
    return NextResponse.json(
      { error: "Failed to initiate file upload" },
      { status: 500 }
    );
  }
}

// Delete a file or folder in OneDrive or SharePoint site
export async function DELETE(request: Request) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const itemId = url.searchParams.get("itemId");
    const siteId = url.searchParams.get("siteId");
    const driveId = url.searchParams.get("driveId");
    const userId = url.searchParams.get("userId") ?? "";

    if (!tenantId || !itemId) {
      return NextResponse.json(
        { error: "Tenant ID and item ID are required" },
        { status: 400 }
      );
    }

    const client = await getAuthenticatedClient(tenantId, userId);

    // If siteId is provided, delete from SharePoint site
    if (siteId && driveId) {
      await client
        .api(`/sites/${siteId}/drives/${driveId}/items/${itemId}`)
        .delete();
    } else {
      // Delete from personal OneDrive (legacy support)
      await client.api(`/me/drive/items/${itemId}`).delete();
    }

    return NextResponse.json(
      { message: "Item deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting item:", error);
    return NextResponse.json(
      { error: "Failed to delete item" },
      { status: 500 }
    );
  }
}
