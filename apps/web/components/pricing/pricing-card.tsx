"use client";

import { Check } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Plan } from "@/services/src/plans";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/lib/language-context";
import { PlanPrice } from "@/services/src/stripe-prices";

import { StripeCheckoutButton } from "./stripe-checkout-button";

interface PricingCardProps {
  plan: Plan;
  isCurrentPlan?: boolean;
  className?: string;
  billingInterval?: "month" | "year";
  stripePriceData?: PlanPrice;
}

export function PricingCard({
  plan,
  isCurrentPlan = false,
  className,
  billingInterval = "month",
  stripePriceData,
}: PricingCardProps) {
  const { t } = useLanguage();

  return (
    <Card
      className={cn(
        "flex flex-col",
        isCurrentPlan && "border-primary shadow-md",
        className
      )}
    >
      <CardHeader>
        <CardTitle className="text-xl">{t(`billing.${plan.name}`)}</CardTitle>
        <CardDescription>
          {t(`billing.description${plan.name}`)}
        </CardDescription>
      </CardHeader>
      <CardContent className="grid flex-1 gap-4">
        <div className="flex flex-col gap-1">
          {/* Price display */}
          <div className="flex items-baseline gap-1">
            <span className="text-3xl font-bold">
              {plan.type === "CUSTOM"
                ? t("billing.contactUs")
                : stripePriceData
                  ? // Use Stripe price data if available
                    t("billing.priceCHF", {
                      price: `${
                        ((billingInterval === "year"
                          ? stripePriceData.yearlyPrice?.unitAmount
                          : stripePriceData.monthlyPrice?.unitAmount) || 0) /
                        100
                      }`,
                    })
                  : // Fallback to plan price from database
                    t("billing.priceCHF", {
                      price:
                        billingInterval === "year"
                          ? `${Math.round((plan.price || 0) * 10)}`
                          : `${plan.price || 0}`,
                    })}
            </span>
            {plan.type !== "CUSTOM" && (
              <span className="text-sm text-muted-foreground">
                {billingInterval === "year"
                  ? t("billing.perYear")
                  : t("billing.perMonth")}
              </span>
            )}
          </div>

          {/* Show original price with strikethrough for yearly billing */}
          {billingInterval === "year" && plan.type !== "CUSTOM" && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground line-through">
                {stripePriceData
                  ? t("billing.priceCHF", {
                      price: `${
                        ((stripePriceData.monthlyPrice?.unitAmount || 0) * 12) /
                        100
                      }`,
                    })
                  : t("billing.priceCHF", {
                      price: `${(plan.price || 0) * 12}`,
                    })}
              </span>
              <span className="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-100">
                {stripePriceData
                  ? t("billing.saveCHF", {
                      amount: `${Math.round(
                        ((stripePriceData.monthlyPrice?.unitAmount || 0) * 12 -
                          (stripePriceData.yearlyPrice?.unitAmount || 0)) /
                          100
                      )}`,
                    })
                  : t("billing.saveCHF", {
                      amount: `${Math.round(
                        (plan.price || 0) * 12 - (plan.price || 0) * 10
                      )}`,
                    })}
              </span>
            </div>
          )}
        </div>
        {plan.type !== "CUSTOM" && (
          <div className="space-y-1">
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">
                {stripePriceData
                  ? // Use Stripe price data if available
                    t("billing.additionalUserFee", {
                      fee: `${
                        ((billingInterval === "year"
                          ? stripePriceData.userYearlyPrice?.unitAmount
                          : stripePriceData.userMonthlyPrice?.unitAmount) ||
                          0) / 100
                      }`,
                    })
                  : // Fallback to plan price from database
                    t("billing.additionalUserFee", {
                      fee:
                        billingInterval === "year"
                          ? Math.round((plan.additionalUserFee || 0) * 10)
                          : plan.additionalUserFee || 0,
                    })}
              </div>

              {/* Show original user fee with strikethrough for yearly billing */}
              {billingInterval === "year" && (
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground line-through">
                    {stripePriceData
                      ? t("billing.additionalUserFee", {
                          fee: `${
                            ((stripePriceData.userMonthlyPrice?.unitAmount ||
                              0) *
                              12) /
                            100
                          }`,
                        })
                      : t("billing.additionalUserFee", {
                          fee: (plan.additionalUserFee || 0) * 12,
                        })}
                  </span>
                </div>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              {t("billing.additionalStorageFee")}
            </div>
          </div>
        )}
        <div>
          <p className="font-medium">{t("billing.includes")}:</p>
          <ul className="mt-2 space-y-2">
            <li className="flex items-center gap-2">
              <Check className="h-4 w-4 text-primary" />
              <span>
                {plan.type !== "CUSTOM"
                  ? plan.includedUsers
                  : t("billing.customNumberOf")}{" "}
                {t("billing.usersLabel")}
              </span>
            </li>
            <li className="flex items-center gap-2">
              <Check className="h-4 w-4 text-primary" />
              <span>
                {plan.type !== "CUSTOM"
                  ? plan.vectorStoreGB
                  : t("billing.custom")}{" "}
                {t("billing.gbVectorStore")}
              </span>
            </li>
            <li className="flex items-center gap-2">
              <Check className="h-4 w-4 text-primary" />
              <span>{t("billing.allFeatures")}</span>
            </li>
            <li className="flex items-center gap-2">
              <Check className="h-4 w-4 text-primary" />
              <span>{t("billing.hostingInSwitzerland")}</span>
            </li>
            <li className="flex items-center gap-2">
              <Check className="h-4 w-4 text-primary" />
              <span>{t("billing.standardSupport")}</span>
            </li>
          </ul>
        </div>
      </CardContent>
      <CardFooter>
        {isCurrentPlan ? (
          <Button className="w-full" variant="outline" disabled>
            {t("billing.currentPlan")}
          </Button>
        ) : (
          <StripeCheckoutButton
            plan={plan}
            variant="default"
            disabled={false}
            billingInterval={billingInterval}
            stripePriceData={stripePriceData}
          />
        )}
      </CardFooter>
    </Card>
  );
}
