"use client";

import React, { useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ImageIcon, X, Upload, Loader2, FileImage } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { toast } from "react-hot-toast";
import { ImageAttachment } from "../types";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface ImageUploadProps {
  onImagesSelected: (images: ImageAttachment[]) => void;
  selectedImages: ImageAttachment[];
  onRemoveImage: (imageId: string) => void;
  disabled?: boolean;
  maxImages?: number;
}

function ImageUpload({
  onImagesSelected,
  selectedImages,
  onRemoveImage,
  disabled = false,
  maxImages = 5,
}: ImageUploadProps) {
  const { t } = useLanguage();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(
    new Set()
  );

  const supportedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  const validateFile = (file: File): boolean => {
    if (!supportedTypes.includes(file.type)) {
      toast.error(
        t("chat.invalidImageFormat") ||
          "Invalid image format. Please use JPG, PNG, or WebP."
      );
      return false;
    }
    if (file.size > maxFileSize) {
      toast.error(
        t("chat.imageTooLarge") || "Image size should be less than 10MB."
      );
      return false;
    }
    return true;
  };

  const processFiles = async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(validateFile);

    if (selectedImages.length + validFiles.length > maxImages) {
      toast.error(
        t("chat.tooManyImages") || `Maximum ${maxImages} images allowed.`
      );
      return;
    }

    const newImages: ImageAttachment[] = [];

    for (const file of validFiles) {
      try {
        // Create preview URL for immediate display
        const preview = URL.createObjectURL(file);

        // Show loading toast
        const loadingToast = toast.loading(`Uploading ${file.name}...`);

        // Upload to server
        const uploadedImage = await uploadImageToServer(file);

        // Dismiss loading toast
        toast.dismiss(loadingToast);

        if (uploadedImage) {
          const imageAttachment: ImageAttachment = {
            id: uploadedImage.id,
            url: uploadedImage.url,
            name: uploadedImage.name,
            type: uploadedImage.type,
            size: uploadedImage.size,
            preview, // Keep local preview for immediate display
          };

          newImages.push(imageAttachment);
          toast.success(`${file.name} uploaded successfully`);
        } else {
          // Clean up preview URL if upload failed
          URL.revokeObjectURL(preview);
          toast.error(`Failed to upload ${file.name}`);
        }
      } catch (error) {
        console.error("Error processing image:", error);
        toast.error(
          t("chat.imageProcessingError") || "Error processing image."
        );
      }
    }

    if (newImages.length > 0) {
      onImagesSelected([...selectedImages, ...newImages]);
    }
  };

  const uploadImageToServer = async (
    file: File
  ): Promise<ImageAttachment | null> => {
    try {
      // Get tenant ID from cookies or context
      const tenantId = document.cookie
        .split("; ")
        .find((row) => row.startsWith("currentOrganizationId="))
        ?.split("=")[1];

      if (!tenantId) {
        throw new Error("Tenant ID not found");
      }

      const formData = new FormData();
      formData.append("file", file);
      formData.append("tenantId", tenantId);

      const response = await fetch("/api/chat/upload-image", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Error uploading image:", error);
      return null;
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveImage = (imageId: string) => {
    // Clean up object URL to prevent memory leaks
    const image = selectedImages.find((img) => img.id === imageId);
    if (image?.preview) {
      URL.revokeObjectURL(image.preview);
    }
    onRemoveImage(imageId);
  };

  return (
    <div className="">
      {/* Upload Button and Drag Area */}
      <div
        className={cn(
          "relative transition-all duration-200 ease-in-out",
          isDragOver && "scale-105"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={supportedTypes.join(",")}
          multiple
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />

        <Button
          type="button"
          variant="ghost"
          size="icon"
          className={cn(
            "h-9 w-9 rounded-xl hover:bg-accent/80 transition-all duration-200",
            "hover:scale-110 active:scale-95",
            "border border-transparent hover:border-border/50",
            disabled && "opacity-50 cursor-not-allowed",
            selectedImages.length >= maxImages &&
              "opacity-50 cursor-not-allowed"
          )}
          onClick={handleButtonClick}
          disabled={disabled || selectedImages.length >= maxImages}
          title={t("chat.uploadImage") || "Upload image"}
        >
          <ImageIcon className="h-4 w-4 text-muted-foreground" />
        </Button>

        {/* Enhanced Drag overlay */}
        {isDragOver && (
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-primary/5 border-2 border-dashed border-primary/60 rounded-xl flex items-center justify-center z-10 backdrop-blur-sm">
            <div className="text-center animate-pulse">
              <div className="bg-primary/10 rounded-full p-3 mx-auto mb-2 w-fit">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              <p className="text-sm text-primary font-medium">
                {t("chat.dropImagesHere") || "Drop images here"}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Image Previews */}
      {selectedImages.length > 0 && (
        <div className="flex flex-wrap gap-3 max-w-full">
          {selectedImages.map((image) => {
            const isUploading = uploadingImages.has(image.id);

            return (
              <div
                key={image.id}
                className={cn(
                  "relative group bg-gradient-to-br from-muted/50 to-muted rounded-xl overflow-hidden border border-border/50",
                  "hover:border-border transition-all duration-200 hover:shadow-md",
                  "w-20 h-20 sm:w-24 sm:h-24",
                  isUploading && "animate-pulse"
                )}
              >
                <Image
                  src={image.url || image.preview || ""}
                  alt={image.name}
                  width={80}
                  height={80}
                  className={cn(
                    "w-full h-full object-cover transition-all duration-200",
                    "group-hover:scale-105",
                    isUploading && "opacity-70"
                  )}
                />

                {/* Loading overlay */}
                {isUploading && (
                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                    <div className="bg-white/90 rounded-full p-2">
                      <Loader2 className="h-4 w-4 animate-spin text-primary" />
                    </div>
                  </div>
                )}

                {/* Enhanced Remove button */}
                <button
                  onClick={() => handleRemoveImage(image.id)}
                  disabled={isUploading}
                  className={cn(
                    "absolute -top-2 -right-2 bg-destructive text-destructive-foreground",
                    "rounded-full p-1.5 opacity-0 group-hover:opacity-100",
                    "transition-all duration-200 hover:scale-110 active:scale-95",
                    "shadow-lg border-2 border-background",
                    isUploading && "opacity-0 cursor-not-allowed"
                  )}
                  title={t("chat.removeImage") || "Remove image"}
                >
                  <X className="h-3 w-3" />
                </button>

                {/* Enhanced File info overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="flex items-center gap-1">
                    <FileImage className="h-3 w-3 text-white/80" />
                    <p className="text-xs text-white/90 truncate font-medium">
                      {image.name}
                    </p>
                  </div>
                  <p className="text-xs text-white/70">
                    {(image.size / 1024 / 1024).toFixed(1)}MB
                  </p>
                </div>

                {/* Upload success indicator */}
                {!isUploading && !image.id.startsWith("temp_") && (
                  <div className="absolute top-1 left-1 bg-green-500 text-white rounded-full p-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* Enhanced Help text with progress indicator */}
      <div className="space-y-2">
        {/* {selectedImages.length === 0 && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <ImageIcon className="h-3 w-3" />
            <span>
              {t("chat.imageUploadHelp") ||
                "Drag & drop images or click to upload (JPG, PNG, WebP, max 10MB)"}
            </span>
          </div>
        )} */}

        {selectedImages.length > 0 && (
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>
              {selectedImages.length} of {maxImages} images
            </span>
            {uploadingImages.size > 0 && (
              <div className="flex items-center gap-1">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>Uploading {uploadingImages.size}...</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export { ImageUpload };
