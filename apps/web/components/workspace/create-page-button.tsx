"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { CreateGuard } from "@/components/permission-guard";

interface CreatePageButtonProps {
  size?: "default" | "sm" | "lg" | "icon";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
  onClick: () => void;
}

export default function CreatePageButton({
  size = "sm",
  variant = "default",
  className = "gap-2",
  onClick,
}: CreatePageButtonProps) {
  const { t } = useLanguage();

  return (
    <CreateGuard 
      resource="PAGE"
      fallback={
        <Button 
          size={size} 
          variant="outline" 
          className={className}
          disabled
          title={t("common.noPermission")}
        >
          <Plus className="h-4 w-4" />
          {t("workspace.newPage")}
        </Button>
      }
    >
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={onClick}
      >
        <Plus className="h-4 w-4" />
        {t("workspace.newPage")}
      </Button>
    </CreateGuard>
  );
}
