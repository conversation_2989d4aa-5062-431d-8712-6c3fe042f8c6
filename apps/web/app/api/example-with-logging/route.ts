import { withLogging } from '@/lib/api/middleware';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Example API route with Datadog logging
 */
export const GET = withLogging(async (req: NextRequest) => {
  // Your API logic here
  return NextResponse.json({ message: 'This API call is being logged to Datadog' });
});

export const POST = withLogging(async (req: NextRequest) => {
  try {
    // Parse the request body
    const body = await req.json();
    
    // Process the request
    const result = { 
      message: 'Data received and logged to Datadog',
      receivedData: body
    };
    
    // Return the response
    return NextResponse.json(result);
  } catch (error) {
    // Error handling - this will also be logged to Datadog
    console.error('Error processing request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
});
