/**
 * Mock email service for development environments
 * This allows the application to function without a real email service
 */
export const mockEmailService = {
  send: async (emailData: any) => {
    // Log email details for development testing
    console.log('\n--- MOCK EMAIL SERVICE ---');
    console.log('To:', emailData.to);
    console.log('Subject:', emailData.subject);
    console.log('From:', emailData.from);
    
    // Log a simplified version of the HTML content
    const htmlPreview = emailData.html
      .replace(/<[^>]*>/g, ' ') // Remove HTML tags
      .replace(/\s+/g, ' ')     // Remove extra whitespace
      .trim()
      .substring(0, 100) + '...'; // First 100 chars
    
    console.log('Content preview:', htmlPreview);
    console.log('--- END OF EMAIL ---\n');
    
    // Extract links from HTML for testing
    const links = extractLinksFromHtml(emailData.html);
    if (links.length > 0) {
      console.log('Links found in email:');
      links.forEach(link => console.log(`- ${link}`));
    }
    
    // Simulate delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return success response
    return {
      success: true,
      messageId: `mock-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
    };
  }
};

/**
 * Helper function to extract links from HTML content
 */
function extractLinksFromHtml(html: string): string[] {
  const links: string[] = [];
  const regex = /href=["'](https?:\/\/[^"']+)["']/g;
  let match;
  
  while ((match = regex.exec(html)) !== null) {
    links.push(match[1]);
  }
  
  return links;
}

export default mockEmailService;