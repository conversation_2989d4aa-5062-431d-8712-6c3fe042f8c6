import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { MemberList } from "@/components/wrapper-screens/settings/member-list";
import { authOptions } from "@/lib/next-auth";
import { getMembers, getTenantSubscription } from "@/services";
import { getTranslations } from "@/lib/server-i18n";
import DashboardPage from "@/components/wrapper-screens/dashboard/screent";
import { cookies } from "next/headers";

export default async function Page() {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;

  if (!session) {
    return redirect("/sign-in");
  }

  if (!session.memberships || session.memberships.length === 0) {
    return redirect("/onboarding");
  }
  const subscription = await getTenantSubscription(
    tenantId,
    session.userId as string
  );

  return <DashboardPage session={session} subscription={subscription} />;
}
