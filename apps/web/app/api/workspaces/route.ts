import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { checkActiveSubscription } from "@/lib/subscription-check";
import {
  withPermission,
  withActiveSubscription,
} from "@/lib/permission-middleware";

// Get workspaces for the current user
const getWorkspaces = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userId = request.headers.get("x-user-id");
    const tenantId = request.headers.get("x-tenant-id");

    const url = request.url;
    const urlParams = new URLSearchParams(url.split("?")[1]);

    const rawSlug = urlParams.get("slug") ?? "";
    const slug = decodeURI(rawSlug);

    // Check if user is an owner or admin
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    const isOwnerOrAdmin =
      membership?.role === "OWNER" || membership?.role === "ADMIN";

    if (slug) {
      // For owners and admins, show any workspace in the tenant
      // For regular users, only show workspaces they are members of
      const workspaceQuery = isOwnerOrAdmin
        ? {
            slug,
            tenantId,
          }
        : {
            slug,
            tenantId,
            workspaceMember: {
              some: {
                userId,
              },
            },
          };

      const workspace = await db.workspace.findFirst({
        where: workspaceQuery,
        include: {
          pages: true,
        },
      });
      return NextResponse.json(
        {
          workspace,
        },
        { status: 200 }
      );
    }

    // For owners and admins, show all workspaces in the tenant
    // For regular users, only show workspaces they are members of
    const workspacesQuery = isOwnerOrAdmin
      ? {
          tenantId,
        }
      : {
          tenantId,
          workspaceMember: {
            some: {
              userId,
            },
          },
        };

    const workspaces = await db.workspace.findMany({
      where: workspacesQuery,
      include: {
        pages: true,
      },
    });

    return NextResponse.json(
      {
        workspaces,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching workspaces:", error);
    return NextResponse.json(
      { error: "Failed to fetch workspaces" },
      { status: 500 }
    );
  }
};

// Create a new workspace
const createWorkspace = async (request: Request) => {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { title, slug, description, initials, tenantId } =
      await request.json();

    // Check if tenant has an active subscription
    const { hasActiveSubscription } = await checkActiveSubscription(tenantId);

    if (!hasActiveSubscription) {
      return NextResponse.json(
        { error: "You need an active subscription to create workspaces" },
        { status: 403 }
      );
    }

    const users = await db.user.findMany({
      where: {
        OR: [
          {
            membership: {
              some: {
                tenantId,
                role: {
                  in: ["ADMIN", "OWNER"],
                },
              },
            },
          },
          {
            id: session.userId, // replace with actual ID variable
          },
        ],
      },
      include: {
        membership: true,
      },
    });

    // Create workspace and membership in a transaction
    const result = await db.$transaction(async (tx: any) => {
      // Create the workspace
      const newWorkspace = await tx.workspace.create({
        data: {
          name: title,
          slug: `${slug}-${Date.now()}`,
          description,
          tenantId,
          initials,
        },
      });
      const workSpaceMember = users?.map((item: any) => ({
        userId: item.id,
        workspaceId: newWorkspace.id,
        membershipId: item?.membership?.[0]?.id,
        role: item?.membership?.[0]?.role,
      }));
      // Create membership with OWNER role
      const membership = await tx.WorkspaceMember.createMany({
        data: workSpaceMember,
      }); // Create membership with OWNER role
      await tx.page.create({
        data: {
          name: "General",
          workspaceId: newWorkspace.id,
        },
      });

      return {
        ...newWorkspace,
        role: membership.role,
      };
    });

    return NextResponse.json(
      {
        message: "Workspace created successfully",
        data: result,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating Workspace:", error);
    return NextResponse.json(
      { error: "Failed to create Workspace" },
      { status: 500 }
    );
  }
};

// Delete a workspace
const deleteWorkspace = async (request: Request) => {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const url = request.url;
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const workspaceId = urlParams.get("workspaceId") ?? "";
    await db.workspace.delete({
      where: {
        id: workspaceId,
      },
    });
    return NextResponse.json(
      {
        message: "Workspace deleted successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting workspace:", error);
    return NextResponse.json(
      { error: "Failed to delete workspace" },
      { status: 500 }
    );
  }
};

// Update a workspace
const updateWorkspace = async (request: Request) => {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const url = request.url;
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const id = urlParams.get("id") ?? "";

    const { data } = await request.json();
    const updatedWorkspace = await db.workspace.update({
      where: {
        id,
      },
      data,
    });
    return NextResponse.json(
      {
        workspace: updatedWorkspace,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating workspace:", error);
    return NextResponse.json(
      { error: "Failed to update workspace" },
      { status: 500 }
    );
  }
};

// Export the handlers with permission checks
export const GET = withPermission(getWorkspaces, "READ", "WORKSPACE");
export const POST = withPermission(
  withActiveSubscription(createWorkspace),
  "CREATE",
  "WORKSPACE"
);
export const DELETE = withPermission(deleteWorkspace, "DELETE", "WORKSPACE");
export const PUT = withPermission(updateWorkspace, "UPDATE", "WORKSPACE");
