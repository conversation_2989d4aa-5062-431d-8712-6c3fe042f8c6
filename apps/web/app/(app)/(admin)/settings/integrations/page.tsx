import { IntegrationScreen } from "@/components/wrapper-screens/settings/cloud-storage-settings";
import { getIntegration } from "@/services/src/integration";
import { cookies } from "next/headers";

export default async function Page() {
  const tenantId = (await cookies().get("currentOrganizationId")?.value) ?? "";
  const userId = (await cookies().get("userId")?.value) ?? "";

  try {
    // Handle case where folder data is not found
    const integration = await getIntegration({ tenantId, userId });
    return <IntegrationScreen integration={integration?.data} />;
  } catch (error) {
    console.error("Error loading folder:", error);
  }
}
