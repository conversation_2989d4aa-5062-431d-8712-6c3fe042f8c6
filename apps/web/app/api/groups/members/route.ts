import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";
import crypto from "crypto";

// Add a user to a group
async function addUserToGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { groupId, email, tenantId } = await request.json();

    if (!groupId || !email || !tenantId) {
      return NextResponse.json(
        { error: "Group ID, Email, and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
      include: {
        groupWorkspaces: true,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Find the user by email
    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    const user = await db.user.findUnique({
      where: {
        emailHash,
      },
      include: {
        membership: {
          where: {
            tenantId,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found. Add the user first" },
        { status: 404 }
      );
    }

    // Verify user is a member of the organization
    if (user.membership.length === 0) {
      return NextResponse.json(
        { error: "User must be a member of the organization first" },
        { status: 403 }
      );
    }

    // Check if user is already a member of the group
    const existingMember = await db.groupMember.findFirst({
      where: {
        userId: user.id,
        groupId,
      },
    });

    if (existingMember) {
      return NextResponse.json(
        { error: "User is already a member of this group" },
        { status: 400 }
      );
    }

    // Add the user to the group
    const groupMember = await db.groupMember.create({
      data: {
        userId: user.id,
        groupId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // For each workspace in the group, ensure the user has access
    for (const groupWorkspace of group.groupWorkspaces) {
      // Check if the user already has direct access to the workspace
      const existingWorkspaceMember = await db.workspaceMember.findFirst({
        where: {
          userId: user.id,
          workspaceId: groupWorkspace.workspaceId,
        },
      });

      // If the user doesn't have direct access, create a workspace member entry
      if (!existingWorkspaceMember) {
        await db.workspaceMember.create({
          data: {
            userId: user.id,
            workspaceId: groupWorkspace.workspaceId,
            membershipId: user.membership[0].id,
            role: "MEMBER", // Default role for group-based access
          },
        });
      }
    }

    return NextResponse.json({
      message: "User added to group successfully",
      data: groupMember,
    });
  } catch (error) {
    console.error("Error adding user to group:", error);
    return NextResponse.json(
      { error: "Failed to add user to group" },
      { status: 500 }
    );
  }
}

// Remove a user from a group
async function removeUserFromGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const groupId = url.searchParams.get("groupId");
    const userId = url.searchParams.get("userId");
    const tenantId = url.searchParams.get("tenantId");

    if (!groupId || !userId || !tenantId) {
      return NextResponse.json(
        { error: "Group ID, User ID, and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Find the group member
    const groupMember = await db.groupMember.findFirst({
      where: {
        groupId,
        userId,
      },
    });

    if (!groupMember) {
      return NextResponse.json(
        { error: "User is not a member of this group" },
        { status: 404 }
      );
    }

    // Remove the user from the group
    await db.groupMember.delete({
      where: {
        id: groupMember.id,
      },
    });

    return NextResponse.json({
      message: "User removed from group successfully",
    });
  } catch (error) {
    console.error("Error removing user from group:", error);
    return NextResponse.json(
      { error: "Failed to remove user from group" },
      { status: 500 }
    );
  }
}

// Get all members of a group
async function getGroupMembers(request: Request) {
  try {
    const url = new URL(request.url);
    const groupId = url.searchParams.get("groupId");
    const tenantId = url.searchParams.get("tenantId");

    if (!groupId || !tenantId) {
      return NextResponse.json(
        { error: "Group ID and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Get all members of the group
    const groupMembers = await db.groupMember.findMany({
      where: {
        groupId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({
      data: groupMembers,
      message: "Group members retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching group members:", error);
    return NextResponse.json(
      { error: "Failed to fetch group members" },
      { status: 500 }
    );
  }
}

// Export the handlers with permission checks
export const POST = withPermission(addUserToGroup, "UPDATE", "WORKSPACE");
export const DELETE = withPermission(removeUserFromGroup, "UPDATE", "WORKSPACE");
export const GET = withPermission(getGroupMembers, "READ", "WORKSPACE");
