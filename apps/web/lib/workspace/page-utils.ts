/**
 * Utility functions for managing workspace pages
 */

// Function to add a new page to a workspace
export function addPageToWorkspace(
  workspaceSlug: string,
  pageName: string,
  pageType: string = "page"
): string {
  if (typeof window === "undefined") {
    console.error("Cannot add page - window is undefined");
    return "";
  }

  try {
    // Generate a unique ID
    const pageId = `page-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    // Create the new page object
    const newPage = {
      id: pageId,
      name: pageName,
      type: pageType,
      createdAt: new Date().toISOString()
    };
    
    // Get existing pages for this workspace
    const pagesKey = `${workspaceSlug}-pages`;
    const existingPagesJSON = sessionStorage.getItem(pagesKey) || "[]";
    const existingPages = JSON.parse(existingPagesJSON);
    
    // Add the new page
    const updatedPages = [...existingPages, newPage];
    
    // Save back to sessionStorage
    sessionStorage.setItem(pagesKey, JSON.stringify(updatedPages));
    
    // Dispatch a custom event to notify listeners about the page update
    const updateEvent = new CustomEvent("workspace-pages-updated", {
      detail: {
        workspaceSlug,
        pages: updatedPages
      }
    });
    
    window.dispatchEvent(updateEvent);
    
    // Return the ID of the new page
    return pageId;
  } catch (error) {
    console.error("Error adding page to workspace:", error);
    return "";
  }
}

// Function to get all pages for a workspace
export function getWorkspacePages(workspaceSlug: string): any[] {
  if (typeof window === "undefined") {
    return [];
  }
  
  try {
    const pagesKey = `${workspaceSlug}-pages`;
    const pagesJSON = sessionStorage.getItem(pagesKey) || "[]";
    return JSON.parse(pagesJSON);
  } catch (error) {
    console.error("Error getting workspace pages:", error);
    return [];
  }
}

// Function to update a page in a workspace
export function updateWorkspacePage(
  workspaceSlug: string,
  pageId: string,
  updates: Partial<{
    name: string;
    type: string;
    content: string;
  }>
): boolean {
  if (typeof window === "undefined") {
    return false;
  }
  
  try {
    // Get existing pages
    const pagesKey = `${workspaceSlug}-pages`;
    const existingPagesJSON = sessionStorage.getItem(pagesKey) || "[]";
    const existingPages = JSON.parse(existingPagesJSON);
    
    // Find and update the page
    const updatedPages = existingPages.map((page: any) => {
      if (page.id === pageId) {
        return {
          ...page,
          ...updates,
          updatedAt: new Date().toISOString()
        };
      }
      return page;
    });
    
    // Save back to sessionStorage
    sessionStorage.setItem(pagesKey, JSON.stringify(updatedPages));
    
    // Dispatch event to notify listeners
    const updateEvent = new CustomEvent("workspace-pages-updated", {
      detail: {
        workspaceSlug,
        pages: updatedPages
      }
    });
    
    window.dispatchEvent(updateEvent);
    
    return true;
  } catch (error) {
    console.error("Error updating workspace page:", error);
    return false;
  }
}

// Function to delete a page from a workspace
export function deleteWorkspacePage(workspaceSlug: string, pageId: string): boolean {
  if (typeof window === "undefined") {
    return false;
  }
  
  try {
    // Get existing pages
    const pagesKey = `${workspaceSlug}-pages`;
    const existingPagesJSON = sessionStorage.getItem(pagesKey) || "[]";
    const existingPages = JSON.parse(existingPagesJSON);
    
    // Filter out the page to delete
    const updatedPages = existingPages.filter((page: any) => page.id !== pageId);
    
    // Save back to sessionStorage
    sessionStorage.setItem(pagesKey, JSON.stringify(updatedPages));
    
    // Dispatch event to notify listeners
    const updateEvent = new CustomEvent("workspace-pages-updated", {
      detail: {
        workspaceSlug,
        pages: updatedPages
      }
    });
    
    window.dispatchEvent(updateEvent);
    
    return true;
  } catch (error) {
    console.error("Error deleting workspace page:", error);
    return false;
  }
}