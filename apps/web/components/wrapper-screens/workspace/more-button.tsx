"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical, Pencil, Trash2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { UpdateGuard, DeleteGuard } from "@/components/permission-guard";

interface MoreButtonProps {
  handleDelete: (id: string, type: string) => void;
  file: any;
  setIsCreateFolderOpen: (value: string | null) => void;
  type: "folder" | "file";
  setUpdateData: (data: any) => void;
  setNewFolderName: (name: string) => void;
  isSyncedWithCloud?: boolean;
}

export const MoreButton = ({
  handleDelete,
  file,
  setIsCreateFolderOpen,
  type,
  setUpdateData,
  setNewFolderName,
  isSyncedWithCloud = false,
}: MoreButtonProps) => {
  const { t } = useLanguage();

  const resource = type.toUpperCase() as "FOLDER" | "FILE";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("workspace.actions")}</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <UpdateGuard resource={resource}>
          <DropdownMenuItem
            disabled={isSyncedWithCloud}
            onClick={() => {
              setIsCreateFolderOpen("rename");
              setNewFolderName(file.name);
              setUpdateData({ ...file, type: type });
            }}
          >
            <Pencil className="h-4 w-4 mr-2" />
            {type === "folder"
              ? t("workspace.renameFolder")
              : t("workspace.renameFile")}
          </DropdownMenuItem>
        </UpdateGuard>

        <DeleteGuard resource={resource}>
          <DropdownMenuItem
            disabled={isSyncedWithCloud}
            onClick={() => handleDelete(file.id, type)}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {type === "folder"
              ? t("workspace.deleteFolder")
              : t("workspace.deleteFile")}
          </DropdownMenuItem>
        </DeleteGuard>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default MoreButton;
