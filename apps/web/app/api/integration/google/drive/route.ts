import { NextRequest, NextResponse } from "next/server";
import { google } from "googleapis";
import db from "@/lib/shared-db";
import { Readable } from "stream";

const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

const getGoogleDriveClient = async (tenantId: string, userId: string) => {
  const integration = await db.integration.findFirst({
    where: { tenantId, platform: "GOOGLE", userId },
  });

  if (!integration) {
    throw new Error("Google integration not found");
  }

  const oauth2Client = new google.auth.OAuth2(
    process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    `${APP_URL}/api/integration/google/callback`
  );

  oauth2Client.setCredentials({
    access_token: integration.accessToken,
    refresh_token: integration.refreshToken,
  });

  // Refresh access token if expired
  try {
    const { credentials } = await oauth2Client.refreshAccessToken();
    oauth2Client.setCredentials(credentials);

    // Optionally, update the database with the new access token
    await db.integration.update({
      where: { tenantId, platform: "GOOGLE", id: integration?.id, userId },
      data: { accessToken: credentials.access_token },
    });
  } catch (error) {
    console.error("Failed to refresh access token:", error);
  }

  return google.drive({ version: "v3", auth: oauth2Client });
};

export async function POST(req: NextRequest) {
  try {
    const { name, tenantId, parentId, userId } = await req.json();
    const drive = await getGoogleDriveClient(tenantId, userId);

    const fileMetadata = {
      name,
      mimeType: "application/vnd.google-apps.folder",
      parents: parentId ? [parentId] : [],
    };

    const folder = await drive.files.create({
      requestBody: fileMetadata,
      fields: "id, name, mimeType, parents",
    });

    return NextResponse.json(folder.data);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || "Failed to create folder" },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const tenantId = url.searchParams.get("tenantId");
    const parentId = url.searchParams.get("parentId") || "root";
    const userId = url.searchParams.get("userId") ?? "";

    if (!tenantId) {
      return NextResponse.json(
        { message: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const drive = await getGoogleDriveClient(tenantId, userId);
    const response = await drive.files.list({
      q: `'${parentId}' in parents and trashed = false and mimeType = 'application/vnd.google-apps.folder'`,
      fields: "files(id, name, mimeType, parents)",
    });

    return NextResponse.json({ data: response.data.files }, { status: 200 });
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || "Failed to list folders" },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const tenantId = formData.get("tenantId") as string;
    const slug = decodeURI(formData.get("slug") as string);
    const userId = formData.get("userId") as string;

    if (!file || !tenantId) {
      return NextResponse.json(
        { message: "File and tenant ID are required" },
        { status: 400 }
      );
    }
    const workspace = await db.workspace.findFirst({
      where: { tenantId, slug },
    });
    if (!workspace?.gDriveFolderId) {
      return NextResponse.json({ message: "Workspace not found" });
    }
    const drive = await getGoogleDriveClient(tenantId, userId);
    const fileMetadata = {
      name: file.name,
      parents: [workspace?.gDriveFolderId],
      mimeType: file.type,
    };

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const media = {
      mimeType: file.type,
      body: bufferToStream(buffer),
    };

    const uploadedFile = await drive.files.create({
      requestBody: fileMetadata,
      media,
      fields: "id, name, mimeType, parents",
    });

    return NextResponse.json(uploadedFile.data);
  } catch (error: any) {
    console.log({ error });
    return NextResponse.json(
      { message: error.message || "Failed to upload file" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const tenantId = url.searchParams.get("tenantId");
    const fileId = url.searchParams.get("fileId");
    const userId = url.searchParams.get("userId") ?? "";

    if (!tenantId || !fileId) {
      return NextResponse.json(
        { message: "Tenant ID and file ID are required" },
        { status: 400 }
      );
    }

    const drive = await getGoogleDriveClient(tenantId, userId);
    await drive.files.delete({
      fileId: fileId,
    });

    return NextResponse.json({ message: "File deleted successfully" });
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || "Failed to delete file" },
      { status: 500 }
    );
  }
}

function bufferToStream(buffer: Buffer) {
  const stream = new Readable();
  stream.push(buffer);
  stream.push(null);
  return stream;
}
