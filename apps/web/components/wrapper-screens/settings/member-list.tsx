"use client";

import { useState } from "react";
import { updateMemberRole, removeMember } from "@/services";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Check, UserMinus, Shield, User } from "lucide-react";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { InviteUserForm } from "../../model/invite-user-model";

type Member = {
  id: string;
  role: string;
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
};

export function MemberList({
  member,
  tenantId,
  currentUserId,
}: {
  tenantId: string;
  member: any;
  currentUserId: any;
}) {
  const [members, setMembers] = useState<Member[]>(member);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [confirmRemoveOpen, setConfirmRemoveOpen] = useState(false);
  const [actionInProgress, setActionInProgress] = useState(false);
  const { t } = useLanguage();

  const handleChangeRole = async (member: Member, newRole: string) => {
    if (member.role === newRole) return;

    setActionInProgress(true);
    try {
      const result = await updateMemberRole(member.id, newRole);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(
          t("memberList.roleChangeSuccess", {
            name: member.user.name || member.user.email,
            role: newRole,
          })
        );
        // Update the local state
        setMembers(
          members.map((m) => (m.id === member.id ? { ...m, role: newRole } : m))
        );
      }
    } catch (error) {
      toast.error(t("memberList.roleChangeFailed"));
      console.error(error);
    } finally {
      setActionInProgress(false);
    }
  };

  const handleRemoveMember = async () => {
    if (!selectedMember) return;

    setActionInProgress(true);
    try {
      const result = await removeMember(selectedMember.id);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(
          t("memberList.memberRemoveSuccess", {
            name: selectedMember.user.name || selectedMember.user.email,
          })
        );
        // Update the local state
        setMembers(members.filter((m) => m.id !== selectedMember.id));
        setConfirmRemoveOpen(false);
      }
    } catch (error) {
      toast.error(t("memberList.memberRemoveFailed"));
      console.error(error);
    } finally {
      setActionInProgress(false);
      setSelectedMember(null);
    }
  };

  const getInitials = (name: string) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "OWNER":
        return "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100";
      case "ADMIN":
        return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      case "MEMBER":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  // Check if the current user can edit this member
  const canEditMember = (member: Member) => {
    // Get the membership of the current user
    const currentUserMember = members.find((m) => m.user.id === currentUserId);

    // Prevent editing yourself
    if (member.user.id === currentUserId) {
      return false;
    }

    // Owners can edit anyone except other owners
    if (currentUserMember?.role === "OWNER") {
      return member.role !== "OWNER";
    }

    // Admins can only edit members, not other admins or owners
    if (currentUserMember?.role === "ADMIN") {
      return member.role === "MEMBER";
    }

    // Members can't edit anyone
    return false;
  };

  const userRole = members?.find(
    (membership) => membership?.user?.id === currentUserId
  )?.role;
  return (
    <>
      <div>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t("memberList.teamMembers")}</CardTitle>
              <CardDescription>
                {t("memberList.manageTeamMembers")}
              </CardDescription>
            </div>
            <InviteUserForm userRole={userRole || ""} tenantId={tenantId} />
          </div>
        </CardHeader>
        <CardContent className="grid gap-6">
          <div className="divide-y divide-border rounded-md border">
            {members.length === 0 ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                {t("memberList.noTeamMembers")}
              </div>
            ) : (
              members.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-4"
                >
                  <div className="flex items-center gap-4">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={member.user.image || ""} />
                      <AvatarFallback>
                        {getInitials(member.user.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{member.user.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {member.user.email}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getRoleBadgeColor(member.role)}>
                      {member.role}
                    </Badge>

                    {canEditMember(member) && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            disabled={actionInProgress}
                          >
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[160px]">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />

                          {/* Role change options */}
                          <DropdownMenuItem
                            onClick={() => handleChangeRole(member, "ADMIN")}
                            className="flex items-center"
                            disabled={member.role === "ADMIN"}
                          >
                            <Shield className="mr-2 h-4 w-4" />
                            <span>Make Admin</span>
                            {member.role === "ADMIN" && (
                              <Check className="ml-auto h-4 w-4" />
                            )}
                          </DropdownMenuItem>

                          <DropdownMenuItem
                            onClick={() => handleChangeRole(member, "MEMBER")}
                            className="flex items-center"
                            disabled={member.role === "MEMBER"}
                          >
                            <User className="mr-2 h-4 w-4" />
                            <span>Make Member</span>
                            {member.role === "MEMBER" && (
                              <Check className="ml-auto h-4 w-4" />
                            )}
                          </DropdownMenuItem>

                          <DropdownMenuSeparator />

                          {/* Remove option */}
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedMember(member);
                              setConfirmRemoveOpen(true);
                            }}
                            className="text-destructive flex items-center"
                          >
                            <UserMinus className="mr-2 h-4 w-4" />
                            <span>{t("common.remove")}</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </div>

      {/* Confirmation Dialog for Member Removal */}
      <AlertDialog open={confirmRemoveOpen} onOpenChange={setConfirmRemoveOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("memberList.removeTeamMember")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("memberList.confirmRemove", {
                name: selectedMember?.user.name || selectedMember?.user.email,
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={actionInProgress}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleRemoveMember();
              }}
              disabled={actionInProgress}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {actionInProgress ? t("memberList.removing") : t("common.remove")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
