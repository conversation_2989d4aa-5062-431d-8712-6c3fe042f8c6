"use client";

import { useEffect, useRef, useState, useCallback } from "react";

import toast from "react-hot-toast";
import { getCookie } from "@/utils/cookies";
import jwt from "jsonwebtoken";
import { getAuthToken } from "@/lib/api/auth-token";
import {
  playMentionSound,
  playReplySound,
  playGeneralSound,
} from "@/lib/audio/notification-sounds";

interface WebSocketMessage {
  type: "notification" | "thread_update" | "connection_established" | "pong";
  data?: any;
  timestamp: string;
}

interface NotificationData {
  type: "MENTION" | "COMMENT_REPLY" | "THREAD_SHARED";
  title: string;
  content: string;
  userId: string;
  triggeredBy: string;
  chatId?: string;
  messageId?: string;
  commentId?: string;
  tenantId: string;
}

interface UseWebSocketNotificationsOptions {
  onNotification?: (notification: NotificationData) => void;
  onThreadUpdate?: (threadData: any) => void;
  enableToasts?: boolean;
}

export function useWebSocketNotifications(
  options: UseWebSocketNotificationsOptions = {}
) {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [token, setHeaders] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const userId = getCookie("userId") || "";
  const tenantId = getCookie("currentOrganizationId") || "";
  const { onNotification, onThreadUpdate, enableToasts = true } = options;
  useEffect(() => {
    getHeaders();
  }, []);

  const getHeaders = async () => {
    const token = await getAuthToken();
    setHeaders(token);
  };
  // Create a token that expires in 1 hour

  const connect = useCallback(() => {
    // Prevent multiple connections
    if (wsRef.current && wsRef.current.readyState === WebSocket.CONNECTING) {
      console.log("WebSocket connection already in progress");
      return;
    }

    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      console.log("WebSocket already connected");
      return;
    }

    if (!token || !userId) {
      console.log("No session or user ID available for WebSocket connection");
      return;
    }

    if (!tenantId) {
      console.log("No tenant ID available for WebSocket connection");
      return;
    }

    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || "ws://localhost:8000/ws";
    const url = `${wsUrl}?token=${token}&user_id=${userId}&tenant_id=${tenantId}`;
    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log("WebSocket connected");
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttempts.current = 0;
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log("WebSocket message received:", message);

          switch (message.type) {
            case "notification": {
              const notificationData = message.data as NotificationData;

              // Call custom notification handler
              if (onNotification) {
                onNotification(notificationData);
              }

              // Show toast notification if enabled
              if (enableToasts) {
                const toastMessage = `${notificationData.title}: ${notificationData.content}`;

                if (notificationData.type === "MENTION") {
                  toast.success(toastMessage, {
                    duration: 5000,
                    icon: "🔔",
                  });
                  // Play mention sound
                  playMentionSound().catch((error) =>
                    console.warn("Failed to play mention sound:", error)
                  );
                } else if (notificationData.type === "COMMENT_REPLY") {
                  toast.success(toastMessage, {
                    duration: 4000,
                    icon: "💬",
                  });
                  // Play reply sound
                  playReplySound().catch((error) =>
                    console.warn("Failed to play reply sound:", error)
                  );
                } else {
                  toast.success(toastMessage, {
                    duration: 4000,
                  });
                  // Play general notification sound
                  playGeneralSound().catch((error) =>
                    console.warn("Failed to play general sound:", error)
                  );
                }
              }
              break;
            }

            case "thread_update":
              if (onThreadUpdate) {
                onThreadUpdate(message.data);
              }
              break;

            case "connection_established":
              console.log("WebSocket connection established:", message.data);
              break;

            case "pong":
              console.log("WebSocket pong received");
              break;

            default:
              console.log("Unknown WebSocket message type:", message.type);
          }
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      ws.onclose = (event) => {
        console.log("WebSocket disconnected:", event.code, event.reason);
        setIsConnected(false);
        wsRef.current = null;

        // Attempt to reconnect if not a normal closure
        if (
          event.code !== 1000 &&
          reconnectAttempts.current < maxReconnectAttempts
        ) {
          const delay = Math.min(
            1000 * Math.pow(2, reconnectAttempts.current),
            30000
          );
          console.log(
            `Attempting to reconnect in ${delay}ms (attempt ${
              reconnectAttempts.current + 1
            })`
          );

          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        } else if (reconnectAttempts.current >= maxReconnectAttempts) {
          setConnectionError("Failed to reconnect after multiple attempts");
        }
      };

      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
        setConnectionError("WebSocket connection error");
      };
    } catch (error) {
      console.error("Error creating WebSocket connection:", error);
      setConnectionError("Failed to create WebSocket connection");
    }
  }, [token, tenantId, userId]); // Removed callback dependencies to prevent re-connections

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, "Manual disconnect");
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionError(null);
    reconnectAttempts.current = 0;
  }, []);

  const sendPing = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send("ping");
    }
  }, []);

  // Connect when token is available and not already connected
  useEffect(() => {
    console.log("WebSocket useEffect triggered:", {
      token: !!token,
      userId: !!userId,
      tenantId: !!tenantId,
      isConnected,
      hasWebSocket: !!wsRef.current,
      wsState: wsRef.current?.readyState,
    });

    if (token && userId && tenantId && !isConnected && !wsRef.current) {
      console.log("Initiating WebSocket connection...");
      connect();
    }

    return () => {
      // Only disconnect on unmount, not on re-renders
      if (wsRef.current) {
        console.log("Cleaning up WebSocket connection on unmount");
        disconnect();
      }
    };
  }, [token, userId, tenantId, isConnected]); // Removed connect/disconnect to prevent loops

  // Ping every 30 seconds to keep connection alive
  useEffect(() => {
    if (isConnected) {
      const pingInterval = setInterval(sendPing, 30000);
      return () => clearInterval(pingInterval);
    }
  }, [isConnected, sendPing]);

  return {
    isConnected,
    connectionError,
    connect,
    disconnect,
    sendPing,
  };
}
