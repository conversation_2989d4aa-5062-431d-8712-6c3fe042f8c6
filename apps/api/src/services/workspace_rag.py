import os
from typing import Dict, List, Optional, Any, Union
import logging
import json
from bson import ObjectId
from src.agents.rag_agent import AdvancedRAGAgent, CustomJSONEncoder
from src.services.usage_tracker import UsageTracker
from src.utils.robust_citation_enhancer import RobustCitationEnhancer
from src.utils.intelligent_query_interpreter import query_interpreter
from pymongo import MongoClient
from fastapi.responses import StreamingResponse
import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
import json


class WorkspaceRAGManager:
    """A manager class that handles workspace access control for RAG agents.

    Ensures users can only access documents from workspaces they have permission to access.
    """

    def __init__(self, db_client=None):
        """Initialize the workspace RAG manager.

        Args:
            db_client: Database client for checking workspace access (Prisma client or similar)
        """

        self.db_client = db_client
        self.workspace_agents = {}  # Cache of RAG agents by workspace_id
        self.agents = {}  # Cache of multi-workspace agents
        self.usage_tracker = UsageTracker(db_client)  # Initialize usage tracker

    async def get_user_workspaces(self, user_id: str,tenant_id:str) -> List[str]:
        """Get all workspaces that a user has access to.

        Args:
            user_id: The ID of the user

        Returns:
            List of workspace IDs
        """
        if self.db_client is None:
            logger.error("Database client not initialized")
            return []
        try:
            workspace_cursor = self.db_client.Workspace.find(
                {"tenantId": ObjectId(tenant_id)},
                {"_id": 1}
            )
            workspace_ids = await workspace_cursor.to_list(length=None)
            # Query workspaces where user is a member
            cursor = self.db_client.WorkspaceMember.find(
                {
                    "userId": ObjectId(user_id),
                    "workspaceId": {"$in": [workspace['_id'] for workspace in workspace_ids]}
                },
                {"_id": 1, "workspaceId": 1}
            )

            workspaces = await cursor.to_list(length=None)


            # Convert ObjectId to string before returning
            return [str(workspace["workspaceId"]) for workspace in workspaces]
        except Exception as e:
            logger.error(f"Error fetching user workspaces: {e}")
            return []

    async def check_workspace_access(self, user_id: str, workspace_id: str,tenant_id:str) -> bool:
        """Check if a user has access to a specific workspace.

        Args:
            user_id: The ID of the user
            workspace_id: The ID of the workspace

        Returns:
            Boolean indicating if the user has access
        """
        if self.db_client is None:
            logger.error("Database client not initialized")
            return False

        try:
            # Check if user is a member of the workspace
            workspace_member = await self.db_client.WorkspaceMember.find_one(
                {
                    "userId": ObjectId(user_id),
                    "workspaceId": ObjectId(workspace_id)
                }
            )
            return workspace_member is not None
        except Exception as e:
            logger.error(f"Error checking workspace access: {e}")
            return False

    async def get_workspace_agent(self, workspace_id: str, tenant_id: str) -> Optional[AdvancedRAGAgent]:
        """Get or create a RAG agent for a specific workspace.

        Args:
            workspace_id: The ID of the workspace
            config: Configuration for the RAG agent if it needs to be created

        Returns:
            AdvancedRAGAgent for the workspace or None if error
        """
        # Return cached agent if available
        if workspace_id in self.workspace_agents:
            return self.workspace_agents[workspace_id]

        try:
            llmModel=(await self.db_client.LLMSettings.find_one({"tenantId":ObjectId(tenant_id)}))or {}
            vectorDBSettings=(await self.db_client.VectorDBSettings.find_one({"tenantId":ObjectId(tenant_id)}))or {}
            embeddingSettings=(await self.db_client.EmbeddingSettings.find_one({"tenantId":ObjectId(tenant_id)})) or {}

            # Get vector store configuration
            vector_store_config = {

                "connection_string":vectorDBSettings.get("localUri", os.getenv("VECTOR_DATABASE_URL")) ,
                "db_name": vectorDBSettings.get("environment", os.getenv("VECTOR_DATABASE_NAME")),
                "text_key": "content",
                "embedding_key": "embedding",
                "index_name": "default_vector_index",
                # Add workspace ID to vector store config to isolate data
                "namespace": f"workspace_{workspace_id}"
            }

            # Create vector search index if it doesn't exist
            if vector_store_config.get("connection_string") and vector_store_config.get("db_name") and vectorDBSettings.get("provider","MONGODB")=="MONGODB":
                try:
                    from pymongo import MongoClient

                    # Connect to MongoDB
                    client = MongoClient(vector_store_config["connection_string"])
                    db_name = vector_store_config["db_name"]
                    collection_name = vector_store_config["namespace"]

                    # Create database if it doesn't exist (in MongoDB this is implicit)
                    db = client[db_name]

                    # Check if collection exists, create if not
                    if collection_name not in db.list_collection_names():
                        logger.info(f"Creating collection {collection_name}")
                        db.create_collection(collection_name)

                    # Get index name and field names
                    index_name = vector_store_config.get("index_name", "default_vector_index")
                    embedding_key = vector_store_config.get("embedding_key", "embedding")

                    # Create vector search index using Cosmos DB syntax
                    try:
                        # Check if index exists by listing indexes
                        existing_indexes = list(db[collection_name].list_indexes())
                        index_exists = any(idx.get('name') == index_name for idx in existing_indexes)

                        if not index_exists:
                            logger.info(f"Creating Cosmos DB vector index {index_name} for collection {collection_name}")

                            # Create the index using Cosmos DB syntax
                            result = db.command({
                                "createIndexes": collection_name,
                                "indexes": [{
                                    "name": index_name,
                                    "key": {embedding_key: "cosmosSearch"},
                                    "cosmosSearchOptions": {
                                        "kind": "vector-ivf",
                                        "numLists": 1,  # Adjust based on collection size
                                        "similarity": "COS",
                                        "dimensions": 1536  # OpenAI embedding dimensions
                                    }
                                }]
                            })
                            logger.info(f"Successfully created Cosmos DB vector index: {result}")
                    except Exception as e:
                        logger.warning(f"Error creating Cosmos DB vector index: {e}")
                except Exception as e:
                    logger.warning(f"Error connecting to database: {e}")
            # Determine embedding model type based on configuration
            embedding_model_config = {
                "deployment_name": os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT") or os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0"),
                "azure_endpoint": os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT") or os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                "api_version": os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                "model_name": str(embeddingSettings.get("embedderModel", os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0"))),
                "api_key": str(embeddingSettings.get("apiKey", os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY")))
            }

            # Use Azure Vision embeddings if AZURE_OPENAI_EMBEDDING_MODEL is configured
            if os.getenv("AZURE_OPENAI_EMBEDDING_MODEL") and os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"):
                embedding_model_config["type"] = "azure_vision"
            else:
                embedding_model_config["type"] = "azure"

            # Create new agent with workspace-specific configuration
            agent = AdvancedRAGAgent(
                vector_store_type=str(vectorDBSettings.get("provider", "mongodb")).lower(),
                embedding_model=embedding_model_config,
                llm_model= {
                    "type":str(llmModel.get("provider","deepseek")).lower(),
                    "deployment_name":str(llmModel.get("azureOpenAIDeployment",os.getenv("AZURE_OPENAI_DEPLOYMENT"))),
                    "azure_endpoint":str(llmModel.get("azureOpenAIEndpoint",os.getenv("AZURE_OPENAI_API_ENDPOINT"))),
                    "api_version": str(llmModel.get("azureOpenAIApiVersion",os.getenv("AZURE_OPENAI_API_VERSION"))),
                    "model_name": str(llmModel.get("model_name",os.getenv("AZURE_OPENAI_MODEL"))),
                    "api_key": str(llmModel.get("apiKey",os.getenv("DEEPSEEK_API_KEY")))
                },
                api_keys={
                    "OPENAI_API_KEY":str(embeddingSettings.get("apiKey",os.getenv("OPENAI_API_KEY"))),
                    "AZURE_OPENAI_EMBEDDING_API_KEY":str(embeddingSettings.get("apiKey",os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"))),
                    "DEEPSEEK_API_KEY":str(llmModel.get("apiKey",os.getenv("DEEPSEEK_API_KEY"))),
                    "AZURE_OPENAI_API_KEY":str(llmModel.get("apiKey",os.getenv("AZURE_OPENAI_API_KEY"))),
                },
                vector_store_config=vector_store_config
            )

            # Cache the agent
            self.workspace_agents[workspace_id] = agent
            return agent
        except Exception as e:
            logger.error(f"Error creating RAG agent for workspace {workspace_id}: {e}")
            return None

    async def query_workspace(self, user_id: str, question: str = None, stream: bool = False, tenant_id: str = None, user_name: str = None, previous_message: str = None, include_web_results: bool = False) -> Dict[str, Any]:
        try:
            self.usage_tracker.db_client = self.db_client
            start_time = datetime.datetime.now(datetime.timezone.utc)

            # Check if this is a simple greeting that doesn't need document retrieval
            is_simple_greeting = RobustCitationEnhancer._is_simple_greeting(question)
            if is_simple_greeting:
                logger.info(f"Simple greeting detected: '{question}'. Using simplified response.")
                # For simple greetings, we can return a simplified response without document retrieval
                if not stream:
                    return {
                        "answer": f"Hello! How can I help you today?",
                        "sources": [],  # No sources for simple greetings
                        "status": 200
                    }

            # Continue with normal processing for non-greetings or streaming responses
            accessible_workspaces = await self.get_user_workspaces(user_id, tenant_id)

            if not accessible_workspaces:
                error_msg = "No accessible workspaces found"
                await self.usage_tracker.log_api_request(
                    user_id=user_id,
                    # workspace_id=None,
                    tenant_id=tenant_id,
                    endpoint="/query_workspace",
                    method="POST",
                    status_code=403,
                    duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000),
                    error_message=error_msg
                )
                async def error_stream():
                    yield {
                        "error": "No workspaces found. Please contact the administrator.",
                        "answer": "",
                        "title": "",
                        "done": True
                    }
                return error_stream()


            agent = await self.get_multi_workspace_agent(accessible_workspaces, tenant_id)

            if not agent:
                error_msg = "No agents available for accessible workspaces"
                await self.usage_tracker.log_api_request(
                    user_id=user_id,
                    # workspace_id=accessible_workspaces[0] if accessible_workspaces else None,
                    tenant_id=tenant_id,
                    endpoint="/query_workspace",
                    method="POST",
                    status_code=500,
                    duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000),
                    error_message=error_msg
                )
                async def error_stream():
                    yield {
                        "error": "Failed to initialize the chat, please try again later.",
                        "answer": "",
                        "title": "",
                        "done": True
                    }
                return error_stream()
            # The connection should already be established in get_multi_workspace_agent
            # But we'll check if the retrievers are properly initialized
            if not hasattr(agent.qa_chain, 'retriever') or not agent.qa_chain.retriever:
                connection_success = agent.connect_to_existing_store(workspace_ids=accessible_workspaces)
                if not connection_success:
                    error_msg = "Failed to initialize the chat,retrievers not properly initialized"
                    await self.usage_tracker.log_api_request(
                        user_id=user_id,
                        # workspace_id=accessible_workspaces[0] if accessible_workspaces else None,
                        tenant_id=tenant_id,
                        endpoint="/query_workspace",
                        method="POST",
                        status_code=500,
                        duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000),
                        error_message=error_msg
                    )
                    async def error_stream():
                        yield {
                            "error": "Failed to initialize the chat, please try again later.",
                            "answer": "",
                            "title": "",
                            "sources": [],  # Include empty sources array for consistency
                            "done": True
                        }
                    return error_stream()
            model_name=agent.llm_model_config.get('model_name', 'unknown')
            if not stream:
                result = await agent.query(
                    user_name=user_name,
                    question=question,
                    stream=False,
                    usage_tracker=self.usage_tracker,
                    tenant_id=tenant_id,
                    user_id=user_id,
                    start_time=start_time,
                    model_name=model_name,
                    previous_message=previous_message,
                    include_web_results=include_web_results
                    )
                if not result or not result.get("answer"):
                    return {
                        "error": "No results found across accessible workspaces",
                        "answer": "",
                        "title": "",
                        "sources": [],  # Include empty sources array for consistency
                        "done": True
                    }

                # Return the structured JSON response with title and answer
                await self.usage_tracker.log_token_usage(
                    user_id=user_id,
                    workspace_id=accessible_workspaces[agents.index(agent)],
                    tenant_id=tenant_id,
                    input_text=question,
                    output_text=result.get("answer", ""),
                    request_type='rag_query',
                    model_used=model_name
                )
                return {
                    "answer": result.get("answer", ""),
                    "title": result.get("title", ""),
                    "sources": result.get("sources", []),  # Include sources in the response
                    "status": result.get("status", 200)
                }
            else:
                # Return the async generator directly
                return await agent.query(
                question=question,
                user_name=user_name,
                 stream=True,
                 usage_tracker=self.usage_tracker,
                 tenant_id=tenant_id,
                 user_id=user_id,
                 model_name=model_name,
                 start_time=start_time,
                 previous_message=previous_message,
                 include_web_results=include_web_results
                )

        except Exception as e:
            logger.error(f"Error querying workspaces: {e}")
            async def error_stream():
                yield {
                    "error": "Failed to processing the query, please try again later.",
                    "answer": "",
                    "title": "",
                    "sources": [],  # Include empty sources array for consistency
                    "done": True
                }
            return error_stream()

    async def query(self, question, user_name, stream=False, previous_message=None):
        """
        Query using the multi-workspace retriever with optional streaming support.

        Args:
            question: The question to answer
            stream: Whether to stream the response

        Returns:
            Query result or async generator if streaming
        """
        if not hasattr(self, 'qa_chain') or self.qa_chain is None:
            async def error_stream():
                yield {
                    "error": "Failed to processing the query, please try again later.",
                    "answer": "",
                    "title": "",
                    "sources": [],  # Include empty sources array for consistency
                    "done": True
                }
            return error_stream()

        # Regular non-streaming approach
        if not stream:
            try:
                # Execute the query against the combined retrievers
                result = self.qa_chain({"query": question})

                # Extract the answer
                answer = result.get("result", "")
                # Extract sources from the result
                sources = []
                if "source_documents" in result:
                    for doc in result["source_documents"]:
                        sources.append({
                            "content": doc.page_content,
                            "metadata": doc.metadata
                        })

                # Enhance sources with relevance scores and relevant text using robust enhancer
                enhanced_sources = RobustCitationEnhancer.enhance_sources(sources, question)

                return {
                    "answer": answer,
                    "sources": enhanced_sources
                }
            except Exception as e:
                logger.error(f"Error executing query: {e}")
                async def error_stream():
                    yield {
                        "error": "Failed to processing the query, please try again later.",
                        "answer": "",
                        "title": "",
                        "sources": [],  # Include empty sources array for consistency
                        "done": True
                    }
                return error_stream()
                return {"error": f"Query execution failed: {str(e)}"}

        # Streaming implementation
        else:
            async def response_stream():
                try:
                    # First, retrieve the documents
                    retriever = self.qa_chain.retriever
                    docs = retriever.get_relevant_documents(question)

                    # Prepare the sources information
                    sources = []
                    for doc in docs:
                        sources.append({
                            "content": doc.page_content,
                            "metadata": doc.metadata
                        })

                    # Enhance sources with relevance scores and relevant text using robust enhancer
                    sources = RobustCitationEnhancer.enhance_sources(sources, question)

                    # Get the prompt template from the chain
                    prompt = self.qa_chain.combine_documents_chain.llm_chain.prompt

                    # Format the prompt with the documents and question
                    is_first_message = not previous_message or previous_message.strip() == ""
                    query_params = {
                        "query": question,
                        "previous_message": previous_message if previous_message else "",
                        "is_first_message": "true" if is_first_message else "false"
                    }
                    input_text = self.qa_chain.combine_documents_chain.combine_docs(docs, query_params)

                    # Create consistent prompt parameters
                    prompt_params = {
                        "context": input_text,
                        "question": question,
                        "user_name": user_name,
                        "previous_message": previous_message if previous_message else "",
                        "is_first_message": "true" if is_first_message else "false"
                    }

                    # Log conversation context for debugging
                    if previous_message and len(previous_message.strip()) > 0:
                        logger.info(f"WorkspaceRAG: Processing conversation with history for user: {user_name}")
                    else:
                        logger.info(f"WorkspaceRAG: Processing first message for user: {user_name}")

                    formatted_prompt = prompt.format_prompt(**prompt_params)
                    # Create a streaming call to the LLM
                    stream = await self.llm.astream(formatted_prompt.to_string())


                    # Send first chunk with sources, title but empty answer
                    # Convert ObjectId to string using CustomJSONEncoder
                    serializable_sources = json.loads(json.dumps(sources, cls=CustomJSONEncoder))
                    yield {
                        "answer": "",
                        "sources": serializable_sources,  # Include the document sources in the first chunk
                        "is_first_chunk": True,  # Add a marker to identify this as the first chunk
                        "done": False
                    }

                    # Initialize the complete answer
                    complete_answer = ""

                    # Stream the token chunks as they come in
                    async for chunk in stream:
                        # Extract the content based on the response format
                        if hasattr(chunk, 'content'):
                            token = chunk.content
                        else:
                            token = str(chunk)
                        complete_answer += token
                        # Yield the current state with processed answer
                        yield {
                            "answer": token,
                            "sources": serializable_sources,  # Include the serialized document sources in each chunk
                            "done": False
                        }

                    # Process the final answer using the same logic as streaming chunks
                    # Send final chunk indicating completion
                    yield {
                        "answer": complete_answer,
                        "sources": serializable_sources,  # Include the serialized document sources in the final chunk
                        "done": True
                    }

                except Exception as e:
                    logger.error(f"Error in streaming response: {e}")

                    yield {
                        "error": f"Query execution failed: {str(e)}",
                        "sources": [],  # Include empty sources array for consistency
                        "done": True
                    }

            return response_stream()

    async def get_multi_workspace_agent(self, workspace_ids, tenant_id):
        """
        Get or create an agent that can access multiple workspaces.

        Args:
                    workspace_ids: List of workspace IDs
                    tenant_id: The tenant ID

        Returns:
                    Agent instance or None if an error occurs
        """
        # Use the first workspace ID as a key for caching purposes
        cache_key = f"multi_{workspace_ids[0]}_{len(workspace_ids)}"
        llmModel=(await self.db_client.LLMSettings.find_one({"tenantId":ObjectId(tenant_id)})) or {}
        vectorDBSettings=(await self.db_client.VectorDBSettings.find_one({"tenantId":ObjectId(tenant_id)})) or {}
        embeddingSettings=(await self.db_client.EmbeddingSettings.find_one({"tenantId":ObjectId(tenant_id)})) or {}

        # Check if we already have an agent for this combination
        if cache_key in self.agents:
            return self.agents[cache_key]

        # Create a new agent
        try:
            # You might need to adjust this based on your agent implementation
            vector_store_config = {
                "connection_string":vectorDBSettings.get("localUri", os.getenv("VECTOR_DATABASE_URL")) ,
                "db_name": vectorDBSettings.get("environment", os.getenv("VECTOR_DATABASE_NAME")),
                "text_key": "content",
                "embedding_key": "embedding",
                "index_name": "default_vector_index",
                # Add workspace ID to vector store config to isolate data
                "namespace": f"workspace_{workspace_ids[0]}"
            }

            # Determine embedding model type based on configuration
            embedding_model_config = {
                "deployment_name": os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT") or os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0"),
                "azure_endpoint": os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT") or os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                "api_version": os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                "model_name": str(embeddingSettings.get("embedderModel", os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "embed-v-4-0"))),
                "api_key": str(embeddingSettings.get("apiKey", os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY")))
            }

            # Use Azure Vision embeddings if AZURE_OPENAI_EMBEDDING_MODEL is configured
            if os.getenv("AZURE_OPENAI_EMBEDDING_MODEL") and os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"):
                embedding_model_config["type"] = "azure_vision"
            else:
                embedding_model_config["type"] = "azure"

            agent = AdvancedRAGAgent(
                vector_store_type=str(vectorDBSettings.get("provider", "mongodb")).lower(),
                embedding_model=embedding_model_config,
                llm_model= {
                    "type":str(llmModel.get("provider","deepseek")).lower(),
                    "deployment_name":str(llmModel.get("azureOpenAIDeployment",os.getenv("AZURE_OPENAI_DEPLOYMENT"))),
                    "azure_endpoint":str(llmModel.get("azureOpenAIEndpoint",os.getenv("AZURE_OPENAI_API_ENDPOINT"))),
                    "api_version": str(llmModel.get("azureOpenAIApiVersion",os.getenv("AZURE_OPENAI_API_VERSION"))),
                    "api_key": str(llmModel.get("apiKey",os.getenv("DEEPSEEK_API_KEY"))),
                    "model_name": str(llmModel.get("model_name",os.getenv("AZURE_OPENAI_MODEL"))),

                },
                api_keys={
                    "OPENAI_API_KEY":str(embeddingSettings.get("apiKey",os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"))),
                    "DEEPSEEK_API_KEY":str(embeddingSettings.get("apiKey",os.getenv("DEEPSEEK_API_KEY"))),
                    "AZURE_OPENAI_API_KEY":str(llmModel.get("apiKey",os.getenv("AZURE_OPENAI_API_KEY"))),
                },
                vector_store_config=vector_store_config
            )

            # Store workspace_ids with the agent for later use
            agent.workspace_ids = workspace_ids
            # Initialize retrievers for the agent to prevent the "retrievers" field error
            # This ensures the MultiWorkspaceRetriever will have the retrievers field properly initialized
            connection_success = agent.connect_to_existing_store(workspace_ids=workspace_ids)
            if not connection_success:
                logger.warning(f"Failed to connect to vector stores for workspaces: {workspace_ids}")

            # Cache the agent
            self.agents[cache_key] = agent
            return agent
        except Exception as e:
            logger.error(f"Error creating multi-workspace agent: {e}")
            return None

    async def index_document_with_access_check(self, user_id: str, workspace_id: str,
                                             document_path: str, document_type: str = "auto",
                                             metadata: Dict[str, Any] = None,tenant_id:str=None,file_id:str=None,slug:str=None) -> Dict[str, Any]:
        """Index a document in a workspace with access control check.

        Args:
            user_id: The ID of the user uploading the document
            workspace_id: The ID of the workspace
            document_path: Path to the document file
            document_type: Type of document (pdf, text, etc.)
            metadata: Additional metadata to add to the document
            config: Configuration for the RAG agent if needed

        Returns:
            Result of indexing operation
        """
        # Check access first
        has_access = await self.check_workspace_access(user_id, workspace_id,tenant_id)
        if not has_access:
            logger.warning(f"User {user_id} attempted to index document in unauthorized workspace {workspace_id}")
            return {
                "error": "You do not have access to this workspace",
                "status": 403
            }

        # Get or create agent for this workspace
        agent = await self.get_workspace_agent(workspace_id,tenant_id)
        if not agent:
            return {
                "error": "Failed to initialize document indexing for this workspace",
                "status": 500
            }

        try:
            # Load and index the document
            documents = agent.load_documents(document_path, document_type)

            # Add workspace metadata to all documents
            base_metadata = {"workspace_id": workspace_id,"fileId":file_id,"slug":slug}
            if metadata:
                base_metadata.update(metadata)

            for doc in documents:
                doc.metadata.update(base_metadata)

            agent.index_documents(documents)
            await self.db_client.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "COMPLETED"}}
            )

            return {
                "status": 200,
                "message": f"Successfully indexed document in workspace {workspace_id}",
                "document_count": len(documents)
            }
        except Exception as e:
            await self.db_client.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "FAILED"}}
            )
            logger.error(f"Error indexing document in workspace {workspace_id}: {e}")
            return {
                "error": f"Error indexing document: {str(e)}",
                "status": 500
            }
    async def generate_title_from_question(self, question: str,tenant_id:str=None, workspace_id: str = None) -> Dict[str, Any]:
        """
        Generate a title for a given question based on the user's question history.

        Args:
            question: The question for which a title is to be generated

        Returns:
            A dictionary containing the generated title or an error message.
        """
        agent = await self.get_workspace_agent(workspace_id,tenant_id)
        if not agent:
            return {
                "error": "Failed to initialize title generation",
                "status": 500
            }
        try:
            prompt = f"Generate a concise and engaging 3 to 4-word title summarizing this question: '{question}'"
            title = agent.llm.invoke(prompt)

            return {
                "title": title.content,
                "status": 200
            }
        except Exception as e:
            logger.error(f"Error generating title: {e}")
            return {
                "error": f"Error generating title: {str(e)}",
                "status": 500
            }

    async def delete_file(self,file_id:str=None, workspace_id: str = None,tenant_id:str=None) -> Dict[str, Any]:
        """
        Delete a file from the workspace.
        Args:
            file_id: The ID of the file to be deleted
        Returns:
            A dictionary containing the result of the deletion operation.
        """
        try:

            agent = await self.get_workspace_agent(workspace_id,tenant_id)
            if not agent:
                return {
                    "error": "Failed to initialize title generation",
                    "status": 500
                }
            result = agent.delete_documents(file_id=file_id,collection_name=f"workspace_{workspace_id}")
            return {
                "status": 200,
                "message": f"Successfully deleted file {file_id}"
            }
        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            return {
                "error": f"Error deleting file: {str(e)}",
                "status": 500
            }