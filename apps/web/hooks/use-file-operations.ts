import {
  updateFile,
  deleteFile,
  createFile,
  recordVectorStoreUsage,
} from "@/services";
import { useLanguage } from "@/lib/language-context";
import { uploadPipeline } from "@/services/src/upload-pipeline";
import { workspaceChatService } from "@/services/workspace-chat";
import { getCookie } from "@/utils/cookies";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { SupportedExtensions } from "@/lib/constant/supported-extensions";

interface FileOperationsProps {
  workspaceSlug: string;
  tenantId: string;
  pageId: string;
  setIsLoading: any;
  usageSummary: any;
}

export const useFileOperations = ({
  workspaceSlug,
  tenantId,
  pageId,
  setIsLoading,
  usageSummary,
}: FileOperationsProps) => {
  const userId = getCookie("userId") || "";
  const router = useRouter();
  const { t } = useLanguage();

  const handleFileUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    folderId: string,
    files: any[],
    setFiles: (files: any[]) => void,
    setIsUploadOpen: (isOpen: boolean) => void
  ) => {
    setIsLoading(true);
    const uploadedFiles = e.target.files;
    if (uploadedFiles && uploadedFiles.length > 0) {
      try {
        // Check vector store usage before uploading
        try {
          // Calculate total size of files to be uploaded in GB
          const totalUploadSizeGB = Array.from(uploadedFiles).reduce(
            (total, file) => total + file.size / (1024 * 1024 * 1024),
            0
          );

          // Check if upload would exceed the limit
          const currentUsageGB = usageSummary.totalUsage || 0;
          const limitGB = usageSummary.limit || 0;

          if (limitGB > 0 && currentUsageGB + totalUploadSizeGB > limitGB) {
            // Inform user about exceeding the limit
            const willExceedBy = (
              currentUsageGB +
              totalUploadSizeGB -
              limitGB
            ).toFixed(2);

            // Ask user for confirmation before proceeding
            const confirmMessage =
              t("subscription.vectorStorageWarning", { willExceedBy }) +
              "\n\n" +
              t("subscription.vectorStorageUsage", {
                currentUsageGB: currentUsageGB.toFixed(2),
                limitGB: limitGB,
              }) +
              "\n\n" +
              t("subscription.vectorStorageContinue");

            const confirmUpload = window.confirm(confirmMessage);

            if (!confirmUpload) {
              setIsLoading(false);
              return;
            }
          }
        } catch (usageError) {
          console.error("Error checking vector store usage:", usageError);
          // Continue with upload even if we can't check usage
        }
        toast.loading("Uploading files...");
        const uploadPromises = Array.from(uploadedFiles).map(async (file) => {
          const formData = new FormData();
          formData.append("file", file);
          formData.append("tenantId", tenantId);
          formData.append("workspaceSlug", workspaceSlug);
          formData.append("pageId", pageId);

          const response = await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });

          let gDriveFileId = "";
          let oneDriveFileId = "";

          // Use the upload pipeline to handle multiple uploads with retry and revert
          try {
            const uploadResults = await uploadPipeline.uploadFile(file, {
              workspaceSlug,
              tenantId,
            });

            // Extract file IDs from upload results
            uploadResults.forEach((result) => {
              if (result.success) {
                if (result.targetName === "googleDrive") {
                  gDriveFileId = result.fileId || "";
                } else if (result.targetName === "outlookDrive") {
                  oneDriveFileId = result.fileId || "";
                }
              }
            });
          } catch (error) {
            console.error("Upload pipeline error:", error);
            // Continue with the main upload as fallback
          }
          const data = await response.json();
          if (!response.ok) throw new Error(data.error);

          // Try to record vector store usage, but continue even if it fails
          try {
            const usageResult = await recordVectorStoreUsage(
              tenantId,
              file.size / (1024 * 1024 * 1024)
            );

            if (usageResult === null) {
              console.warn(
                `Vector store usage recording failed for file ${file.name}, but continuing with upload`
              );
            }
          } catch (usageError) {
            // Log the error but don't fail the upload
            console.error(
              `Failed to record vector store usage, but continuing with upload:`,
              usageError
            );
          }
          return {
            name: file.name.substring(0, file.name.lastIndexOf(".")),
            type: "file" as const,
            ...(folderId && folderId.trim() !== "" ? { folderId } : {}),
            workspaceSlug: workspaceSlug,
            pageId: pageId,
            parentId: folderId && folderId.trim() !== "" ? folderId : pageId,
            extension: file.name.split(".").pop() || "",
            size: `${Math.round(file.size / 1024)} KB`,
            oneDriveFileId,
            gDriveFileId,
            createdAt: new Date().toISOString(),
            url: data.url,
          };
        });

        const newFiles = await Promise.all(uploadPromises);
        const file = await createFile(
          {
            files: newFiles,
            workspaceSlug,
            tenantId,
          },
          tenantId,
          userId
        );

        file?.data?.map((item: any) => {
          if (SupportedExtensions?.includes(item?.extension)) {
            workspaceChatService?.uploadForIndexing({
              userId: userId,
              document_path: item.url,
              workspaceSlug: workspaceSlug,
              tenantId,
              file_id: item?.id,
            });
          }
        });
        toast.remove();
        setIsLoading(false);
        if (file?.error) {
          toast.error("Failed to upload files. Please try again.");
          return;
        }
        setFiles([...files, ...(file?.data ?? [])]);
        setIsUploadOpen(false);
        toast.remove();
        toast.success("Files uploaded successfully!");
        router.refresh();
      } catch (error) {
        console.error("Error uploading files:", error);
        toast.error("Failed to upload files. Please try again.");
      }
    }
  };

  const handleRenameFile = async (fileId: string, newName: string) => {
    if (!newName) {
      toast.error("File name cannot be empty.");
      return false;
    }
    toast.loading("Renaming file...");
    setIsLoading(true);
    const updatedFile = await updateFile(
      {
        id: fileId,
        name: newName,
      },
      tenantId,
      userId
    );
    toast.remove();
    setIsLoading(false);
    if (updatedFile?.message) {
      toast.success(updatedFile?.message ?? "File renamed successfully!");
      window.location.reload();
      return true;
    } else {
      toast.error(
        updatedFile?.error ?? "Failed to rename file. Please try again."
      );
      return false;
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    toast.loading("Deleting file...");
    setIsLoading(true);
    workspaceChatService.deleteFile({
      fileId: fileId,
      workspaceSlug: workspaceSlug,
      tenantId: tenantId,
    });
    const deletedFile = await deleteFile(fileId, tenantId, userId);
    if (deletedFile?.message) {
      toast.success(deletedFile?.message ?? "File deleted successfully!");
      window.location.reload();
      return true;
    } else {
      toast.error(
        deletedFile?.error ?? "Failed to delete file. Please try again."
      );
      return false;
    }
  };

  return {
    handleFileUpload,
    handleRenameFile,
    handleDeleteFile,
  };
};
