import { getWorkspace } from "@/services";
import { cookies, headers } from "next/headers";
import { redirect } from "next/navigation";

export default async function WorkspacePage({ children }) {
  const pathname = (await headers().get("x-next-pathname")) as string;
  const workspaceSlug = pathname?.split("/")?.[2] ?? "";
  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;
  const workspace = await getWorkspace(tenantId, userId, workspaceSlug);
  if (!workspace?.workspace?.id) return redirect(`/404`);
  return <>{children}</>;
}
