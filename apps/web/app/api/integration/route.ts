import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import db from "@/lib/shared-db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const tenantId = searchParams.get("tenantId");
    const userId = searchParams.get("userId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "TenantId is required" },
        { status: 400 }
      );
    }

    if (id) {
      const integration = await db.integration.findFirst({
        where: {
          id,
          userId,
        },
      });

      if (!integration) {
        return NextResponse.json({ error: "Page not found" }, { status: 404 });
      }

      return NextResponse.json({ data: integration });
    }

    const integration = await db.integration.findMany({
      where: {
        tenantId: tenantId,
        userId,
      },
    });

    return NextResponse.json({ data: integration });
  } catch (error) {
    console.error("Get integration error:", error);
    return NextResponse.json(
      { error: "Failed to fetch integration" },
      { status: 500 }
    );
  }
}

// Update a page
export async function PUT(request: NextRequest) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, tenantId, workspaceId } = body;

    if (!id || !tenantId) {
      return NextResponse.json(
        { error: "Page ID and tenantId are required" },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Check if page exists and belongs to the tenant
    const existingPage = await db.page.findFirst({
      where: {
        id,
        workspace: {
          tenantId: tenantId,
          slug: workspaceId,
        },
      },
    });

    if (!existingPage) {
      return NextResponse.json({ error: "Page not found" }, { status: 404 });
    }

    const updatedPage = await db.page.update({
      where: { id },
      data: {
        ...(name && { name }),
      },
    });

    return NextResponse.json({
      data: updatedPage,
      message: "Page updated successfully",
    });
  } catch (error) {
    console.error("Update page error:", error);
    return NextResponse.json(
      { error: "Failed to update page" },
      { status: 500 }
    );
  }
}

// Delete a page
export async function DELETE(request: NextRequest) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const tenantId = searchParams.get("tenantId");

    if (!id) {
      return NextResponse.json({ error: "Id is required" }, { status: 400 });
    }

    // Delete the page
    await db.integration.delete({
      where: { id },
    });

    return NextResponse.json({
      message: "Page deleted successfully",
    });
  } catch (error) {
    console.error("Delete page error:", error);
    return NextResponse.json(
      { error: "Failed to delete page" },
      { status: 500 }
    );
  }
}
