import { J<PERSON><PERSON> } from "jsdom";
import { Readability } from "@mozilla/readability";

// We're accepting the any type for jsdom since it's a third-party library
// @ts-ignore

interface ContentExtractionOptions {
  contentCleaningLevel?: "basic" | "medium" | "aggressive";
  fullContent?: boolean;
}

interface ContentExtractionResult {
  success: boolean;
  url?: string;
  title?: string;
  description?: string;
  content?: string;
  fullContent?: string;
  headings?: string[];
  images?: { src: string; alt: string }[];
  hasSitemap?: boolean;
  sitemapUrl?: string | null;
  contentCleaningApplied?: string;
  error?: string;
}

/**
 * Fetches content from a URL and extracts relevant information
 * @param url The URL to fetch
 * @param options Optional settings for content extraction
 */
export async function fetchUrlContent(
  url: string,
  options?: ContentExtractionOptions
): Promise<ContentExtractionResult> {
  try {
    // Validate URL
    const urlObj = new URL(url);

    // Check if robots.txt allows access
    const robotsUrl = `${urlObj.protocol}//${urlObj.hostname}/robots.txt`;
    let isAllowed = true;

    try {
      const robotsResponse = await fetch(robotsUrl);
      if (robotsResponse.ok) {
        const robotsText = await robotsResponse.text();
        // Simple check for "Disallow: /" or "Disallow: [path]" where path matches our URL path
        const disallowLines = robotsText
          .split("\n")
          .filter((line: string) => line.toLowerCase().startsWith("disallow:"))
          .map((line: string) => line.split(":", 2)[1].trim());

        // Check if the URL path is disallowed
        isAllowed = !disallowLines.some((disallowPath: string) => {
          if (disallowPath === "/") return true;
          return urlObj.pathname.startsWith(disallowPath);
        });
      }
    } catch (error) {
      console.warn("Error checking robots.txt, proceeding with fetch:", error);
    }

    if (!isAllowed) {
      return {
        success: false,
        error: "Access to this URL is restricted by robots.txt",
      };
    }

    // Fetch the URL content
    const response = await fetch(url, {
      headers: {
        "User-Agent": "Swiss Knowledge Hub URL Importer/1.0",
      },
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to fetch URL: ${response.statusText}`,
      };
    }

    const html = await response.text();

    // Use JSDOM and Readability for better content extraction
    const dom = new JSDOM(html, { url });
    const document = dom.window.document;

    // Extract title
    const titleElement = document.querySelector("title");
    const title = titleElement ? titleElement.textContent?.trim() : "";

    // Extract meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    const description = metaDescription
      ? metaDescription.getAttribute("content")?.trim()
      : "";

    // Use Readability to extract the main content
    const reader = new Readability(document);
    const article = reader.parse();

    if (!article) {
      // Fallback to traditional extraction if Readability fails
      return extractContentTraditionally(html, url, options);
    }

    // Extract headings
    const headings: string[] = [];
    document
      .querySelectorAll("h1, h2, h3, h4, h5, h6")
      .forEach((heading: Element) => {
        const headingText = heading.textContent?.trim();
        if (headingText) {
          headings.push(headingText);
        }
      });

    // No image extraction

    // Apply content cleaning based on level
    let articleText = article.textContent || "";

    if (options?.contentCleaningLevel) {
      const paragraphs = articleText
        .split("\n")
        .filter((p: string) => p.trim().length > 0);

      if (options.contentCleaningLevel === "medium") {
        // Remove very short paragraphs and those that might be navigation/footer content
        articleText = paragraphs
          .filter(
            (p: string) =>
              p.length > 50 &&
              !p.match(
                /copyright|all rights reserved|privacy policy|terms of service/i
              )
          )
          .join("\n\n");
      } else if (options.contentCleaningLevel === "aggressive") {
        // More aggressive cleaning - longer paragraphs only and filter out common non-content text
        articleText = paragraphs
          .filter(
            (p: string) =>
              p.length > 100 &&
              !p.match(
                /copyright|all rights reserved|privacy policy|terms of service|contact us|about us|sign up|log in/i
              )
          )
          .join("\n\n");
      }
    }

    // Use the HTML content directly from Readability
    const htmlContent = article.content;

    // Create a simple HTML wrapper with the title and description
    let fullHtmlContent = `<!DOCTYPE html>
<html>
<head>
  <title>${title || "Untitled"}</title>
  <meta name="description" content="${description || ""}">
</head>
<body>
  <h1>${title || "Untitled"}</h1>
  ${description ? `<p>${description}</p>` : ""}
  ${htmlContent}
</body>
</html>`;

    // No image extraction

    // Check for sitemap
    let hasSitemap = false;
    let sitemapUrl = `${urlObj.protocol}//${urlObj.hostname}/sitemap.xml`;

    try {
      const sitemapResponse = await fetch(sitemapUrl);
      hasSitemap = sitemapResponse.ok;
    } catch (error) {
      console.warn("Error checking sitemap:", error);
    }

    return {
      success: true,
      url,
      title: title || "Untitled",
      description: description || "",
      content: fullHtmlContent, // Return the HTML content instead of markdown
      fullContent: articleText,
      headings,
      images: [],
      hasSitemap,
      sitemapUrl: hasSitemap ? sitemapUrl : null,
      contentCleaningApplied: options?.contentCleaningLevel || "none",
    };
  } catch (error) {
    console.error("Error fetching URL content:", error);
    return {
      success: false,
      error: `Failed to process URL: ${error.message}`,
    };
  }
}

/**
 * Fallback method to extract content traditionally if Readability fails
 */
function extractContentTraditionally(
  html: string,
  url: string,
  options?: ContentExtractionOptions
): ContentExtractionResult {
  try {
    // Extract title
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    const title = titleMatch ? titleMatch[1].trim() : "Untitled";

    // Extract meta description
    const descriptionMatch =
      html.match(
        /<meta[^>]*name=["']description["'][^>]*content=["'](.*?)["'][^>]*>/i
      ) ||
      html.match(
        /<meta[^>]*content=["'](.*?)["'][^>]*name=["']description["'][^>]*>/i
      );
    const description = descriptionMatch ? descriptionMatch[1].trim() : "";

    // Extract main content (simplified approach)
    // Remove script and style tags
    let content = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, "");

    // Extract body content
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
      content = bodyMatch[1];
    }

    // Extract headings
    const headings: string[] = [];
    const headingMatches = content.matchAll(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi);
    for (const match of headingMatches) {
      const headingText = match[1].replace(/<[^>]*>/g, "").trim();
      if (headingText) {
        headings.push(headingText);
      }
    }

    // Extract paragraphs (simplified)
    const paragraphs: string[] = [];
    const paragraphMatches = content.matchAll(/<p[^>]*>(.*?)<\/p>/gi);
    for (const match of paragraphMatches) {
      const paragraphText = match[1].replace(/<[^>]*>/g, "").trim();
      if (paragraphText) {
        paragraphs.push(paragraphText);
      }
    }

    // Extract div content as well (many sites use divs instead of paragraphs)
    const divMatches = content.matchAll(/<div[^>]*>(.*?)<\/div>/gi);
    for (const match of divMatches) {
      const divText = match[1].replace(/<[^>]*>/g, "").trim();
      if (divText && divText.length > 50) {
        // Only include substantial div content
        paragraphs.push(divText);
      }
    }

    // No image extraction

    // Apply content cleaning based on level
    let cleanedParagraphs = [...paragraphs];
    if (options?.contentCleaningLevel) {
      if (options.contentCleaningLevel === "medium") {
        // Remove very short paragraphs and those that might be navigation/footer content
        cleanedParagraphs = paragraphs.filter(
          (p) =>
            p.length > 50 &&
            !p.match(
              /copyright|all rights reserved|privacy policy|terms of service/i
            )
        );
      } else if (options.contentCleaningLevel === "aggressive") {
        // More aggressive cleaning - longer paragraphs only and filter out common non-content text
        cleanedParagraphs = paragraphs.filter(
          (p) =>
            p.length > 100 &&
            !p.match(
              /copyright|all rights reserved|privacy policy|terms of service|contact us|about us|sign up|log in/i
            )
        );
      }
    }

    // Create HTML content
    const paragraphsHtml = options?.contentCleaningLevel
      ? cleanedParagraphs.map((p) => `<p>${p}</p>`).join("\n")
      : paragraphs.map((p) => `<p>${p}</p>`).join("\n");

    // Create a simple HTML wrapper with the title and description
    const fullHtmlContent = `<!DOCTYPE html>
<html>
<head>
  <title>${title}</title>
  <meta name="description" content="${description || ""}">
</head>
<body>
  <h1>${title}</h1>
  ${description ? `<p>${description}</p>` : ""}
  ${paragraphsHtml}
</body>
</html>`;

    // Store the plain text for fullContent
    const allText = options?.contentCleaningLevel
      ? cleanedParagraphs.join("\n\n")
      : paragraphs.join("\n\n");

    // No image extraction

    // Check for sitemap
    let hasSitemap = false;
    const urlObj = new URL(url);
    let sitemapUrl = `${urlObj.protocol}//${urlObj.hostname}/sitemap.xml`;

    return {
      success: true,
      url,
      title,
      description,
      content: fullHtmlContent, // Return the HTML content instead of markdown
      fullContent: allText,
      headings,
      images: [],
      hasSitemap,
      sitemapUrl: hasSitemap ? sitemapUrl : null,
      contentCleaningApplied: options?.contentCleaningLevel || "none",
    };
  } catch (error) {
    console.error("Error in traditional content extraction:", error);
    return {
      success: false,
      error: `Failed to extract content: ${error.message}`,
    };
  }
}
