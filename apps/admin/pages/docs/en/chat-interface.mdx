---
title: "Chat Interface"
description: "Learn how to use the AI-powered chat interface"
---

# Chat Interface

The AI-powered chat interface is the core feature of Swiss Knowledge Hub, allowing you to have natural conversations with your documents. This guide explains how to use the chat effectively.

## Starting a Chat

There are multiple ways to start a chat:

### From the Document View

1. Navigate to "My Hub"
2. Select a document from your list
3. Click the "Chat" button

### From the Dashboard

1. Go to the Dashboard
2. Click "New Chat" in the Quick Actions section
3. Select the document(s) you want to include in the chat

### From the Chat History

1. Navigate to "Chat History"
2. Click "New Chat"
3. Select document(s) to include

## Chat Interface Overview

The chat interface consists of several key components:

### Message Area

This is where your conversation with the AI appears:
- Your messages appear on the right
- AI responses appear on the left
- Document citations are highlighted and clickable

### Input Field

Located at the bottom of the screen:
- Type your questions or commands here
- Press Enter to send
- Use Shift+Enter for multi-line messages

### Document Context Panel

Located on the right side:
- Shows which documents are included in the chat
- Displays relevant document sections
- Allows adding or removing documents

### Chat Controls

Located at the top of the chat:
- Start a new chat
- Save the current chat
- Export the conversation
- Adjust AI settings

## Asking Effective Questions

To get the best results from the AI:

### Basic Questions

Simple, direct questions work well:
- "What is the main topic of this document?"
- "When was this policy implemented?"
- "Who is responsible for project approval?"

### Complex Queries

For more detailed information:
- "Compare the financial results from Q1 and Q2"
- "Summarize the key findings from the research"
- "Explain the procedure for handling customer complaints"

### Follow-up Questions

The AI remembers context, so you can ask follow-up questions:
- "Why was that decision made?"
- "Can you elaborate on that point?"
- "What were the alternatives?"

## Advanced Features

### Multi-document Chat

Chat with multiple documents simultaneously:
1. Start a new chat
2. Click "Add Documents" in the context panel
3. Select additional documents
4. The AI will now draw information from all selected documents

### Chat Commands

Use special commands for specific functions:
- `/summarize` - Generate a summary of the document
- `/extract [topic]` - Extract information about a specific topic
- `/compare` - Compare information across multiple documents
- `/clear` - Clear the current conversation

### Citation Verification

Verify the AI's responses:
1. Click on highlighted citations in the AI's response
2. The original document will open to the cited section
3. Review the source material to verify accuracy

## Saving and Managing Chats

### Saving Chats

Save important conversations for future reference:
1. Click "Save Chat" in the chat controls
2. Give the chat a descriptive name
3. Optionally add tags for organization

### Accessing Saved Chats

Find your saved conversations:
1. Navigate to "Chat History" in the main menu
2. Browse or search for specific chats
3. Click on a chat to resume the conversation

### Exporting Chats

Export conversations for sharing or documentation:
1. Open the chat you want to export
2. Click "Export" in the chat controls
3. Choose your preferred format (PDF, Markdown, or Text)
4. Save the exported file to your computer

## Chat Settings

Customize your chat experience:

### AI Model Selection

Choose the AI model that best suits your needs:
1. Click "Settings" in the chat controls
2. Select from available models (OpenAI, Azure OpenAI)
3. Choose the specific model version

### Response Settings

Adjust how the AI responds:
1. **Temperature**: Controls creativity (lower = more factual, higher = more creative)
2. **Context Length**: Adjust how much document context is included
3. **Response Length**: Set preferred response length (concise to detailed)

## Troubleshooting

### Common Issues

- **No Response**: Ensure your document is fully processed
- **Incorrect Information**: Check citations and consider reframing your question
- **Limited Context**: Try breaking complex questions into smaller parts
- **Slow Responses**: Large documents may take longer to process

### Getting Help

If you encounter issues:
1. Check the Troubleshooting guide
2. Contact support through the "Help" button
3. Provide details about your issue for faster resolution
