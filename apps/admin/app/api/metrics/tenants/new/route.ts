import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "6months"; // '30days', '6months', '12months', etc.

    // Get date range from query parameters or use period
    let fromDate = searchParams.get("from")
      ? new Date(searchParams.get("from") as string)
      : null;
    let toDate = searchParams.get("to")
      ? new Date(searchParams.get("to") as string)
      : new Date();

    // If no from date is provided, calculate it from the period
    if (!fromDate) {
      // Parse the period
      const match = period.match(/^(\d+)(days|months)$/);
      if (!match) {
        return NextResponse.json(
          {
            error:
              "Invalid period format. Use format like '30days' or '6months'",
          },
          { status: 400 }
        );
      }

      const amount = parseInt(match[1], 10);
      const unit = match[2];

      // Calculate the start date
      fromDate = new Date();
      if (unit === "days") {
        fromDate.setDate(fromDate.getDate() - amount);
      } else if (unit === "months") {
        fromDate.setMonth(fromDate.getMonth() - amount);
      }
    }

    // Get all tenants created in the period
    const tenants = await db.tenant.findMany({
      where: {
        createdAt: {
          gte: fromDate,
          lte: toDate,
        },
      },
      select: {
        id: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Group by month or day
    const groupedData = {};
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    tenants.forEach((tenant) => {
      const date = new Date(tenant.createdAt);

      if (unit === "days") {
        // Format as YYYY-MM-DD
        const day = date.toISOString().split("T")[0];
        if (!groupedData[day]) {
          groupedData[day] = 0;
        }
        groupedData[day]++;
      } else {
        // Format as Month YYYY (e.g., "Jan 2023")
        const monthYear = `${
          monthNames[date.getMonth()]
        } ${date.getFullYear()}`;
        if (!groupedData[monthYear]) {
          groupedData[monthYear] = 0;
        }
        groupedData[monthYear]++;
      }
    });

    // Convert to array format for charts
    let timeSeriesData = [];

    if (unit === "days") {
      // For days, we want to ensure all days in the range are represented
      const endDate = toDate;
      let currentDate = new Date(fromDate);

      while (currentDate <= endDate) {
        const day = currentDate.toISOString().split("T")[0];
        timeSeriesData.push({
          date: day,
          count: groupedData[day] || 0,
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }
    } else {
      // For months, convert the grouped data to array
      for (const [monthYear, count] of Object.entries(groupedData)) {
        timeSeriesData.push({
          month: monthYear,
          count,
        });
      }

      // Sort by date
      timeSeriesData.sort((a, b) => {
        const [aMonth, aYear] = a.month.split(" ");
        const [bMonth, bYear] = b.month.split(" ");

        if (aYear !== bYear) {
          return parseInt(aYear) - parseInt(bYear);
        }

        return monthNames.indexOf(aMonth) - monthNames.indexOf(bMonth);
      });
    }

    return NextResponse.json({
      period,
      totalNewTenants: tenants.length,
      timeSeriesData,
      dailyData: unit === "days" ? timeSeriesData : null,
      monthlyData: unit === "months" ? timeSeriesData : null,
    });
  } catch (error) {
    console.error("Error fetching new tenant metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch new tenant metrics" },
      { status: 500 }
    );
  }
}
