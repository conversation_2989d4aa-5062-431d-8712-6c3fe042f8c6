"use client";

import React from "react";
import { formatDistanceToNow } from "date-fns";
import {
  MessageCircle,
  Users,
  Globe,
  Lock,
  ExternalLink,
  Clock,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { SharedThread } from "@/services/src/shared-threads";
import { useLanguage } from "@/lib/language-context";

interface SharedThreadItemProps {
  thread: SharedThread;
  onClick?: () => void;
}

export const SharedThreadItem: React.FC<SharedThreadItemProps> = ({
  thread,
  onClick,
}) => {
  const { t } = useLanguage();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Navigate to shared thread
      window.open(`/shared/thread/${thread.shareToken}`, "_blank");
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { addSuffix: true });
    } catch {
      return "Unknown";
    }
  };

  const getThreadTitle = () => {
    if (thread.chat.title) {
      return thread.chat.title;
    }

    // Fallback to first message content (truncated)
    if (thread.chat.messages && thread.chat.messages.length > 0) {
      const firstMessage = thread.chat.messages[0];
      return (
        firstMessage.content.slice(0, 50) +
        (firstMessage.content.length > 50 ? "..." : "")
      );
    }

    return "Untitled Thread";
  };

  const getLastMessage = () => {
    if (thread.chat.messages && thread.chat.messages.length > 0) {
      const lastMessage = thread.chat.messages[0]; // API returns latest message first
      return {
        content:
          lastMessage.content.slice(0, 100) +
          (lastMessage.content.length > 100 ? "..." : ""),
        user: lastMessage.user,
        createdAt: lastMessage.createdAt,
      };
    }
    return null;
  };

  const lastMessage = getLastMessage();
  const isExpired = thread.expiresAt && new Date(thread.expiresAt) < new Date();

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md border-l-4",
        thread.hasNewActivity
          ? "border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/20"
          : "",
        isExpired && "opacity-60"
      )}
      onClick={handleClick}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                {thread.isPublic ? (
                  <Globe className="h-4 w-4 text-green-600" />
                ) : (
                  <Lock className="h-4 w-4 text-orange-600" />
                )}
                <h3 className="font-medium text-sm truncate">
                  {getThreadTitle()}
                </h3>
                {thread.hasNewActivity && (
                  <Badge variant="destructive" className="h-5 px-1.5 text-xs">
                    {thread.unreadCount || "New"}
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span
                  className={cn(
                    "px-2 py-1 rounded-full text-xs font-medium",
                    thread.isPublic
                      ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400"
                  )}
                >
                  {thread.isPublic ? "Public" : "Private"}
                </span>

                <div className="flex items-center gap-1">
                  <MessageCircle className="h-3 w-3" />
                  <span>{thread.chat._count.messages}</span>
                </div>

                {isExpired && (
                  <div className="flex items-center gap-1 text-red-600">
                    <Clock className="h-3 w-3" />
                    <span>Expired</span>
                  </div>
                )}
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                window.open(`/shared/thread/${thread.shareToken}`, "_blank");
              }}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>

          {/* Last Message */}
          {lastMessage && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={(lastMessage.user as any).image} />
                  <AvatarFallback className="text-xs">
                    {getInitials(lastMessage.user.name)}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs font-medium text-muted-foreground">
                  {lastMessage.user.name}
                </span>
                <span className="text-xs text-muted-foreground">
                  {formatDate(lastMessage.createdAt)}
                </span>
              </div>

              <p className="text-sm text-muted-foreground line-clamp-2">
                {lastMessage.content}
              </p>
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between pt-2 border-t border-border/50">
            <div className="flex items-center gap-2">
              <Avatar className="h-5 w-5">
                <AvatarImage src={thread.createdBy.image} />
                <AvatarFallback className="text-xs">
                  {getInitials(thread.createdBy.name)}
                </AvatarFallback>
              </Avatar>
              <span className="text-xs text-muted-foreground">
                Shared by {thread.createdBy.name}
              </span>
            </div>

            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>{thread.tenant.name}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
