import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import Stripe from "stripe";
import db from "@/lib/shared-db";

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2023-10-16" as any,
});

export async function GET(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all active plans
    const plans = await db.plan.findMany({
      where: {
        isActive: true,
      },
    });

    // Get all active storage tiers
    const storageTiers = await db.storageTier.findMany({
      where: {
        isActive: true,
      },
    });

    // Extract all price IDs for filtering
    const priceIds = [
      ...plans.flatMap((plan) => [
        plan.stripePriceId,
        plan.stripeYearlyPriceId,
        plan.stripeUserPriceId,
        plan.stripeUserYearlyPriceId,
      ]),
      ...storageTiers.flatMap((tier) => [
        tier.stripePriceId,
        tier.stripeYearlyPriceId,
      ]),
    ].filter(Boolean) as string[];

    console.log("Fetching prices from Stripe...");

    // Fetch all active prices from Stripe
    const { data: allPrices } = await stripe.prices.list({
      active: true,
      expand: ["data.product"],
      limit: 100, // Increase limit to get more prices
    });

    console.log(`Retrieved ${allPrices.length} prices from Stripe`);

    // Filter prices to only include the ones we need
    const prices = allPrices.filter((price) => priceIds.includes(price.id));

    console.log(
      `Found ${prices.length} prices out of ${priceIds.length} price IDs`
    );

    // Log any missing price IDs for debugging
    const foundPriceIds = prices.map((price) => price.id);
    const missingPriceIds = priceIds.filter(
      (id) => !foundPriceIds.includes(id)
    );

    if (missingPriceIds.length > 0) {
      console.log(`Missing price IDs: ${missingPriceIds.join(", ")}`);
    }

    // Create a map of price ID to price data
    const priceMap = prices.reduce(
      (acc, price: any) => {
        // Log each price for debugging

        acc[price.id] = {
          id: price.id,
          unitAmount: price.unit_amount,
          currency: price.currency,
          interval: price.recurring?.interval,
          productId:
            typeof price.product === "object"
              ? price.product.id
              : price.product,
          productName:
            typeof price.product === "object" ? price?.product?.name : null,
        };
        return acc;
      },
      {} as Record<string, any>
    );

    // Prepare the response data
    const planPrices = plans.map((plan) => ({
      planId: plan.id,
      planName: plan.name,
      planType: plan.type,
      includedUsers: plan.includedUsers,
      vectorStoreGB: plan.vectorStoreGB,
      displayPrice: plan.price,
      monthlyPrice: priceMap[plan.stripePriceId as string] || null,
      yearlyPrice: priceMap[plan.stripeYearlyPriceId as string] || null,
      userMonthlyPrice: priceMap[plan.stripeUserPriceId as string] || null,
      userYearlyPrice: priceMap[plan.stripeUserYearlyPriceId as string] || null,
    }));

    const storagePrices = storageTiers.map((tier) => ({
      tierId: tier.id,
      tierName: tier.name,
      sizeGB: tier.sizeGB,
      displayPrice: tier.price,
      monthlyPrice: priceMap[tier.stripePriceId] || null,
      yearlyPrice: priceMap[tier.stripeYearlyPriceId] || null,
    }));

    return NextResponse.json({
      plans: planPrices,
      storageTiers: storagePrices,
    });
  } catch (error) {
    console.error("Error fetching Stripe prices:", error);
    return NextResponse.json(
      { error: "Failed to fetch prices" },
      { status: 500 }
    );
  }
}
