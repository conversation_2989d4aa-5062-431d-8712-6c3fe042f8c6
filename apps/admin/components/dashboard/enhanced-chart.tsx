"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps,
  Legend,
  Bar,
  BarChart,
  Line,
  LineChart,
} from "recharts";
import { BarChart3, LineChart as LineChartIcon, TrendingUp } from "lucide-react";
import { useState } from "react";

interface EnhancedChartProps {
  className?: string;
  title: string;
  data: any[];
  dataKey: string;
  valueFormatter?: (value: number) => string;
  description?: string;
  color?: "indigo" | "blue" | "green" | "amber" | "red";
  chartType?: "area" | "bar" | "line";
  showLegend?: boolean;
  yAxisLabel?: string;
  xAxisLabel?: string;
}

const CustomTooltip = ({
  active,
  payload,
  label,
  valueFormatter,
}: TooltipProps<number, string> & {
  valueFormatter: (value: number) => string;
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-background p-2 shadow-sm">
        <p className="text-xs font-medium text-muted-foreground">{label}</p>
        <p className="text-sm font-bold">
          {valueFormatter(payload[0].value as number)}
        </p>
      </div>
    );
  }

  return null;
};

export function EnhancedChart({
  className,
  title,
  data,
  dataKey,
  valueFormatter = (value: number) => `${value}`,
  description,
  color = "indigo",
  chartType = "area",
  showLegend = false,
  yAxisLabel,
  xAxisLabel,
}: EnhancedChartProps) {
  const [selectedChartType, setSelectedChartType] = useState<"area" | "bar" | "line">(chartType);
  
  const colorMap = {
    indigo: {
      stroke: "#6366F1",
      fill: "#6366F1",
    },
    blue: {
      stroke: "#3B82F6",
      fill: "#3B82F6",
    },
    green: {
      stroke: "#10B981",
      fill: "#10B981",
    },
    amber: {
      stroke: "#F59E0B",
      fill: "#F59E0B",
    },
    red: {
      stroke: "#EF4444",
      fill: "#EF4444",
    },
  };

  const renderChart = () => {
    const commonProps = {
      data,
      margin: {
        top: 5,
        right: 5,
        left: 5,
        bottom: 5,
      },
    };

    switch (selectedChartType) {
      case "bar":
        return (
          <BarChart {...commonProps}>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#888"
              strokeOpacity={0.2}
            />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dy={10}
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5 } : undefined}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dx={-10}
              tickFormatter={valueFormatter}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload}
                  label={label}
                  valueFormatter={valueFormatter}
                />
              )}
            />
            {showLegend && <Legend />}
            <Bar
              dataKey={dataKey}
              fill={colorMap[color].fill}
              fillOpacity={0.8}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        );
      case "line":
        return (
          <LineChart {...commonProps}>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#888"
              strokeOpacity={0.2}
            />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dy={10}
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5 } : undefined}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dx={-10}
              tickFormatter={valueFormatter}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload}
                  label={label}
                  valueFormatter={valueFormatter}
                />
              )}
            />
            {showLegend && <Legend />}
            <Line
              type="monotone"
              dataKey={dataKey}
              stroke={colorMap[color].stroke}
              strokeWidth={2}
              dot={{ r: 4, fill: colorMap[color].stroke }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        );
      case "area":
      default:
        return (
          <AreaChart {...commonProps}>
            <defs>
              <linearGradient id={`color-${color}`} x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={colorMap[color].fill}
                  stopOpacity={0.2}
                />
                <stop
                  offset="95%"
                  stopColor={colorMap[color].fill}
                  stopOpacity={0}
                />
              </linearGradient>
            </defs>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#888"
              strokeOpacity={0.2}
            />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dy={10}
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5 } : undefined}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dx={-10}
              tickFormatter={valueFormatter}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload}
                  label={label}
                  valueFormatter={valueFormatter}
                />
              )}
            />
            {showLegend && <Legend />}
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={colorMap[color].stroke}
              strokeWidth={2}
              fill={`url(#color-${color})`}
              fillOpacity={1}
            />
          </AreaChart>
        );
    }
  };

  return (
    <Card className={cn("col-span-4 overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-base font-medium">{title}</CardTitle>
          {description && (
            <CardDescription className="text-xs">{description}</CardDescription>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex rounded-md border">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-8 w-8 rounded-none rounded-l-md",
                selectedChartType === "area" && "bg-muted"
              )}
              onClick={() => setSelectedChartType("area")}
            >
              <TrendingUp className="h-4 w-4" />
              <span className="sr-only">Area Chart</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-8 w-8 rounded-none",
                selectedChartType === "bar" && "bg-muted"
              )}
              onClick={() => setSelectedChartType("bar")}
            >
              <BarChart3 className="h-4 w-4" />
              <span className="sr-only">Bar Chart</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-8 w-8 rounded-none rounded-r-md",
                selectedChartType === "line" && "bg-muted"
              )}
              onClick={() => setSelectedChartType("line")}
            >
              <LineChartIcon className="h-4 w-4" />
              <span className="sr-only">Line Chart</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="h-[300px] pt-4">
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
