import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import { PlanUpgradeRequestSchema } from "@/scripts/seed-subscription-schema";
import { z } from "zod";

// Get subscriptions for a tenant
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const userId = searchParams.get("userId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // const token = await getToken({ req: request as any });
    // if (!token) {
    //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    // }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Get active subscription for the tenant
    const subscription = await db.subscription.findFirst({
      where: {
        tenantId,
        isActive: true,
      },
      include: {
        plan: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ subscription });
  } catch (error) {
    console.error("Error fetching subscription:", error);
    return NextResponse.json(
      { error: "Failed to fetch subscription" },
      { status: 500 }
    );
  }
}

// Create a new subscription
export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate request body against schema
    let validatedData;
    try {
      validatedData = PlanUpgradeRequestSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: "Validation error",
            details: error.errors,
          },
          { status: 400 }
        );
      }
      throw error;
    }

    const { tenantId, planId, additionalUsers, additionalStorageGB } =
      validatedData;

    // Validate required fields
    if (!tenantId || !planId) {
      return NextResponse.json(
        { error: "Tenant ID and Plan ID are required" },
        { status: 400 }
      );
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Check if plan exists and is active
    const plan = await db.plan.findFirst({
      where: {
        id: planId,
        isActive: true,
      },
    });

    if (!plan) {
      return NextResponse.json(
        { error: "Selected plan not found or inactive" },
        { status: 404 }
      );
    }

    // Deactivate any existing active subscriptions
    await db.subscription.updateMany({
      where: {
        tenantId,
        isActive: true,
      },
      data: {
        isActive: false,
        endDate: new Date(),
      },
    });

    // Create new subscription
    const subscription = await db.subscription.create({
      data: {
        tenantId,
        planId,
        additionalUsers: additionalUsers || 0,
        additionalStorageGB: additionalStorageGB || 0,
        isActive: true,
        startDate: new Date(),
      },
      include: {
        plan: true,
      },
    });

    return NextResponse.json({ subscription }, { status: 201 });
  } catch (error) {
    console.error("Error creating subscription:", error);
    return NextResponse.json(
      { error: "Failed to create subscription" },
      { status: 500 }
    );
  }
}
