import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { getUserPermissions } from "@/lib/permissions";

/**
 * GET /api/permissions/user
 * Get all permissions for the current user
 */
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const userIdFromParams = url.searchParams.get("userId");
    const tenantId = url.searchParams.get("tenantId");

    // Get the user from the session for client-side calls
    const token = await getToken({ req: request as any });

    // Use userId from params if token is null (server-side call)
    // or use token.sub if available (client-side call)
    const userId = userIdFromParams || token?.sub;

    if (!userId) {
      return NextResponse.json(
        {
          error:
            "Unauthorized - userId is required either in token or as a query parameter",
        },
        { status: 401 }
      );
    }

    if (!tenantId) {
      return NextResponse.json(
        { error: "TenantId is required" },
        { status: 400 }
      );
    }

    // Get all permissions for the user
    const permissions = await getUserPermissions(userId, tenantId);

    return NextResponse.json({ permissions });
  } catch (error: any) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch user permissions", details: error.message },
      { status: 500 }
    );
  }
}
