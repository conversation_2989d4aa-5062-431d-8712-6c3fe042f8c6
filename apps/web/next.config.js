/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: true,
  swcMinify: true,
  experimental: {
    turbo: {
      resolveAlias: {
        canvas: './empty-module.ts',
      },
    },
  },

  images: {
    domains: ['devskhstorage.blob.core.windows.net', "skhdocumentstorageprod.blob.core.windows.net"],
  },
  async rewrites() {
    return {
      beforeFiles: [
        {
          source: '/terms',
          destination: `https://www.swissknowledgehub.ch/agb`,
        }
      ],
      afterFiles: [
        // These rewrites are checked after pages/public files
        // are checked but before dynamic routes
      ],
      fallback: [],
    }
  },
};


const withNextra = require('nextra')({
  theme: 'nextra-theme-docs',
  themeConfig: './theme.config.tsx',
});

module.exports = withNextra(nextConfig);
