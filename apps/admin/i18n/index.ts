import { cookies } from 'next/headers';
import enTranslations from './locales/en.json';
import deTranslations from './locales/de.json';

const translations = {
  en: enTranslations,
  de: deTranslations,
};

export type Translations = typeof enTranslations;

export async function getTranslations() {
  const cookieStore = cookies();
  const localeCookie = cookieStore.get('NEXT_LOCALE');
  const locale = localeCookie?.value || 'en';
  
  const t = (key: string, params?: Record<string, string>) => {
    const keys = key.split('.');
    let value: any = translations[locale as keyof typeof translations];
    
    for (const k of keys) {
      if (!value) return key;
      value = value[k];
    }
    
    if (typeof value !== 'string') return key;
    
    if (params) {
      return Object.entries(params).reduce((acc, [paramKey, paramValue]) => {
        return acc.replace(new RegExp(`{${paramKey}}`, 'g'), paramValue);
      }, value);
    }
    
    return value;
  };
  
  return { t, locale };
}
