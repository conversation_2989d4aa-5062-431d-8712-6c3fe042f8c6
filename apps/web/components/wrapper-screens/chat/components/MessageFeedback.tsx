import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ThumbsUp, ThumbsDown, RefreshCw } from "lucide-react";
import { Message } from '../types';
import { useLanguage } from "@/lib/language-context";
import { toast } from 'react-hot-toast';

interface MessageFeedbackProps {
  displayedMessage: Message;
  index: number;
  processedMessages: Message[];
  handleFeedback: (messageIndex: number, feedback: "like" | "dislike") => void;
  handleRegenerate: () => void;
}

export const MessageFeedback: React.FC<MessageFeedbackProps> = ({
  displayedMessage,
  index,
  processedMessages,
  handleFeedback,
  handleRegenerate,
}) => {
  const { t } = useLanguage();

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="icon"
        className={`h-7 w-7 rounded-full ${
          displayedMessage.metadata?.feedback === "like"
            ? "bg-green-100 text-green-600"
            : ""
        }`}
        onClick={() => handleFeedback(index, "like")}
        title={t("chat.like")}
      >
        <ThumbsUp className="h-3.5 w-3.5" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className={`h-7 w-7 rounded-full ${
          displayedMessage.metadata?.feedback === "dislike"
            ? "bg-red-100 text-red-600"
            : ""
        }`}
        onClick={() => handleFeedback(index, "dislike")}
        title={t("chat.dislike")}
      >
        <ThumbsDown className="h-3.5 w-3.5" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7 rounded-full"
        onClick={() => {
          // Find the last assistant message
          let lastAssistantIndex = processedMessages.length - 1;
          while (
            lastAssistantIndex >= 0 &&
            processedMessages[lastAssistantIndex].role !== "assistant"
          ) {
            lastAssistantIndex--;
          }

          // Only regenerate if this is the last assistant message
          if (lastAssistantIndex === index) {
            handleRegenerate();
          } else {
            toast(t("chat.regenerateLastOnly"));
          }
        }}
        title={t("chat.regenerate")}
      >
        <RefreshCw className="h-3.5 w-3.5" />
      </Button>
    </div>
  );
};
