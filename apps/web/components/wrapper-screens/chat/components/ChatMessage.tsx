import React from "react";
import ReactMarkdown from "react-markdown";
import { Message, Source } from "../types";
import { useLanguage } from "@/lib/language-context";
import { MessageCarousel } from "./MessageCarousel";
import { MessageFeedback } from "./MessageFeedback";
import { MessageCitations } from "./MessageCitations";
import { ImagePreview } from "./ImagePreview";
import { Button } from "@/components/ui/button";
import { Copy, MessageCircle } from "lucide-react";
import { toast } from "react-hot-toast";

interface ChatMessageProps {
  message: Message;
  index: number;
  processedMessages: Message[];
  displayIndicesRef: React.MutableRefObject<Record<string, number>>;
  isLoading: boolean;
  updateDisplayIndex: (
    messageId: string,
    newIndex: number,
    isManuallySet: boolean
  ) => void;
  handleFeedback: (messageIndex: number, feedback: "like" | "dislike") => void;
  handleRegenerate: () => void;
  toggleCitationAccordion: (messageId: string) => void;
  setSelectedSource: (source: Source) => void;
  setIsCitationModalOpen: (isOpen: boolean) => void;
  onCommentClick?: () => void; // Optional comment handler
  isSharedView?: boolean; // Whether this is in a shared thread view
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  index,
  processedMessages,
  displayIndicesRef,
  isLoading,
  updateDisplayIndex,
  handleFeedback,
  handleRegenerate,
  toggleCitationAccordion,
  setSelectedSource,
  setIsCitationModalOpen,
  onCommentClick,
  isSharedView = false,
}) => {
  const { t } = useLanguage();

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t("chat.copiedToClipboard") || "Copied to clipboard");
  };

  const hasRegeneratedVersions =
    message.regeneratedMessages && message.regeneratedMessages.length > 0;

  let displayedMessage = message;
  let isRegeneratedVersion = false;
  let versionLabel = "";

  const displayIndex = message.id
    ? displayIndicesRef.current[message.id] || 0
    : 0;

  if (
    hasRegeneratedVersions &&
    displayIndex > 0 &&
    message.regeneratedMessages &&
    message.regeneratedMessages[displayIndex - 1]
  ) {
    // Show the selected regenerated message
    displayedMessage = message.regeneratedMessages[displayIndex - 1];
    isRegeneratedVersion = true;

    // Get the generation level of the displayed message
    const generationLevel = displayedMessage.metadata?.generationLevel || 1;

    // Create a more descriptive label based on generation level
    if (generationLevel === 1) {
      versionLabel = `${t("chat.regeneratedResponse")} ${displayIndex}/${
        message.regeneratedMessages.length
      }`;
    } else {
      versionLabel = `${t(
        "chat.regeneratedResponse"
      )} (Gen ${generationLevel}) ${displayIndex}/${
        message.regeneratedMessages.length
      }`;
    }
  } else if (hasRegeneratedVersions && message.regeneratedMessages) {
    // Show the original message
    versionLabel = `${t("chat.originalResponse")} (1/${
      message.regeneratedMessages.length + 1
    })`;
  }

  // Calculate total versions for the carousel indicator
  const totalVersions = hasRegeneratedVersions
    ? message.regeneratedMessages!.length + 1
    : 1;

  // Current version index is simply our display index
  const currentVersionIndex = displayIndex;

  return (
    <div
      data-role={message?.role}
      className={`max-w-[100%] px-4 text-xs sm:text-sm data-[role=assistant]:self-start data-[role=user]:self-end data-[role=user]:bg-secondary data-[role=user]:rounded-md data-[role=user]:max-w-[85%] sm:data-[role=user]:max-w-[80%]`}
    >
      <div className="relative">
        {hasRegeneratedVersions && (
          <div
            className={`text-xs mb-1 font-medium flex items-center gap-1 ${
              isRegeneratedVersion ? "text-green-600" : "text-amber-600"
            }`}
          >
            <span>{versionLabel}</span>

            {/* Streaming indicator */}
            {message.metadata?.isStreaming && (
              <span className="ml-2 flex items-center text-blue-500 animate-pulse">
                <span className="mr-1">Streaming</span>
                <span className="inline-block h-2 w-2 rounded-full bg-blue-500 mr-0.5 animate-ping"></span>
                <span
                  className="inline-block h-2 w-2 rounded-full bg-blue-500 mr-0.5 animate-ping"
                  style={{ animationDelay: "0.2s" }}
                ></span>
                <span
                  className="inline-block h-2 w-2 rounded-full bg-blue-500 animate-ping"
                  style={{ animationDelay: "0.4s" }}
                ></span>
              </span>
            )}
          </div>
        )}

        {displayedMessage.role === "user" &&
          displayedMessage.images &&
          displayedMessage.images.length > 0 && (
            <div className="my-3">
              <ImagePreview images={displayedMessage.images} />
            </div>
          )}

        <ReactMarkdown className="markdown">
          {displayedMessage?.content?.replace(/\\n/g, "\n")}
        </ReactMarkdown>

        {/* Document Citations */}
        {displayedMessage.role === "assistant" &&
          displayedMessage.sources &&
          displayedMessage.sources.length > 0 && (
            <MessageCitations
              displayedMessage={displayedMessage}
              index={index}
              toggleCitationAccordion={toggleCitationAccordion}
              setSelectedSource={setSelectedSource}
              setIsCitationModalOpen={setIsCitationModalOpen}
            />
          )}

        {/* Message controls container */}
        {displayedMessage.role === "assistant" && !isLoading && (
          <div className="flex items-center gap-2 mt-2 justify-between">
            {/* Left side - Navigation controls */}
            <div>
              {hasRegeneratedVersions && (
                <MessageCarousel
                  message={message}
                  currentVersionIndex={currentVersionIndex}
                  totalVersions={totalVersions}
                  updateDisplayIndex={updateDisplayIndex}
                />
              )}
            </div>

            {/* Right side - Copy, Comment, and Feedback buttons */}
            <div className="flex items-center gap-1">
              {/* Copy button */}
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 rounded-full"
                onClick={() => copyToClipboard(displayedMessage.content)}
                title={t("chat.copyMessage") || "Copy message"}
              >
                <Copy className="h-3.5 w-3.5" />
              </Button>

              {/* Comment button */}
              {onCommentClick && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full"
                  onClick={onCommentClick}
                  title="Add comment"
                >
                  <MessageCircle className="h-3.5 w-3.5" />
                </Button>
              )}

              {/* Feedback buttons - only show in regular chat, not shared view */}
              {!isSharedView && (
                <MessageFeedback
                  displayedMessage={displayedMessage}
                  index={index}
                  processedMessages={processedMessages}
                  handleFeedback={handleFeedback}
                  handleRegenerate={handleRegenerate}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
