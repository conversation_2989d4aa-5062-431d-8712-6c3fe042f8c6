"""
Notification broadcasting endpoints for real-time WebSocket notifications
"""
import logging
from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.websocket import (
    broadcast_mention_notification,
    broadcast_comment_reply_notification,
    broadcast_thread_shared_notification,
    broadcast_thread_update
)

logger = logging.getLogger(__name__)

router = APIRouter()

class NotificationData(BaseModel):
    type: str
    title: str
    content: str
    userId: str
    triggeredBy: str
    chatId: Optional[str] = None
    messageId: Optional[str] = None
    commentId: Optional[str] = None
    tenantId: str
    metadata: Optional[Dict[str, Any]] = None

class ThreadUpdateData(BaseModel):
    type: str
    chatId: str
    tenantId: str
    shareToken: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class BroadcastRequest(BaseModel):
    type: str  # "notification" or "thread_update"
    data: Dict[str, Any]

@router.post("/broadcast")
async def broadcast_notification_endpoint(request: BroadcastRequest):
    """
    Broadcast notifications or thread updates via WebSocket
    """
    try:
        if request.type == "notification":
            # Parse notification data
            notification_data = NotificationData(**request.data)
            
            # Broadcast based on notification type
            if notification_data.type == "MENTION":
                await broadcast_mention_notification(notification_data.dict())
            elif notification_data.type == "COMMENT_REPLY":
                await broadcast_comment_reply_notification(notification_data.dict())
            elif notification_data.type == "THREAD_SHARED":
                await broadcast_thread_shared_notification(notification_data.dict())
            else:
                logger.warning(f"Unknown notification type: {notification_data.type}")
                
        elif request.type == "thread_update":
            # Parse thread update data
            thread_data = ThreadUpdateData(**request.data)
            await broadcast_thread_update(thread_data.dict())
            
        else:
            raise HTTPException(status_code=400, detail=f"Unknown broadcast type: {request.type}")
            
        return {"success": True, "message": "Notification broadcasted successfully"}
        
    except Exception as e:
        logger.error(f"Error broadcasting notification: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to broadcast notification: {str(e)}")

@router.get("/health")
async def notification_health_check():
    """
    Health check endpoint for notification service
    """
    return {"status": "healthy", "service": "notification_broadcaster"}
