import { Client } from "@microsoft/microsoft-graph-client";
import db from "@/lib/shared-db";
import axios from "axios";

/**
 * Gets an authenticated Microsoft Graph client for the given tenant
 * @param tenantId The ID of the tenant to get the client for
 * @returns An authenticated Microsoft Graph client
 */
export async function getAuthenticatedClient(
  tenantId: string,
  userId: string
): Promise<Client> {
  try {
    // Get Microsoft credentials from database
    const integration = await db.integration.findFirst({
      where: {
        tenantId,
        platform: "OUTLOOK",
        userId,
      },
    });

    if (!integration) {
      throw new Error("Outlook integration not found");
    }
    const response = await axios.get(
      `${process.env.NEXTAUTH_URL}/api/integration/outlook/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`
    );

    const client = Client.init({
      authProvider: (done) =>
        done(null, response?.data?.integration?.accessToken),
    });

    return client;
  } catch (error) {
    console.error("Error getting Microsoft Graph client:", error);
    throw error;
  }
}
