"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DateRangeSelector } from "@/components/metrics/date-range-selector";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter } from "next/navigation";
import { Download } from "lucide-react";
import {
  MetricsProvider,
  useMetrics,
  dateRangeToParams,
} from "@/components/metrics/metrics-context";
import { formatDateRangeForFileName } from "@/lib/export-utils";
import { LoadingOverlay } from "@/components/ui/loading-spinner";
import { useToast } from "@/components/ui/use-toast";
import { Toaster } from "@/components/ui/toaster";

function MetricsLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const { dateRange, setDateRange, isLoading, setIsLoading } = useMetrics();
  const { toast } = useToast();

  // Get the current metrics section
  const currentSection = pathname?.split("/").pop();

  // Handle tab change
  const handleTabChange = (value: string) => {
    router.push(`/metrics/${value}`);
  };

  // Handle date range change
  const handleDateRangeChange = (
    range: { from: Date; to: Date } | undefined
  ) => {
    if (range) {
      setDateRange(range);
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      setIsLoading(true);

      // Create date range params
      const dateParams = dateRangeToParams(dateRange);

      // Determine which metrics to export based on the current section
      const section = currentSection || "tenants";

      // Fetch the data for the current section
      let endpoint = "";
      let fileName = "";

      switch (section) {
        case "tenants":
          endpoint = `/api/metrics/export/tenants?${dateParams}`;
          fileName = `tenant_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
        case "users":
          endpoint = `/api/metrics/export/users?${dateParams}`;
          fileName = `user_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
        case "finance":
          endpoint = `/api/metrics/export/finance?${dateParams}`;
          fileName = `finance_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
        case "system":
          endpoint = `/api/metrics/export/system?${dateParams}`;
          fileName = `system_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
      }

      // Fetch the data
      const response = await fetch(endpoint);

      if (response.ok) {
        // Get the CSV content
        const csvContent = await response.text();

        // Create a blob with the CSV content
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });

        // Create a download link
        const link = document.createElement("a");

        // Create a URL for the blob
        const url = URL.createObjectURL(blob);

        // Set link properties
        link.setAttribute("href", url);
        link.setAttribute("download", fileName);

        // Add link to the document
        document.body.appendChild(link);

        // Click the link to download the file
        link.click();

        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Show success toast
        toast({
          title: "Export successful",
          description: `${fileName} has been downloaded.`,
        });
      } else {
        console.error("Failed to export metrics data");

        // Show error toast
        toast({
          title: "Export failed",
          description: "There was an error exporting the metrics data.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error exporting metrics data:", error);

      // Show error toast
      toast({
        title: "Export failed",
        description: "There was an error exporting the metrics data.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {isLoading && <LoadingOverlay />}
      <Toaster />

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Metrics</h1>
          <p className="text-muted-foreground">
            Detailed metrics and analytics for your SaaS platform
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:items-center md:space-x-2 md:space-y-0">
          <DateRangeSelector onChange={handleDateRangeChange} />
          <Button
            variant="outline"
            size="icon"
            onClick={handleExport}
            disabled={isLoading}
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs
        defaultValue={currentSection || "tenants"}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="tenants">Tenants</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="finance">Finance</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>
      </Tabs>

      {children}
    </div>
  );
}

export default function MetricsLayoutWithProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MetricsProvider>
      <MetricsLayout>{children}</MetricsLayout>
    </MetricsProvider>
  );
}
