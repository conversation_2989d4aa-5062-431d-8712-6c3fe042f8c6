"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  X,
  Send,
  MessageCircle,
  CheckCircle,
  MoreVertical,
  AtSign,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { SimpleMentionInput } from "./SimpleMentionInput";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLanguage } from "@/lib/language-context";
import toast from "react-hot-toast";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface Comment {
  id: string;
  parentId?: string;
  content: string;
  author: User;
  status: "ACTIVE" | "RESOLVED" | "DELETED";
  mentions?: string[];
  createdAt: string;
  updatedAt: string;
  replies?: Comment[];
}

interface CommentSidebarProps {
  messageId: string;
  chatId: string;
  currentUser: User;
  onClose: () => void;
}

export const CommentSidebar: React.FC<CommentSidebarProps> = ({
  messageId,
  chatId,
  currentUser,
  onClose,
}) => {
  const { t } = useLanguage();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [commentMentions, setCommentMentions] = useState<string[]>([]);
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showResolved, setShowResolved] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Load comments
  useEffect(() => {
    loadComments();
  }, [messageId, showResolved]);

  const loadComments = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/chat/${chatId}/messages/${messageId}/comments?includeResolved=${showResolved}`
      );

      if (response.ok) {
        const data = await response.json();
        setComments(data.comments);
      }
    } catch (error) {
      console.error("Error loading comments:", error);
      toast.error("Failed to load comments");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    console.log("Submitting comment with mentions:", commentMentions);

    try {
      setSubmitting(true);
      const response = await fetch(
        `/api/chat/${chatId}/messages/${messageId}/comments`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: newComment.trim(),
            parentId: replyTo,
            mentions: commentMentions,
          }),
        }
      );

      if (response.ok) {
        setNewComment("");
        setCommentMentions([]);
        setReplyTo(null);
        await loadComments();
        toast.success("Comment added");
      } else {
        toast.error("Failed to add comment");
      }
    } catch (error) {
      console.error("Error submitting comment:", error);
      toast.error("Failed to add comment");
    } finally {
      setSubmitting(false);
    }
  };

  const handleResolveComment = async (commentId: string, resolve: boolean) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: resolve ? "RESOLVED" : "ACTIVE",
        }),
      });

      if (response.ok) {
        await loadComments();
        toast.success(resolve ? "Comment resolved" : "Comment reopened");
      } else {
        toast.error("Failed to update comment");
      }
    } catch (error) {
      console.error("Error updating comment:", error);
      toast.error("Failed to update comment");
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        await loadComments();
        toast.success("Comment deleted");
      } else {
        toast.error("Failed to delete comment");
      }
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast.error("Failed to delete comment");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const renderCommentContent = (content: string) => {
    // Convert @Name mentions to styled spans
    const parts = content.split(/(@\w+)/g);

    return parts.map((part, index) => {
      const mentionMatch = part.match(/@(\w+)/);
      if (mentionMatch) {
        const [, name] = mentionMatch;
        return (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 mx-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-sm hover:shadow-md transition-shadow cursor-pointer"
            title={`Mentioned ${name}`}
          >
            <AtSign className="h-3 w-3 mr-1" />
            {name}
          </span>
        );
      }
      return part;
    });
  };

  const activeComments = comments.filter((c) => c.status === "ACTIVE");
  const resolvedComments = comments.filter((c) => c.status === "RESOLVED");

  return (
    <div
      style={{
        zIndex: 10000,
      }}
      className="fixed right-0 top-0 h-full w-96 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 shadow-lg z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-primary" />
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
            Comments
          </h3>
          <Badge variant="secondary">{activeComments.length}</Badge>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Comments List */}
      <ScrollArea className="flex-1 h-[calc(100vh-200px)]">
        <div className="p-4 space-y-4">
          {loading ? (
            <div className="text-center text-gray-500 dark:text-gray-400">
              Loading comments...
            </div>
          ) : (
            <>
              {/* Active Comments */}
              {activeComments.map((comment) => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  currentUser={currentUser}
                  onReply={(commentId) => setReplyTo(commentId)}
                  onResolve={() => handleResolveComment(comment.id, true)}
                  onDelete={() => handleDeleteComment(comment.id)}
                  formatDate={formatDate}
                  getInitials={getInitials}
                  renderCommentContent={renderCommentContent}
                />
              ))}

              {/* Resolved Comments Toggle */}
              {resolvedComments.length > 0 && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowResolved(!showResolved)}
                    className="w-full justify-start"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {showResolved ? "Hide" : "Show"} resolved (
                    {resolvedComments.length})
                  </Button>

                  {showResolved && (
                    <div className="mt-2 space-y-2">
                      {resolvedComments.map((comment) => (
                        <CommentItem
                          key={comment.id}
                          comment={comment}
                          currentUser={currentUser}
                          onReply={(commentId) => setReplyTo(commentId)}
                          onResolve={() =>
                            handleResolveComment(comment.id, false)
                          }
                          onDelete={() => handleDeleteComment(comment.id)}
                          formatDate={formatDate}
                          getInitials={getInitials}
                          renderCommentContent={renderCommentContent}
                          isResolved
                        />
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeComments.length === 0 && resolvedComments.length === 0 && (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  No comments yet. Be the first to comment!
                </div>
              )}
            </>
          )}
        </div>
      </ScrollArea>

      {/* Comment Input */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        {replyTo && (
          <div className="mb-2 bg-gray-50 dark:bg-gray-800 rounded text-xs text-gray-600 dark:text-gray-400">
            <div className="flex items-center justify-between">
              <span>
                💬 Replying to{" "}
                {(() => {
                  // Find the comment in active comments, resolved comments, or their replies
                  const allComments = [...activeComments, ...resolvedComments];
                  const targetComment =
                    allComments.find((c) => c.id === replyTo) ||
                    allComments
                      .flatMap((c) => c.replies || [])
                      .find((r) => r.id === replyTo);
                  return targetComment?.author.name || "comment";
                })()}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setReplyTo(null)}
                className="h-auto p-1 text-xs hover:bg-gray-200 dark:hover:bg-gray-700"
              >
                ✕
              </Button>
            </div>
          </div>
        )}

        <div className="flex items-center gap-2 w-full">
          <SimpleMentionInput
            value={newComment}
            onChange={(value, mentions) => {
              setNewComment(value);
              setCommentMentions(mentions);
            }}
            placeholder="Add a comment... (use @ to mention users)"
            className="flex-1 min-h-[80px] resize-none w-full"
            onKeyDown={(e) => {
              if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                handleSubmitComment();
              }
            }}
          />
          <Button
            onClick={handleSubmitComment}
            disabled={!newComment.trim() || submitting}
            size="sm"
            className="self-end"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Press Cmd+Enter to send
        </div>
      </div>
    </div>
  );
};

// Comment Item Component
interface CommentItemProps {
  comment: Comment;
  currentUser: User;
  onReply: (commentId: string) => void; // Pass the comment ID to reply to
  onResolve: () => void;
  onDelete: () => void;
  formatDate: (date: string) => string;
  getInitials: (name: string) => string;
  renderCommentContent?: any;
  isResolved?: boolean;
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  currentUser,
  onReply,
  onResolve,
  onDelete,
  formatDate,
  getInitials,
  renderCommentContent,
  isResolved = false,
}) => {
  const isAuthor = comment.author.id === currentUser.id;

  return (
    <div className={`space-y-2 ${isResolved ? "opacity-60" : ""}`}>
      <div className="flex items-start gap-2">
        <Avatar className="h-6 w-6">
          <AvatarImage src={comment.author.image} />
          <AvatarFallback className="text-xs">
            {getInitials(comment.author.name)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {comment.author.name}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {formatDate(comment.createdAt)}
            </span>
            {isResolved && (
              <Badge variant="secondary" className="text-xs">
                Resolved
              </Badge>
            )}
          </div>

          <div className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
            {renderCommentContent(comment.content)}
          </div>

          <div className="flex items-center gap-2 mt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onReply(comment.parentId || comment.id)}
              className="text-xs h-6"
            >
              Reply
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onResolve}
              className="text-xs h-6"
            >
              {isResolved ? "Reopen" : "Resolve"}
            </Button>

            {isAuthor && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={onDelete} className="text-red-600">
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>

      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="ml-8 space-y-2">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              currentUser={currentUser}
              onReply={onReply} // This will now correctly handle replies to replies
              onResolve={() => {}}
              onDelete={() => onDelete()}
              formatDate={formatDate}
              getInitials={getInitials}
              renderCommentContent={renderCommentContent}
            />
          ))}
        </div>
      )}
    </div>
  );
};
