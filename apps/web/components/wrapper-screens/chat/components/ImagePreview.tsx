"use client";

import React, { useState } from "react";
import { ImageAttachment } from "../types";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download, ExternalLink, ZoomIn, FileImage, Eye } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface ImagePreviewProps {
  images: ImageAttachment[];
  className?: string;
  maxDisplayImages?: number;
}

export const ImagePreview: React.FC<ImagePreviewProps> = ({
  images,
  className = "",
  maxDisplayImages = 4,
}) => {
  const { t } = useLanguage();
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(
    null
  );

  if (!images || images.length === 0) {
    return null;
  }

  const displayImages = images.slice(0, maxDisplayImages);
  const remainingCount = images.length - maxDisplayImages;

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index);
  };

  const handleDownload = async (image: ImageAttachment) => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = image.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={cn("space-y-3", className)}>
      {/* Enhanced Image Grid */}
      <div className="grid grid-cols-2 gap-3 max-w-md">
        {displayImages.map((image, index) => (
          <div
            key={image.id}
            className={cn(
              "relative group cursor-pointer bg-gradient-to-br from-muted/50 to-muted",
              "rounded-xl overflow-hidden border border-border/50",
              "hover:border-primary/50 transition-all duration-200 hover:shadow-lg",
              "hover:scale-[1.02] active:scale-[0.98]"
            )}
          >
            <Dialog>
              <DialogTrigger asChild>
                <div
                  className="relative aspect-square"
                  onClick={() => handleImageClick(index)}
                >
                  <Image
                    src={image.url || image.preview || ""}
                    alt={image.name}
                    width={80}
                    height={80}
                    className="object-cover transition-transform duration-200 group-hover:scale-105"
                    loading="lazy"
                  />

                  {/* Enhanced Hover overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20 opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 transform scale-75 group-hover:scale-100 transition-transform duration-200">
                      <Eye className="h-5 w-5 text-gray-700" />
                    </div>
                  </div>

                  {/* Enhanced remaining count overlay */}
                  {index === maxDisplayImages - 1 && remainingCount > 0 && (
                    <div className="absolute inset-0 bg-gradient-to-br from-black/80 to-black/60 flex items-center justify-center backdrop-blur-sm">
                      <div className="text-center">
                        <div className="bg-white/20 rounded-full p-3 mx-auto mb-2">
                          <FileImage className="h-6 w-6 text-white" />
                        </div>
                        <span className="text-white font-bold text-xl">
                          +{remainingCount}
                        </span>
                        <p className="text-white/80 text-sm mt-1">
                          {t("chat.moreImages") || "more images"}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Image info badge */}
                  <div className="absolute top-2 left-2  backdrop-blur-sm  text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {formatFileSize(image.size)}
                  </div>
                </div>
              </DialogTrigger>

              <DialogContent className="max-w-5xl max-h-[95vh] p-0  border-none">
                <div className="relative flex flex-col h-full">
                  {/* Enhanced Full size image */}
                  <div className="flex-1 flex items-center justify-center p-4">
                    <Image
                      src={image.url || image.preview || ""}
                      alt={image.name}
                      width={320}
                      height={320}
                      className="object-cover transition-transform duration-200 group-hover:scale-105"
                    />
                  </div>

                  {/* Enhanced Image info and actions */}
                  <div className=" p-6">
                    <div className="flex items-center justify-between ">
                      <div className="space-y-2 flex-1 mr-4">
                        <div className="flex items-center gap-2">
                          <FileImage className="h-5 w-5 " />
                          <p className="font-semibold text-lg truncate">
                            {image.name}
                          </p>
                        </div>
                        <div className="flex items-center gap-4 text-sm ">
                          <span className="px-2 py-1 rounded-full">
                            {formatFileSize(image.size)}
                          </span>
                          <span className="px-2 py-1 rounded-full">
                            {image.type.split("/")[1]?.toUpperCase()}
                          </span>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => handleDownload(image)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {t("chat.download") || "Download"}
                        </Button>

                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => window.open(image.url, "_blank")}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          {t("chat.openInNewTab") || "Open"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        ))}
      </div>

      {/* Image list for screen readers */}
      <div className="sr-only">
        <p>{t("chat.imagesAttached") || "Images attached"}:</p>
        <ul>
          {images.map((image) => (
            <li key={image.id}>
              {image.name} ({formatFileSize(image.size)})
            </li>
          ))}
        </ul>
      </div>

      {/* Enhanced Image navigation for multiple images */}
      {images.length > 1 && (
        <div className="flex items-center justify-between text-xs text-muted-foreground bg-muted/30 rounded-lg px-3 py-2">
          <div className="flex items-center gap-2">
            <FileImage className="h-3 w-3" />
            <span className="font-medium">
              {images.length} {t("chat.imagesCount") || "images"}
            </span>
          </div>
          {remainingCount > 0 && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
              {displayImages.length} {t("chat.shown") || "shown"}
            </span>
          )}
        </div>
      )}
    </div>
  );
};
