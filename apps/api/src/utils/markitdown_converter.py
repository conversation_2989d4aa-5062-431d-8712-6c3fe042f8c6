import os
import logging
from typing import List, Dict, Any, Optional
from markitdown import MarkItDown
from langchain.schema.document import Document

logger = logging.getLogger(__name__)

class MarkitdownConverter:
    """
    A document converter that uses Microsoft's Markitdown package to convert various file formats to Markdown.
    This provides better document structure preservation and support for more file formats.
    """

    def __init__(self, enable_plugins: bool = False, llm_client=None, llm_model=None):
        """
        Initialize the Markitdown converter.
        
        Args:
            enable_plugins: Whether to enable Markitdown plugins
            llm_client: Optional LLM client for enhanced document processing (e.g., image descriptions)
            llm_model: Optional LLM model name to use with the client
        """
        self.md = MarkItDown(
            enable_plugins=enable_plugins,
            llm_client=llm_client,
            llm_model=llm_model
        )
        logger.info("Initialized Markitdown converter")

    def convert_file(self, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> List[Document]:
        """
        Convert a file to Markdown and return as LangChain Document objects.
        
        Args:
            file_path: Path to the file to convert
            metadata: Optional metadata to add to the documents
            
        Returns:
            List of LangChain Document objects
        """
        logger.info(f"Converting file {file_path} with Markitdown")
        
        try:
            # Convert the file to markdown
            result = self.md.convert(file_path)
            
            # Create base metadata
            base_metadata = metadata or {}
            base_metadata.update({
                "source": file_path,
                "file_name": os.path.basename(file_path),
                "file_type": os.path.splitext(file_path)[1].lower().replace(".", ""),
            })
            
            # Create a LangChain Document from the markdown content
            document = Document(
                page_content=result.text_content,
                metadata=base_metadata
            )
            
            logger.info(f"Successfully converted {file_path} to markdown")
            return [document]
            
        except Exception as e:
            logger.error(f"Error converting file {file_path} with Markitdown: {str(e)}")
            raise
    
    def get_supported_extensions(self) -> List[str]:
        """
        Get a list of file extensions supported by Markitdown.
        
        Returns:
            List of supported file extensions
        """
        return [
            # Document formats
            ".pdf", ".docx", ".doc", ".pptx", ".ppt", ".xlsx", ".xls",
            # Text formats
            ".txt", ".md", ".markdown", ".csv", ".json", ".xml", ".html", ".htm",
            # Audio formats (with transcription)
            ".mp3", ".wav",
            # Archive formats
            ".zip", ".epub",
            # Email formats
            ".eml", ".msg"
        ]
