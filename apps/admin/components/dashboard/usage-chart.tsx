"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps,
} from "recharts";
import { ArrowUpRight, MoreHorizontal } from "lucide-react";

interface UsageChartProps {
  title: string;
  data: {
    name: string;
    value: number;
  }[];
  dataKey: string;
  valueFormatter?: (value: number) => string;
  description?: string;
  color?: "indigo" | "blue" | "green" | "amber";
}

const CustomTooltip = ({
  active,
  payload,
  label,
  valueFormatter,
}: TooltipProps<number, string> & {
  valueFormatter: (value: number) => string;
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-background p-2 shadow-sm">
        <p className="text-xs font-medium text-muted-foreground">{label}</p>
        <p className="text-sm font-bold">
          {valueFormatter(payload[0].value as number)}
        </p>
      </div>
    );
  }

  return null;
};

export function UsageChart({
  title,
  data,
  dataKey,
  valueFormatter = (value: number) => `${value}`,
  description,
  color = "indigo",
}: UsageChartProps) {
  const colorMap = {
    indigo: {
      stroke: "#6366F1",
      fill: "#6366F1",
    },
    blue: {
      stroke: "#3B82F6",
      fill: "#3B82F6",
    },
    green: {
      stroke: "#10B981",
      fill: "#10B981",
    },
    amber: {
      stroke: "#F59E0B",
      fill: "#F59E0B",
    },
  };

  return (
    <Card className="col-span-4 overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-base font-medium">{title}</CardTitle>
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
            <ArrowUpRight className="h-4 w-4" />
            <span className="sr-only">View details</span>
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">More options</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="h-[300px] pt-4">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{
              top: 5,
              right: 5,
              left: 5,
              bottom: 5,
            }}
          >
            <defs>
              <linearGradient id={`color-${color}`} x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={colorMap[color].fill}
                  stopOpacity={0.2}
                />
                <stop
                  offset="95%"
                  stopColor={colorMap[color].fill}
                  stopOpacity={0}
                />
              </linearGradient>
            </defs>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#888"
              strokeOpacity={0.2}
            />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dy={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
              dx={-10}
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload}
                  label={label}
                  valueFormatter={valueFormatter}
                />
              )}
            />
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={colorMap[color].stroke}
              strokeWidth={2}
              fill={`url(#color-${color})`}
              fillOpacity={1}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
