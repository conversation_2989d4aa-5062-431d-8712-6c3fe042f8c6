"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useChat } from "@ai-sdk/react";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  ArrowUpIcon,
  Globe,
  ExternalLink,
  FileText,
  Sparkles,
  MessageSquare,
  Search,
  Trash2,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { createChat } from "@/services/src/chat";
import { createMessage } from "@/services/src/message";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ReactMarkdown from "react-markdown";
import { Source } from "../wrapper-screens/chat/types";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { addAuthHeaders } from "@/lib/api/auth-token";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from "framer-motion";
import { Input } from "../ui/input";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";

// API constants
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const API_VERSION = "/api/v1";

interface Message {
  id?: string;
  role: "user" | "assistant";
  content: string;
  sources?: Source[];
  timestamp: Date;
  metadata?: any;
  images?: any[];
}

interface QuickAskDialogProps {
  userId: string;
  tenantId: string;
  userName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function QuickAskDialog({
  userId,
  tenantId,
  userName,
  open,
  onOpenChange,
}: QuickAskDialogProps) {
  const router = useRouter();
  const { t } = useLanguage();
  const [input, setInput] = useState("");
  const [conversationMessages, setConversationMessages] = useState<Message[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [includeWebResults, setIncludeWebResults] = useState(false);
  const [headers, setHeaders] = useState({});
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    getHeaders();
  }, []);

  const getHeaders = async () => {
    const headers = await addAuthHeaders({
      "Content-Type": "application/json",
    });
    setHeaders(headers);
  };
  // Focus input when dialog opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        setInput("");
        setConversationMessages([]);
        setIsLoading(false);
      }, 300);
    }
  }, [open]);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [conversationMessages]);

  // Save conversation to local storage when it changes
  useEffect(() => {
    if (conversationMessages.length > 0) {
      try {
        localStorage.setItem(
          "quick_ask_conversation",
          JSON.stringify(conversationMessages)
        );
      } catch (error) {
        console.error("Error saving conversation to local storage:", error);
      }
    }
  }, [conversationMessages]);

  // Load conversation from local storage on initial load
  useEffect(() => {
    try {
      const savedConversation = localStorage.getItem("quick_ask_conversation");
      if (savedConversation && JSON.parse(savedConversation).length > 0) {
        setConversationMessages(JSON.parse(savedConversation));
      }
    } catch (error) {
      console.error("Error loading conversation from local storage:", error);
    }
  }, []);

  // Set up AI chat with proper message initialization for conversation memory
  const {
    handleInputChange,
    handleSubmit: handleAISubmit,
    status,
  } = useChat({
    api: `${API_BASE_URL}${API_VERSION}/workspace-chat/chat?current_user=${userId}&tenant_id=${tenantId}&user_name=${userName}`,
    body: {
      stream: true,
      messages: [
        ...conversationMessages.map((msg) => ({
          id: msg.id || Math.random().toString(),
          role: msg.role,
          content: msg.content,
          createdAt: new Date(),
        })),
        {
          id: Math.random().toString(),
          role: "user",
          content: input,
          createdAt: new Date(),
        },
      ],
      config: {
        includeWebResults: includeWebResults === true,
      },
    },
    headers,
    onResponse: async (response) => {
      // Try to extract sources from the response headers if available

      setInput("");
      if (!response.ok) {
        console.error("API response error:", response.statusText);
        return;
      }

      // Set up streaming response handling
      const reader = response.body?.getReader();
      let assistantMessage = "";
      let sources: Source[] = [];

      if (reader) {
        try {
          // Read the first chunk to check for sources
          const { value } = await reader.read();
          const firstChunk = new TextDecoder().decode(value);

          try {
            // Try to parse the first chunk as JSON to extract sources
            const parsedChunk = JSON.parse(firstChunk);

            // Check if this is the first chunk with sources
            if (parsedChunk.is_first_chunk && parsedChunk.sources) {
              sources = parsedChunk.sources;
              assistantMessage = parsedChunk.answer || "";
            } else if (parsedChunk.sources) {
              // Standard format fallback
              sources = parsedChunk.sources;
              assistantMessage = parsedChunk.answer || "";
            } else {
              // Not a sources chunk, treat as normal text
              assistantMessage = firstChunk;
            }
          } catch (e) {
            // If not JSON, use the chunk as plain text
            assistantMessage = firstChunk;
          }

          // Add assistant message to conversation
          const newAssistantMessage: Message = {
            role: "assistant",
            content: assistantMessage,
            timestamp: new Date(),
            sources: sources.length > 0 ? sources : undefined,
          };

          // Update conversation with initial assistant message
          setConversationMessages((prev) => {
            // Check if we already have an assistant message (avoid duplicates)
            const lastMessage = prev[prev.length - 1];
            if (lastMessage && lastMessage.role === "assistant") {
              // Update the existing assistant message
              return prev.map((msg, index) =>
                index === prev.length - 1
                  ? {
                      ...msg,
                      content: assistantMessage,
                      sources: sources.length > 0 ? sources : undefined,
                    }
                  : msg
              );
            } else {
              // Add a new assistant message
              return [...prev, newAssistantMessage];
            }
          });

          // Continue reading the stream
          let streamDone = false;
          while (!streamDone) {
            const { done, value } = await reader.read();
            if (done) {
              streamDone = true;
              break;
            }

            // Convert the chunk to text and accumulate
            const chunk = new TextDecoder().decode(value);

            // Try to parse the chunk as JSON to extract sources
            try {
              const parsedChunk = JSON.parse(chunk);
              if (parsedChunk.sources) {
                // If this is a sources chunk, store the sources
                sources = parsedChunk.sources;
                // Skip adding this chunk to the message content
                continue;
              }
            } catch (e) {
              // Not JSON, treat as normal text
            }

            assistantMessage += chunk;

            // Update the assistant message in the conversation
            setConversationMessages((prev) => {
              // Find the last assistant message and update it
              return prev.map((msg, index) => {
                if (index === prev.length - 1 && msg.role === "assistant") {
                  return {
                    ...msg,
                    content: assistantMessage,
                    sources: sources.length > 0 ? sources : undefined,
                  };
                }
                return msg;
              });
            });
          }
        } catch (error) {
          console.error("Error reading stream:", error);
        } finally {
          reader.releaseLock();
        }
      }
    },
    onFinish: () => {
      setIsLoading(false);
    },
  });

  // Update loading state based on status
  useEffect(() => {
    if (status === "submitted") {
      setIsLoading(true);
    } else if (status === "ready" || status === "error") {
      setIsLoading(false);
    }
    // Don't set isLoading to false during streaming, as we want to show the loading indicator
    // in the send button, but we'll show the streaming content in the message
  }, [status]);

  const handleChange = (value: string) => {
    handleInputChange({
      target: { value },
    } as React.ChangeEvent<HTMLInputElement>);
    setInput(value);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (input.trim() === "" || isLoading || status === "streaming") return;

    // Add user message to conversation
    const userMessage: Message = {
      role: "user",
      content: input,
      timestamp: new Date(),
    };

    // Store the current input before clearing it
    const currentInput = input;

    // Update conversation with user message and clear input
    setConversationMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Just use the input directly with handleInputChange and then submit
      handleInputChange({
        target: { value: currentInput },
      } as React.ChangeEvent<HTMLInputElement>);

      // Then submit the form
      handleAISubmit(e);
    } catch (error) {
      console.error("Error submitting question:", error);
      setIsLoading(false);
    }
  };

  // Function to clear conversation history
  const handleClearConversation = () => {
    setConversationMessages([]);
    localStorage.removeItem("quick_ask_conversation");
  };

  const handleOpenAsChat = async () => {
    if (conversationMessages.length === 0) return;

    try {
      // Get the first user message for the title
      const firstUserMessage = conversationMessages.find(
        (msg) => msg.role === "user"
      );
      const title = firstUserMessage
        ? firstUserMessage.content.substring(0, 50) +
          (firstUserMessage.content.length > 50 ? "..." : "")
        : "Quick Ask Conversation";

      // Create a new chat
      const chatResponse = await createChat({
        title,
        tenantId,
      });

      if (chatResponse?.chat?.id) {
        // Add all messages to the chat with proper metadata including images
        for (const message of conversationMessages) {
          await createMessage({
            chatId: chatResponse.chat.id,
            content: message.content,
            role: message.role,
            metadata: {
              ...(message.role === "user" ? { includeWebResults } : {}),
              ...(message.metadata || {}),
              // Preserve any images that might be in the message
              images: message.images || message.metadata?.images || undefined,
              hasImages: !!(message.images || message.metadata?.images),
            },
            sources: message.sources || [],
          });
        }

        // Navigate to the new chat
        router.push(`/ask-ai/${chatResponse.chat.id}`);
        onOpenChange(false);
      }
    } catch (error) {
      console.error("Error creating chat:", error);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        style={{
          zIndex: 10000,
        }}
        side="right"
        className="w-full sm:max-w-md p-0 border-l border-border/30 shadow-xl"
      >
        <div className="flex flex-col h-full">
          {/* Header with gradient */}
          <SheetHeader className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent px-4 py-3 border-b border-border/30">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              <SheetTitle className="text-lg font-semibold text-foreground">
                {t("quickAsk.buttonLabel") || "Quick Ask"}
              </SheetTitle>
            </div>
          </SheetHeader>

          {/* Web search toggle with enhanced styling */}
          <div className="flex items-center px-4 py-2 border-b border-border/30 bg-muted/20">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="web-search-quick"
                      checked={includeWebResults}
                      onCheckedChange={setIncludeWebResults}
                      className="data-[state=checked]:bg-primary"
                    />
                    <Label
                      htmlFor="web-search-quick"
                      className="flex items-center gap-1.5 text-xs cursor-pointer font-medium"
                    >
                      <Globe className="h-3.5 w-3.5 text-primary/80" />
                      {t("chat.includeWebResults") || "Include Web Results"}
                    </Label>
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="bg-popover/95 backdrop-blur-sm border-border/30"
                >
                  {t("chat.webSearchTooltip") ||
                    "Search the web for more up-to-date information"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Messages area */}
          <ScrollArea className="flex-1 p-4 bg-background/80">
            <AnimatePresence>
              {conversationMessages.length === 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex flex-col items-center justify-center py-10 text-center"
                >
                  <MessageSquare className="h-12 w-12 text-muted-foreground/30 mb-4" />
                  <p className="text-muted-foreground font-medium">
                    {t("quickAsk.emptyState") ||
                      "Ask a question to get a quick answer"}
                  </p>
                  <p className="text-xs text-muted-foreground/70 mt-2 max-w-md">
                    Get instant answers without saving the conversation to your
                    workspace
                  </p>
                </motion.div>
              )}

              {/* Conversation messages */}
              <div className="space-y-4">
                {conversationMessages.map((message, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`flex ${
                      message.role === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`rounded-2xl px-4 py-2.5 text-sm max-w-[85%] shadow-sm ${
                        message.role === "user"
                          ? "bg-primary/10 rounded-tr-sm"
                          : "bg-card rounded-tl-sm"
                      }`}
                    >
                      {message.role === "assistant" ? (
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          <ReactMarkdown>{message.content}</ReactMarkdown>
                          {status === "streaming" &&
                            index === conversationMessages.length - 1 && (
                              <span className="inline-block w-2 h-4 ml-1 bg-primary/70 animate-pulse rounded-sm"></span>
                            )}
                        </div>
                      ) : (
                        <div>{message.content}</div>
                      )}

                      {/* Sources for assistant messages */}
                      {message.role === "assistant" &&
                        message.sources &&
                        message.sources.length > 0 && (
                          <div className="mt-4 pt-3 border-t border-border/30">
                            <Accordion
                              type="single"
                              collapsible
                              className="w-full border-none"
                            >
                              <AccordionItem
                                value={`citations-${index}`}
                                className="border-none"
                              >
                                <AccordionTrigger className="py-1.5 px-2 hover:no-underline hover:bg-muted/50 rounded-lg group transition-colors">
                                  <div className="flex justify-between items-center gap-2 w-full">
                                    <div className="flex items-center gap-2">
                                      <div className="bg-primary/10 p-1.5 rounded-full">
                                        <FileText className="h-3.5 w-3.5 text-primary" />
                                      </div>
                                      <div className="text-xs font-medium text-primary">
                                        {t("chat.sources") || "Sources"}
                                        <Badge
                                          variant="outline"
                                          className="ml-2 bg-primary/5 text-[10px] font-normal py-0 h-4"
                                        >
                                          {message.sources.length}
                                        </Badge>
                                      </div>
                                    </div>
                                    <div className="text-[10px] text-muted-foreground mr-1 opacity-70 group-hover:opacity-100">
                                      {t("chat.citationsAccordion") ||
                                        "Click to view sources"}
                                    </div>
                                  </div>
                                </AccordionTrigger>
                                <AccordionContent className="pt-2 pb-1 animate-in fade-in-50 duration-300">
                                  <div className="grid grid-cols-1 gap-2">
                                    {message.sources.map((source, idx) => (
                                      <motion.div
                                        key={idx}
                                        onClick={() => {
                                          // Open the web link in a new tab
                                          window.open(
                                            source.metadata?.link ?? "#",
                                            "_blank",
                                            "noopener,noreferrer"
                                          );
                                        }}
                                        initial={{ opacity: 0, y: 5 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: idx * 0.05 }}
                                        className="group relative p-3 rounded-lg border border-border/50
                                              bg-muted/30 hover:bg-muted/50
                                              transition-all duration-200 cursor-pointer hover:shadow-md"
                                      >
                                        <div className="flex items-start gap-3">
                                          <div className="mt-0.5 flex-shrink-0 bg-primary/10 p-1.5 rounded-full">
                                            <FileText className="h-3.5 w-3.5 text-primary/80" />
                                          </div>
                                          <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                              <div className="text-xs font-medium truncate">
                                                {source.metadata?.fileName ||
                                                  "Document"}
                                              </div>
                                            </div>
                                            <div className="mt-1 text-[11px] text-muted-foreground line-clamp-2">
                                              {source.metadata?.relevantText
                                                ? source.metadata.relevantText.substring(
                                                    0,
                                                    100
                                                  ) + "..."
                                                : source.content.substring(
                                                    0,
                                                    100
                                                  ) + "..."}
                                            </div>
                                          </div>
                                        </div>
                                      </motion.div>
                                    ))}
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          </div>
                        )}
                    </div>
                  </motion.div>
                ))}

                {/* Loading indicator - only show when submitted but not yet streaming */}
                {isLoading &&
                  status === "submitted" &&
                  conversationMessages.length > 0 &&
                  conversationMessages[conversationMessages.length - 1].role ===
                    "user" && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center gap-3 p-4 bg-muted/30 rounded-2xl rounded-tl-sm max-w-[85%]"
                    >
                      <div className="bg-primary/20 p-2 rounded-full">
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">
                          {t("quickAsk.thinking") || "Thinking..."}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Searching for the best answer
                        </p>
                      </div>
                    </motion.div>
                  )}

                {/* Invisible element for scrolling to bottom */}
                <div ref={messagesEndRef} />
              </div>
            </AnimatePresence>
          </ScrollArea>

          {/* Input area with enhanced styling */}
          <div className="border-t border-border/30 p-4 bg-background/90">
            <form onSubmit={handleSubmit} className="flex items-center gap-2">
              <div className="relative flex-1">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <Search className="h-4 w-4 text-muted-foreground" />
                </div>
                <Input
                  ref={inputRef}
                  value={input}
                  onChange={(e) => handleChange(e.target.value)}
                  placeholder={
                    t("quickAsk.placeholder") || "Ask a quick question..."
                  }
                  disabled={isLoading || status === "streaming"}
                  className="h-10 pl-9 rounded-full bg-muted/50 border-none focus-visible:ring-1 focus-visible:ring-primary/30 transition-all"
                />
              </div>

              {input.trim() !== "" && !isLoading && status !== "streaming" ? (
                <Button
                  type="submit"
                  size="icon"
                  variant="ghost"
                  className="h-9 w-9 rounded-full hover:bg-primary/10 transition-colors"
                >
                  <ArrowUpIcon className="h-4 w-4 text-primary" />
                </Button>
              ) : (
                (isLoading || status === "streaming") && (
                  <div className="h-9 w-9 flex items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin text-primary" />
                  </div>
                )
              )}
            </form>

            {conversationMessages.length > 0 && (
              <div className="mt-3 flex justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-1.5 rounded-full hover:bg-destructive/10 hover:text-destructive transition-colors"
                  onClick={handleClearConversation}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                  {t("quickAsk.clearConversation") || "Clear Conversation"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1.5 rounded-full hover:bg-primary/10 hover:text-primary transition-colors border-primary/20"
                  onClick={handleOpenAsChat}
                >
                  <ExternalLink className="h-3.5 w-3.5" />
                  {t("quickAsk.openAsChat") || "Open as Chat"}
                </Button>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
