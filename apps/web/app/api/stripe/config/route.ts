import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Return the Stripe publishable key from environment variables
    return NextResponse.json({
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
    });
  } catch (error) {
    console.error("Error fetching Stripe config:", error);
    return NextResponse.json(
      { error: "Failed to fetch Stripe config" },
      { status: 500 }
    );
  }
}
